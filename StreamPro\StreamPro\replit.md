# StreamEngine - Live Streaming Platform

## Overview

StreamEngine is a modern real-time streaming platform built with React, Express, and WebRTC. It provides a complete streaming solution with live video/audio broadcasting, real-time chat, viewer analytics, and comprehensive stream management. The application uses a full-stack architecture with PostgreSQL for data persistence and WebSocket connections for real-time features.

## System Architecture

### Frontend Architecture
- **React 18** with TypeScript for the client-side application
- **Vite** as the build tool and development server
- **TanStack Query** for server state management and API caching
- **Wouter** for client-side routing
- **Tailwind CSS** with **shadcn/ui** for styling and component library
- **WebRTC** for peer-to-peer video/audio streaming
- **WebSocket** for real-time communication (chat, viewer counts, notifications)

### Backend Architecture
- **Express.js** with TypeScript for the REST API server
- **Node.js** runtime with ES modules
- **WebSocket Server** for real-time bidirectional communication
- **Drizzle ORM** for database operations and schema management
- **PostgreSQL** as the primary database (configured for Neon serverless)

### Real-time Communication
- **WebRTC** for direct peer-to-peer video/audio streaming
- **WebSocket** connections for chat messages, viewer updates, and system notifications
- **STUN servers** for NAT traversal in WebRTC connections

## Key Components

### Stream Management
- **Stream Creation/Deletion**: Full CRUD operations for stream entities
- **Stream States**: Inactive, live, scheduled, and ended status management
- **Stream Quality Settings**: Configurable bitrate, resolution, and codec options
- **Recording Support**: Optional stream recording with URL storage

### Real-time Features
- **Live Chat**: Real-time messaging system with user authentication
- **Viewer Tracking**: Active viewer counting and session management
- **Analytics**: Stream performance metrics and viewer statistics
- **WebRTC Streaming**: Direct browser-to-browser video/audio transmission

### User Interface
- **Dashboard**: Main interface showing active streams and analytics
- **Stream Player**: Video player with quality controls and full-screen support
- **Chat Interface**: Real-time chat with message history
- **Stream Controls**: Broadcasting controls for streamers (start/stop/record)
- **Analytics Dashboard**: Performance metrics and viewer insights

## Data Flow

### Stream Broadcasting Flow
1. User creates a stream through the dashboard
2. Stream key is generated and stored in the database
3. WebRTC connection is established for video/audio capture
4. Stream status is broadcast to all connected clients via WebSocket
5. Viewers can join the stream and receive the WebRTC feed
6. Real-time analytics are collected and stored

### Chat System Flow
1. User joins a stream and establishes WebSocket connection
2. Chat messages are sent through WebSocket to the server
3. Messages are validated, stored in database, and broadcast to all viewers
4. Chat history is retrieved from database when users join

### Viewer Management Flow
1. Viewer joins stream via WebSocket with stream ID
2. Viewer count is incremented and stored in database
3. Updated viewer count is broadcast to all connected clients
4. When viewer leaves, count is decremented and database updated

## External Dependencies

### Database
- **Neon PostgreSQL**: Serverless PostgreSQL database
- **Drizzle ORM**: Type-safe database operations
- **Connection Pooling**: Managed through Neon's connection pooling

### WebRTC Infrastructure
- **STUN Servers**: Google's public STUN servers for NAT traversal
- **Browser WebRTC APIs**: Native browser support for peer-to-peer connections

### UI Framework
- **Radix UI**: Accessible component primitives
- **shadcn/ui**: Pre-built component library
- **Tailwind CSS**: Utility-first styling framework
- **Lucide React**: Icon library

## Deployment Strategy

### Development Environment
- **Vite Dev Server**: Hot module replacement and fast development builds
- **Express Server**: Concurrent development server for API endpoints
- **WebSocket Server**: Integrated with Express for real-time features

### Production Build
- **Vite Build**: Optimized client-side bundle with code splitting
- **esbuild**: Server-side bundling for Node.js deployment
- **Static Assets**: Client assets served from Express server
- **Database Migrations**: Drizzle migrations for schema updates

### Environment Configuration
- **DATABASE_URL**: PostgreSQL connection string (required)
- **NODE_ENV**: Environment mode (development/production)
- **WebSocket Protocol**: Automatic HTTP/HTTPS and WS/WSS detection

## Changelog

Changelog:
- July 06, 2025. Initial setup

## User Preferences

Preferred communication style: Simple, everyday language.
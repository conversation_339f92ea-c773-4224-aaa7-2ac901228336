import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { 
  Search, 
  Filter, 
  Eye, 
  Edit, 
  Trash2, 
  Play,
  Square,
  Video,
  Clock,
  Users
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";

interface StreamTableProps {
  streams: any[];
  isLoading: boolean;
  onStreamSelect?: (streamId: number) => void;
}

export function StreamTable({ streams, isLoading, onStreamSelect }: StreamTableProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const deleteStreamMutation = useMutation({
    mutationFn: async (streamId: number) => {
      return await apiRequest('DELETE', `/api/streams/${streamId}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/streams'] });
      toast({
        title: "Stream Deleted",
        description: "The stream has been deleted successfully.",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  const updateStreamMutation = useMutation({
    mutationFn: async ({ streamId, updates }: { streamId: number; updates: any }) => {
      return await apiRequest('PUT', `/api/streams/${streamId}`, updates);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/streams'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  const filteredStreams = streams.filter(stream =>
    stream.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
    stream.streamKey.toLowerCase().includes(searchTerm.toLowerCase())
  );
  
  const handleStreamToggle = (stream: any) => {
    const newStatus = stream.status === 'live' ? 'inactive' : 'live';
    updateStreamMutation.mutate({
      streamId: stream.id,
      updates: { 
        status: newStatus,
        [newStatus === 'live' ? 'startedAt' : 'endedAt']: new Date().toISOString()
      }
    });
  };
  
  const handleDelete = (streamId: number) => {
    if (window.confirm('Are you sure you want to delete this stream?')) {
      deleteStreamMutation.mutate(streamId);
    }
  };
  
  const getStatusBadge = (status: string) => {
    const variants = {
      live: 'default',
      inactive: 'secondary',
      scheduled: 'outline',
      ended: 'secondary'
    };
    
    return (
      <Badge variant={variants[status as keyof typeof variants] || 'secondary'}>
        {status === 'live' && <div className="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse" />}
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    );
  };
  
  const formatDuration = (startedAt: string, endedAt?: string) => {
    if (!startedAt) return '-';
    
    const start = new Date(startedAt);
    const end = endedAt ? new Date(endedAt) : new Date();
    const diff = Math.floor((end.getTime() - start.getTime()) / 1000);
    
    const hours = Math.floor(diff / 3600);
    const minutes = Math.floor((diff % 3600) / 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Active Streams</CardTitle>
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search streams..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
            <Button variant="outline" size="sm">
              <Filter className="w-4 h-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : filteredStreams.length === 0 ? (
          <div className="text-center py-8">
            <Video className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
            <h3 className="text-lg font-semibold mb-2">No Streams Found</h3>
            <p className="text-muted-foreground">
              {searchTerm ? 'No streams match your search criteria.' : 'Create your first stream to get started.'}
            </p>
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Stream</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Viewers</TableHead>
                <TableHead>Quality</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredStreams.map((stream) => (
                <TableRow key={stream.id} className="hover:bg-muted/50">
                  <TableCell>
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-8 bg-muted rounded flex items-center justify-center">
                        <Video className="w-4 h-4 text-muted-foreground" />
                      </div>
                      <div>
                        <div className="font-medium">{stream.title}</div>
                        <div className="text-sm text-muted-foreground">
                          {stream.streamKey}
                        </div>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(stream.status)}
                  </TableCell>
                  <TableCell className="text-sm">
                    {stream.viewerCount || 0}
                  </TableCell>
                  <TableCell className="text-sm">
                    {stream.quality || '720p'}
                  </TableCell>
                  <TableCell className="text-sm">
                    {stream.status === 'live' ? 
                      formatDuration(stream.startedAt) : 
                      stream.status === 'scheduled' ? 
                        `Starts ${new Date(stream.scheduledAt).toLocaleDateString()}` :
                        formatDuration(stream.startedAt, stream.endedAt)
                    }
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center space-x-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => onStreamSelect?.(stream.id)}
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                      <Link href={`/stream/${stream.id}`}>
                        <Button variant="ghost" size="sm">
                          <Edit className="w-4 h-4" />
                        </Button>
                      </Link>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleStreamToggle(stream)}
                        disabled={updateStreamMutation.isPending}
                      >
                        {stream.status === 'live' ? (
                          <Square className="w-4 h-4" />
                        ) : (
                          <Play className="w-4 h-4" />
                        )}
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDelete(stream.id)}
                        disabled={deleteStreamMutation.isPending}
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        )}
      </CardContent>
    </Card>
  );
}

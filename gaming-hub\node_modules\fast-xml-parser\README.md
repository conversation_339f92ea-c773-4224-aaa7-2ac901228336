# [fast-xml-parser](https://www.npmjs.com/package/fast-xml-parser)
[![NPM quality][quality-image]][quality-url]
[![Coverage Status](https://coveralls.io/repos/github/NaturalIntelligence/fast-xml-parser/badge.svg?branch=master)](https://coveralls.io/github/NaturalIntelligence/fast-xml-parser?branch=master)
[<img src="https://img.shields.io/badge/Try-me-blue.svg?colorA=FFA500&colorB=0000FF" alt="Try me"/>](https://naturalintelligence.github.io/fast-xml-parser/)
[![NPM total downloads](https://img.shields.io/npm/dt/fast-xml-parser.svg)](https://npm.im/fast-xml-parser)


Validate XML, Parse XML to JS Object, or Build XML from JS Object without C/C++ based libraries and no callback.

---

<small>Checkout our new library [Text2Chart](https://solothought.com/text2chart/flow) that constructs flow chart out of simple text. Very helpful in creating or alayzing an algorithm, and documentation purpose</small>
<br>

Sponsor this project

<a href="https://github.com/sponsors/NaturalIntelligence"> 
  <img src="https://raw.githubusercontent.com/NaturalIntelligence/ThankYouBackers/main/github_sponsor.png" width="180" />
</a>
<a href="https://opencollective.com/fast-xml-parser/donate" target="_blank">
  <img src="https://opencollective.com/fast-xml-parser/donate/<EMAIL>?color=blue" width=180 />
</a>
<a href="https://paypal.me/naturalintelligence"> <img src="static/img/support_paypal.svg" alt="donate button" width="180"/></a>
<br>
<br>
<br>

<!--
### Current Sponsors

Check the complete list at [ThankYouBackers](https://github.com/NaturalIntelligence/ThankYouBackers) for our sponsors and supporters.

Through Github

<a href="https://github.com/skunkteam" target="_blank"><img src="https://avatars.githubusercontent.com/u/46373671?s=60" width="60px"></a>
<a href="https://github.com/getsentry" target="_blank"><img src="https://avatars.githubusercontent.com/u/1396951?s=60" width="60px"></a>

Through OpenCollective

<a href="https://opencollective.com/fast-xml-parser/sponsor/0/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/0/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/1/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/1/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/2/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/2/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/3/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/3/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/4/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/4/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/5/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/5/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/6/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/6/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/7/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/7/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/8/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/8/avatar.svg"></a>
<a href="https://opencollective.com/fast-xml-parser/sponsor/9/website" target="_blank"><img src="https://opencollective.com/fast-xml-parser/sponsor/9/avatar.svg"></a>
-->

![fxp_sponsors](https://raw.githubusercontent.com/NaturalIntelligence/ThankYouBackers/main/assets/NI_sponsors.jpg)
- <a href="https://github.com/cocopon" target="_blank">Hiroki Kokubun</a>

> This is a donation. No goods or services are expected in return. Any requests for refunds for those purposes will be rejected.
## Users

<a href="https://github.com/renovatebot/renovate" title="renovate" ><img src="https://avatars1.githubusercontent.com/u/38656520" width="60px" ></a>
<a href="https://vmware.com/" title="vmware" > <img src="https://avatars0.githubusercontent.com/u/473334" width="60px" ></a>
<a href="https://opensource.microsoft.com/" title="microsoft" > <img src="https://avatars0.githubusercontent.com/u/6154722" width="60px" ></a>
<a href="http://ibm.github.io/" title="IBM" > <img src="https://avatars2.githubusercontent.com/u/1459110" width="60px" ></a>
<a href="http://www.smartbear.com" title="SmartBear Software" > <img src="https://avatars2.githubusercontent.com/u/1644671" width="60px" ></a>
<a href="http://nasa.github.io/" title="NASA" > <img src="https://avatars0.githubusercontent.com/u/848102" width="60px" ></a>
<a href="https://github.com/prettier" title="Prettier" > <img src="https://avatars0.githubusercontent.com/u/25822731" width="60px" ></a>
<a href="http://brain.js.org/" title="brain.js" > <img src="https://avatars2.githubusercontent.com/u/23732838" width="60px" ></a>
<a href="https://github.com/aws" title="AWS SDK" > <img src="https://avatars.githubusercontent.com/u/2232217" width="60px" ></a>
<a href="http://www.fda.gov/" title="Food and Drug Administration " > <img src="https://avatars2.githubusercontent.com/u/6471964" width="60px" ></a>
<a href="http://www.magento.com/" title="Magento" > <img src="https://avatars2.githubusercontent.com/u/168457" width="60px" ></a>
<a href="https://github.com/SAP" title="SAP" > <img src="https://user-images.githubusercontent.com/7692328/204835214-d9d25b58-e3df-408d-87a3-c7d36b578ee4.png" width="60px" ></a>
<a href="https://github.com/postmanlabs" title="postman" > <img src="https://user-images.githubusercontent.com/7692328/204835529-e9e290ad-696a-49ad-9d34-08e955704715.png" width="60px" ></a>
<a href="https://github.com/react-native-community" title="React Native Community" > <img src="https://avatars.githubusercontent.com/u/20269980?v=4" width="60px" ></a>
<a href="https://github.com/googleapis" title="Google APIs" > <img src="https://avatars.githubusercontent.com/u/16785467?v=4" width="60px" ></a>
<a href="https://github.com/langchain-ai" title="Langchain AI" > <img src="https://avatars.githubusercontent.com/u/126733545?v=4" width="60px" ></a>
<a href="https://github.com/withastro" title="Astro websie builder" > <img src="https://avatars.githubusercontent.com/u/44914786?v=4" width="60px" ></a>
<a href="https://github.com/baidu" title="Baidu" > <img src="https://avatars.githubusercontent.com/u/13245940?v=4" width="60px" ></a>
[more](./USERs.md)

<small>The list of users are mostly published by Github or communicated directly. Feel free to contact if you find any information wrong.</small>

---

## Main Features

<img align="right" src="static/img/fxp_logo.png" width="180px" alt="FXP logo"/>

* Validate XML data syntactically
* Parse XML to JS Object
* Build XML from JS Object
* Compatible to node js packages, in browser, and in CLI (click try me button above for demo)
* Faster than any other pure JS implementation.
* It can handle big files (tested up to 100mb).
* Controlled parsing using various options
* XML Entities, HTML entities, and DOCTYPE entites are supported.
* unpaired tags (Eg `<br>` in HTML), stop nodes (Eg `<script>` in HTML) are supported.
* You can restore almost same XML from JSON
* Supports comments
* It can preserve Order of tags in JS object
* You can control if a single tag should be parsed into array.
* Supports parsing of PI (Processing Instruction) tags with XML declaration tags
* And many more other features.

## v5
I developed v5 in Apr 2023. And I didn't get the chance to complete all the features. I've ensured that new features don't impact performance. With v5, you have more control on parsing output. Check [docs](./docs/v5) for syntax help and basic understanding.

Please leave a comment in discussion forum for your suggestions and if you really need v5.

## How to use

To use as package dependency
`$ npm install fast-xml-parser` 
or 
`$ yarn add fast-xml-parser`

To use as system command
`$ npm install fast-xml-parser -g` 

To use it on a **webpage** include it from a [CDN](https://cdnjs.com/libraries/fast-xml-parser)

**Example**

As CLI command
```bash
$ fxparser some.xml
```

In a node js project
```js
const { XMLParser, XMLBuilder, XMLValidator} = require("fast-xml-parser");

const parser = new XMLParser();
let jObj = parser.parse(XMLdata);

const builder = new XMLBuilder();
const xmlContent = builder.build(jObj);
```

In a HTML page
```html
<script src="path/to/fxp.min.js"></script>
:
<script>
  const parser = new fxparser.XMLParser();
  parser.parse(xmlContent);
</script>
```

Bundle size

| Bundle Name        | Size |
| ------------------ | ---- |
| fxbuilder.min.js   | 6.5K |
| fxparser.min.js    | 20K  |
| fxp.min.js         | 26K  |
| fxvalidator.min.js | 5.7K |

### Documents
<table>
  <tr><td>v3</td><td>v4</td><td>v5</td></tr>
  <tr>
    <td>
      <a href="./docs/v3/docs.md">documents</a>
    </td>
    <td><ol>
      <li><a href="./docs/v4/1.GettingStarted.md">Getting Started</a></li>
      <li><a href="./docs/v4/2.XMLparseOptions.md">XML Parser</a></li>
      <li><a href="./docs/v4/3.XMLBuilder.md">XML Builder</a></li>
      <li><a href="./docs/v4/4.XMLValidator.md">XML Validator</a></li>
      <li><a href="./docs/v4/5.Entities.md">Entities</a></li>
      <li><a href="./docs/v4/6.HTMLParsing.md">HTML Document Parsing</a></li>
      <li><a href="./docs/v4/7.PITags.md">PI Tag processing</a></li>
    </ol></td>
    <td><ol>
      <li></li><a href="./docs/v5/1.GettingStarted.md">Getting Started</a></li>
      <li><a href="./docs/v5/2.Features.md">Features</a></li>
      <li><a href="./docs/v5/3.Options.md">Options</a></li>
      <li><a href="./docs/v5/4.OutputBuilders.md">Output Builders</a></li>
      <li><a href="./docs/v5/5.ValueParsers.md">Value Parsers</a></li>
    </ol></td>
  </tr>
</table>

**note**: version 5 is released with version 4 tfor experimental use. Based on it's demand, it'll be developed and the features can be different in final release.

## Performance
<small>negative means error</small>

### XML Parser

<img align="left" src="./docs/imgs/XMLParser_v4.png" width="45%" />
<img src="./docs/imgs/XMLParser_large_v4.png" width="47%" />

* Y-axis: requests per second
* X-axis: File size

### XML Builder

<img src="./docs/imgs/XMLBuilder_v4.png" width="50%" />
* Y-axis: requests per second



<!-- [![](static/img/ni_ads_ads.gif)](https://github.com/NaturalIntelligence/ads/) -->


## Usage Trend

[Usage Trend of fast-xml-parser](https://npm-compare.com/fast-xml-parser#timeRange=THREE_YEARS)
  
<a href="https://npm-compare.com/fast-xml-parser#timeRange=THREE_YEARS" target="_blank">
  <img src="https://npm-compare.com/img/npm-trend/THREE_YEARS/fast-xml-parser.png" width="50%" alt="NPM Usage Trend of fast-xml-parser" />
</a>

## Supporters
### Contributors

This project exists thanks to [all](graphs/contributors) the people who contribute. [[Contribute](docs/CONTRIBUTING.md)].
<!-- <a href="graphs/contributors"><img src="https://opencollective.com/fast-xml-parser/contributors.svg?width=890&button=false" /></a> -->
<!--
### Lead Maintainers
![Amit Gupta](https://avatars1.githubusercontent.com/u/7692328?s=100&v=4)
[![Vohmyanin Sergey Vasilevich](https://avatars3.githubusercontent.com/u/783335?s=100&v=4)](https://github.com/Delagen)

### All Contributors -->
<a href="graphs/contributors"><img src="https://opencollective.com/fast-xml-parser/contributors.svg?width=890&button=false" /></a>

### Backers

Thank you to all our backers! 🙏 [[Become a backer](https://opencollective.com/fast-xml-parser#backer)]

<a href="https://opencollective.com/fast-xml-parser#backers" target="_blank"><img src="https://opencollective.com/fast-xml-parser/backers.svg?width=890"></a>



# License
* MIT License

![Donate $5](static/img/donation_quote.png)

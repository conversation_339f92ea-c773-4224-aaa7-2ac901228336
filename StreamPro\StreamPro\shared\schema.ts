import { pgTable, text, serial, integer, boolean, timestamp, jsonb, varchar, uuid } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").notNull().default("user"),
  firstName: text("first_name"),
  lastName: text("last_name"),
  profileImageUrl: text("profile_image_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const streams = pgTable("streams", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  title: text("title").notNull(),
  description: text("description"),
  streamKey: text("stream_key").notNull().unique(),
  status: text("status").notNull().default("inactive"), // inactive, live, scheduled, ended
  quality: text("quality").notNull().default("720p"),
  bitrate: integer("bitrate").default(2500),
  protocol: text("protocol").notNull().default("webrtc"),
  isRecording: boolean("is_recording").default(false),
  recordingUrl: text("recording_url"),
  thumbnailUrl: text("thumbnail_url"),
  viewerCount: integer("viewer_count").default(0),
  maxViewers: integer("max_viewers").default(0),
  startedAt: timestamp("started_at"),
  endedAt: timestamp("ended_at"),
  scheduledAt: timestamp("scheduled_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const streamViewers = pgTable("stream_viewers", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  userId: integer("user_id").references(() => users.id),
  sessionId: text("session_id").notNull(),
  joinedAt: timestamp("joined_at").defaultNow(),
  leftAt: timestamp("left_at"),
  isActive: boolean("is_active").default(true),
});

export const chatMessages = pgTable("chat_messages", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  message: text("message").notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  type: text("type").notNull().default("message"), // message, system, moderator
});

export const streamAnalytics = pgTable("stream_analytics", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  viewerCount: integer("viewer_count").default(0),
  bitrate: integer("bitrate").default(0),
  latency: integer("latency").default(0),
  cpuUsage: integer("cpu_usage").default(0),
  memoryUsage: integer("memory_usage").default(0),
  bandwidth: integer("bandwidth").default(0),
});

export const webrtcConnections = pgTable("webrtc_connections", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  userId: integer("user_id").references(() => users.id),
  peerId: text("peer_id").notNull(),
  connectionType: text("connection_type").notNull(), // publisher, subscriber
  status: text("status").notNull().default("connecting"), // connecting, connected, disconnected, failed
  iceConnectionState: text("ice_connection_state"),
  connectionState: text("connection_state"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Relations
export const usersRelations = relations(users, ({ many }) => ({
  streams: many(streams),
  streamViewers: many(streamViewers),
  chatMessages: many(chatMessages),
  webrtcConnections: many(webrtcConnections),
}));

export const streamsRelations = relations(streams, ({ one, many }) => ({
  user: one(users, { fields: [streams.userId], references: [users.id] }),
  viewers: many(streamViewers),
  chatMessages: many(chatMessages),
  analytics: many(streamAnalytics),
  webrtcConnections: many(webrtcConnections),
}));

export const streamViewersRelations = relations(streamViewers, ({ one }) => ({
  stream: one(streams, { fields: [streamViewers.streamId], references: [streams.id] }),
  user: one(users, { fields: [streamViewers.userId], references: [users.id] }),
}));

export const chatMessagesRelations = relations(chatMessages, ({ one }) => ({
  stream: one(streams, { fields: [chatMessages.streamId], references: [streams.id] }),
  user: one(users, { fields: [chatMessages.userId], references: [users.id] }),
}));

export const streamAnalyticsRelations = relations(streamAnalytics, ({ one }) => ({
  stream: one(streams, { fields: [streamAnalytics.streamId], references: [streams.id] }),
}));

export const webrtcConnectionsRelations = relations(webrtcConnections, ({ one }) => ({
  stream: one(streams, { fields: [webrtcConnections.streamId], references: [streams.id] }),
  user: one(users, { fields: [webrtcConnections.userId], references: [users.id] }),
}));

// Insert schemas
export const insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertStreamSchema = createInsertSchema(streams).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

export const insertChatMessageSchema = createInsertSchema(chatMessages).omit({
  id: true,
  timestamp: true,
});

export const insertStreamAnalyticsSchema = createInsertSchema(streamAnalytics).omit({
  id: true,
  timestamp: true,
});

export const insertWebrtcConnectionSchema = createInsertSchema(webrtcConnections).omit({
  id: true,
  createdAt: true,
  updatedAt: true,
});

// Types
export type User = typeof users.$inferSelect;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type Stream = typeof streams.$inferSelect;
export type InsertStream = z.infer<typeof insertStreamSchema>;
export type StreamViewer = typeof streamViewers.$inferSelect;
export type ChatMessage = typeof chatMessages.$inferSelect;
export type InsertChatMessage = z.infer<typeof insertChatMessageSchema>;
export type StreamAnalytics = typeof streamAnalytics.$inferSelect;
export type InsertStreamAnalytics = z.infer<typeof insertStreamAnalyticsSchema>;
export type WebrtcConnection = typeof webrtcConnections.$inferSelect;
export type InsertWebrtcConnection = z.infer<typeof insertWebrtcConnectionSchema>;

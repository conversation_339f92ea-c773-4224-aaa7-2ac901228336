# Findz Gaming Streaming Platform - Docker Startup Script
# PowerShell script to start the complete Findz Gaming Streaming Platform

Write-Host "🎮 Starting Findz Gaming Streaming Platform with Docker" -ForegroundColor Green
Write-Host "======================================================" -ForegroundColor Green
Write-Host ""

# Check if Docker is running
try {
    docker version | Out-Null
    Write-Host "✅ Docker is running!" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not running. Please start Docker Desktop first." -ForegroundColor Red
    Write-Host "   1. Open Docker Desktop" -ForegroundColor Yellow
    Write-Host "   2. Wait for it to start completely" -ForegroundColor Yellow
    Write-Host "   3. Run this script again" -ForegroundColor Yellow
    exit 1
}

# Check if Docker Compose is available
try {
    docker-compose version | Out-Null
    Write-Host "✅ Docker Compose is available!" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose not found. Using 'docker compose' instead." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🔧 Setting up Findz Gaming Streaming Platform..." -ForegroundColor Cyan

# Stop any existing containers
Write-Host "🛑 Stopping any existing containers..." -ForegroundColor Yellow
docker-compose down --remove-orphans 2>$null

# Remove old volumes if requested
$cleanStart = Read-Host "Do you want a clean start? (This will remove all data) [y/N]"
if ($cleanStart -eq "y" -or $cleanStart -eq "Y") {
    Write-Host "🧹 Cleaning up old data..." -ForegroundColor Yellow
    docker-compose down -v --remove-orphans
    docker system prune -f
}

# Build and start the platform
Write-Host "🏗️  Building Findz Gaming Streaming Platform..." -ForegroundColor Cyan
docker-compose build --no-cache

Write-Host "🚀 Starting all services..." -ForegroundColor Cyan
docker-compose up -d

# Wait for services to be healthy
Write-Host "⏳ Waiting for services to start..." -ForegroundColor Yellow
Start-Sleep -Seconds 10

# Check service health
Write-Host ""
Write-Host "🔍 Checking service health..." -ForegroundColor Cyan

$services = @("findz-gaming-db", "findz-gaming-redis", "findz-gaming-streaming")
$allHealthy = $true

foreach ($service in $services) {
    $health = docker inspect --format="{{.State.Health.Status}}" $service 2>$null
    if ($health -eq "healthy" -or $health -eq "") {
        $status = if ($health -eq "") { "running" } else { $health }
        Write-Host "  ✅ ${service}: $status" -ForegroundColor Green
    } else {
        Write-Host "  ❌ ${service}: $health" -ForegroundColor Red
        $allHealthy = $false
    }
}

Write-Host ""

if ($allHealthy) {
    Write-Host "🎉 Findz Gaming Streaming Platform is ready!" -ForegroundColor Green
    Write-Host "=============================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "🌐 Platform URLs:" -ForegroundColor Cyan
    Write-Host "   • Streaming Platform: http://localhost:5001" -ForegroundColor White
    Write-Host "   • Gaming Hub: http://localhost:8082" -ForegroundColor White
    Write-Host "   • Database: localhost:5432" -ForegroundColor White
    Write-Host "   • Redis: localhost:6379" -ForegroundColor White
    Write-Host ""
    Write-Host "🎮 Features Available:" -ForegroundColor Cyan
    Write-Host "   • Live Gaming Streams with WebRTC" -ForegroundColor White
    Write-Host "   • Real-time Chat and Community" -ForegroundColor White
    Write-Host "   • AI-Powered Gaming Assistance" -ForegroundColor White
    Write-Host "   • Game Development Streaming" -ForegroundColor White
    Write-Host "   • Stream Recording and Analytics" -ForegroundColor White
    Write-Host "   • Gaming Hub Integration" -ForegroundColor White
    Write-Host ""
    Write-Host "📊 Database Info:" -ForegroundColor Cyan
    Write-Host "   • Database: findz_gaming_streaming" -ForegroundColor White
    Write-Host "   • User: findz_user" -ForegroundColor White
    Write-Host "   • Password: findz_secure_password_2024" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Management Commands:" -ForegroundColor Cyan
    Write-Host "   • View logs: docker-compose logs -f" -ForegroundColor White
    Write-Host "   • Stop platform: docker-compose down" -ForegroundColor White
    Write-Host "   • Restart: docker-compose restart" -ForegroundColor White
    Write-Host "   • Clean restart: docker-compose down -v && docker-compose up -d" -ForegroundColor White
    Write-Host ""
    
    # Test the platform
    Write-Host "🧪 Testing platform connectivity..." -ForegroundColor Cyan
    try {
        $response = Invoke-RestMethod -Uri "http://localhost:5001/api/health" -TimeoutSec 5
        Write-Host "  ✅ Platform API is responding!" -ForegroundColor Green
    } catch {
        Write-Host "  ⚠️  Platform API not ready yet. Give it a few more seconds." -ForegroundColor Yellow
    }
    
    Write-Host ""
    Write-Host "🎮 Ready to start gaming and streaming!" -ForegroundColor Green
    
} else {
    Write-Host "⚠️  Some services are not healthy. Check the logs:" -ForegroundColor Yellow
    Write-Host "   docker-compose logs" -ForegroundColor White
    Write-Host ""
    Write-Host "🔧 Troubleshooting:" -ForegroundColor Cyan
    Write-Host "   • Wait a few more minutes for services to start" -ForegroundColor White
    Write-Host "   • Check Docker Desktop has enough resources" -ForegroundColor White
    Write-Host "   • Restart with: docker-compose restart" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to view live logs (Ctrl+C to exit)..." -ForegroundColor Yellow
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")

# Show live logs
docker-compose logs -f

{"version": 3, "file": "Audio.types.js", "sourceRoot": "", "sources": ["../src/Audio.types.ts"], "names": [], "mappings": "AA0CA,cAAc;AACd;;GAEG;AACH,MAAM,CAAN,IAAY,mBAaX;AAbD,WAAY,mBAAmB;IAC7B;;OAEG;IACH,+EAAiB,CAAA;IACjB;;OAEG;IACH,qEAAY,CAAA;IACZ;;OAEG;IACH,yEAAc,CAAA;AAChB,CAAC,EAbW,mBAAmB,KAAnB,mBAAmB,QAa9B;AAED;;GAEG;AACH,MAAM,CAAN,IAAY,uBASX;AATD,WAAY,uBAAuB;IACjC;;OAEG;IACH,6EAAY,CAAA;IACZ;;OAEG;IACH,iFAAc,CAAA;AAChB,CAAC,EATW,uBAAuB,KAAvB,uBAAuB,QASlC", "sourcesContent": ["// @needsAudit\nexport type AudioMode = {\n  /**\n   * A boolean selecting if recording is enabled on iOS.\n   * > When this flag is set to `true`, playback may be routed to the phone earpiece instead of to the speaker. Set it back to `false` after stopping recording to reenable playback through the speaker.\n   * @default false\n   */\n  allowsRecordingIOS: boolean;\n  /**\n   * An enum selecting how your experience's audio should interact with the audio from other apps on iOS.\n   */\n  interruptionModeIOS: InterruptionModeIOS;\n  /**\n   * A boolean selecting if your experience's audio should play in silent mode on iOS.\n   * @default false\n   */\n  playsInSilentModeIOS: boolean;\n  /**\n   * A boolean selecting if the audio session (playback or recording) should stay active even when the app goes into background.\n   * > This is not available in Expo Go for iOS, it will only work in standalone apps.\n   * > To enable it for standalone apps, [follow the instructions below](#playing-or-recording-audio-in-background)\n   * > to add `UIBackgroundModes` to your app configuration.\n   * @default false\n   */\n  staysActiveInBackground: boolean;\n  /**\n   * An enum selecting how your experience's audio should interact with the audio from other apps on Android.\n   */\n  interruptionModeAndroid: InterruptionModeAndroid;\n  /**\n   * A boolean selecting if your experience's audio should automatically be lowered in volume (\"duck\") if audio from another\n   * app interrupts your experience. If `false`, audio from other apps will pause your audio.\n   * @default true\n   */\n  shouldDuckAndroid: boolean;\n  /**\n   * A boolean selecting if the audio is routed to earpiece on Android.\n   * @default false\n   */\n  playThroughEarpieceAndroid: boolean;\n};\n\n// @needsAudit\n/**\n * @platform ios\n */\nexport enum InterruptionModeIOS {\n  /**\n   * **This is the default option.** If this option is set, your experience's audio is mixed with audio playing in background apps.\n   */\n  MixWithOthers = 0,\n  /**\n   * If this option is set, your experience's audio interrupts audio from other apps.\n   */\n  DoNotMix = 1,\n  /**\n   * If this option is set, your experience's audio lowers the volume (\"ducks\") of audio from other apps while your audio plays.\n   */\n  DuckOthers = 2,\n}\n\n/**\n * @platform android\n */\nexport enum InterruptionModeAndroid {\n  /**\n   * If this option is set, your experience's audio interrupts audio from other apps.\n   */\n  DoNotMix = 1,\n  /**\n   * **This is the default option.** If this option is set, your experience's audio lowers the volume (\"ducks\") of audio from other apps while your audio plays.\n   */\n  DuckOthers = 2,\n}\n"]}
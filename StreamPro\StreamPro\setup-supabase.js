#!/usr/bin/env node

/**
 * Findz Gaming Streaming Platform - Supabase Setup
 * This script sets up the database schema in your Supabase instance
 */

import { createClient } from '@supabase/supabase-js';

class SupabaseSetup {
  constructor() {
    this.supabaseUrl = 'https://opgyuyeuczddftaqkvdt.supabase.co/';
    this.supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9wZ3l1eWV1Y3pkZGZ0YXFrdmR0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjY2MDQzNCwiZXhwIjoyMDU4MjM2NDM0fQ.IZQxZn2Upqtql-filzrSQ1q9zK1nPaweXzwB4LVDzuM';
    this.supabase = createClient(this.supabaseUrl, this.supabaseKey);
  }

  async setupDatabase() {
    console.log('🎮 Setting up Findz Gaming Streaming Platform Database...\n');

    try {
      // Test connection
      console.log('🔗 Testing Supabase connection...');
      const { data, error } = await this.supabase.from('_test').select('*').limit(1);
      if (error && !error.message.includes('does not exist')) {
        throw error;
      }
      console.log('✅ Supabase connection successful!\n');

      // Create tables
      await this.createTables();
      
      // Insert sample data
      await this.insertSampleData();
      
      console.log('\n🎉 Findz Gaming Streaming Platform database setup complete!');
      console.log('\n🚀 Ready to start streaming! Your platform includes:');
      console.log('   • User authentication and profiles');
      console.log('   • Live gaming streams with WebRTC');
      console.log('   • Real-time chat and community features');
      console.log('   • AI-powered gaming assistance');
      console.log('   • Stream analytics and recording');
      console.log('   • Gaming hub integration');
      
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    }
  }

  async createTables() {
    console.log('📋 Creating database tables...');

    const tables = [
      {
        name: 'users',
        sql: `
          CREATE TABLE IF NOT EXISTS users (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            email VARCHAR(255) UNIQUE NOT NULL,
            username VARCHAR(50) UNIQUE NOT NULL,
            display_name VARCHAR(100),
            avatar_url TEXT,
            bio TEXT,
            gaming_preferences JSONB DEFAULT '{}',
            streaming_settings JSONB DEFAULT '{}',
            is_streamer BOOLEAN DEFAULT false,
            is_verified BOOLEAN DEFAULT false,
            follower_count INTEGER DEFAULT 0,
            following_count INTEGER DEFAULT 0,
            total_stream_time INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'gaming_streams',
        sql: `
          CREATE TABLE IF NOT EXISTS gaming_streams (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            streamer_id UUID REFERENCES users(id) ON DELETE CASCADE,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            game_category VARCHAR(100),
            game_title VARCHAR(200),
            thumbnail_url TEXT,
            stream_key VARCHAR(255) UNIQUE,
            rtmp_url TEXT,
            webrtc_url TEXT,
            status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('live', 'offline', 'starting', 'ending')),
            viewer_count INTEGER DEFAULT 0,
            max_viewers INTEGER DEFAULT 0,
            quality VARCHAR(10) DEFAULT '720p',
            bitrate INTEGER DEFAULT 2500,
            fps INTEGER DEFAULT 30,
            is_recording BOOLEAN DEFAULT false,
            recording_url TEXT,
            stream_settings JSONB DEFAULT '{}',
            analytics_data JSONB DEFAULT '{}',
            started_at TIMESTAMP WITH TIME ZONE,
            ended_at TIMESTAMP WITH TIME ZONE,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'chat_messages',
        sql: `
          CREATE TABLE IF NOT EXISTS chat_messages (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            stream_id UUID REFERENCES gaming_streams(id) ON DELETE CASCADE,
            user_id UUID REFERENCES users(id) ON DELETE CASCADE,
            message TEXT NOT NULL,
            message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'emote', 'system', 'ai_response')),
            metadata JSONB DEFAULT '{}',
            is_deleted BOOLEAN DEFAULT false,
            is_moderated BOOLEAN DEFAULT false,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'stream_analytics',
        sql: `
          CREATE TABLE IF NOT EXISTS stream_analytics (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            stream_id UUID REFERENCES gaming_streams(id) ON DELETE CASCADE,
            timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
            viewer_count INTEGER DEFAULT 0,
            chat_activity INTEGER DEFAULT 0,
            quality_metrics JSONB DEFAULT '{}',
            engagement_data JSONB DEFAULT '{}',
            technical_metrics JSONB DEFAULT '{}'
          );
        `
      },
      {
        name: 'game_categories',
        sql: `
          CREATE TABLE IF NOT EXISTS game_categories (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            name VARCHAR(100) UNIQUE NOT NULL,
            slug VARCHAR(100) UNIQUE NOT NULL,
            description TEXT,
            icon_url TEXT,
            banner_url TEXT,
            is_featured BOOLEAN DEFAULT false,
            stream_count INTEGER DEFAULT 0,
            total_viewers INTEGER DEFAULT 0,
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      },
      {
        name: 'ai_stream_assistance',
        sql: `
          CREATE TABLE IF NOT EXISTS ai_stream_assistance (
            id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
            stream_id UUID REFERENCES gaming_streams(id) ON DELETE CASCADE,
            user_id UUID REFERENCES users(id) ON DELETE CASCADE,
            query TEXT NOT NULL,
            response TEXT NOT NULL,
            ai_provider VARCHAR(50) DEFAULT 'ollama',
            response_time INTEGER,
            confidence_score FLOAT,
            feedback_rating INTEGER CHECK (feedback_rating >= 1 AND feedback_rating <= 5),
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
          );
        `
      }
    ];

    for (const table of tables) {
      try {
        const { error } = await this.supabase.rpc('exec_sql', { sql: table.sql });
        if (error) {
          console.log(`⚠️  Table ${table.name} might already exist or there was an issue:`, error.message);
        } else {
          console.log(`✅ Created table: ${table.name}`);
        }
      } catch (error) {
        console.log(`⚠️  Could not create table ${table.name}:`, error.message);
      }
    }

    // Create indexes for better performance
    await this.createIndexes();
  }

  async createIndexes() {
    console.log('🔍 Creating database indexes...');

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_gaming_streams_streamer_id ON gaming_streams(streamer_id);',
      'CREATE INDEX IF NOT EXISTS idx_gaming_streams_status ON gaming_streams(status);',
      'CREATE INDEX IF NOT EXISTS idx_gaming_streams_game_category ON gaming_streams(game_category);',
      'CREATE INDEX IF NOT EXISTS idx_chat_messages_stream_id ON chat_messages(stream_id);',
      'CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id);',
      'CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);',
      'CREATE INDEX IF NOT EXISTS idx_stream_analytics_stream_id ON stream_analytics(stream_id);',
      'CREATE INDEX IF NOT EXISTS idx_stream_analytics_timestamp ON stream_analytics(timestamp);',
      'CREATE INDEX IF NOT EXISTS idx_ai_assistance_stream_id ON ai_stream_assistance(stream_id);',
      'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);',
      'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);'
    ];

    for (const indexSql of indexes) {
      try {
        const { error } = await this.supabase.rpc('exec_sql', { sql: indexSql });
        if (error && !error.message.includes('already exists')) {
          console.log(`⚠️  Index creation issue:`, error.message);
        }
      } catch (error) {
        // Indexes might already exist, that's okay
      }
    }

    console.log('✅ Database indexes created');
  }

  async insertSampleData() {
    console.log('🎮 Inserting sample gaming data...');

    // Sample game categories
    const gameCategories = [
      { name: 'Action Games', slug: 'action', description: 'Fast-paced action and adventure games', is_featured: true },
      { name: 'Strategy Games', slug: 'strategy', description: 'Strategic thinking and planning games', is_featured: true },
      { name: 'RPG Games', slug: 'rpg', description: 'Role-playing and character development games', is_featured: true },
      { name: 'Indie Games', slug: 'indie', description: 'Independent and creative games', is_featured: false },
      { name: 'Multiplayer Games', slug: 'multiplayer', description: 'Online multiplayer experiences', is_featured: true },
      { name: 'Game Development', slug: 'gamedev', description: 'Live game development and coding', is_featured: true }
    ];

    try {
      const { error } = await this.supabase
        .from('game_categories')
        .upsert(gameCategories, { onConflict: 'slug' });
      
      if (error) {
        console.log('⚠️  Could not insert game categories:', error.message);
      } else {
        console.log('✅ Sample game categories added');
      }
    } catch (error) {
      console.log('⚠️  Sample data insertion issue:', error.message);
    }
  }
}

// Run the setup
const setup = new SupabaseSetup();
setup.setupDatabase().catch(console.error);

export default SupabaseSetup;

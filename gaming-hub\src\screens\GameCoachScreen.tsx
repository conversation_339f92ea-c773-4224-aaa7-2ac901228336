import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';

import { gameCoachService, GameAnalysis, GameTip } from '../services/gameCoachService';
import { GameContext } from '../services/aiService';
import { theme, colors } from '../theme';
import { useAuth } from '../contexts/AuthContext';

export default function GameCoachScreen() {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useAuth();
  
  const [analysis, setAnalysis] = useState<GameAnalysis | null>(null);
  const [tips, setTips] = useState<GameTip[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'strategy' | 'mechanics' | 'optimization' | 'mindset'>('all');

  // Get game context and session data from route params
  const gameContext = (route.params as any)?.gameContext as GameContext | undefined;
  const sessionData = (route.params as any)?.sessionData;

  useEffect(() => {
    if (user && gameContext) {
      loadCoachingData();
    }
  }, [user, gameContext]);

  const loadCoachingData = async () => {
    if (!user || !gameContext) return;

    try {
      setLoading(true);

      // Load analysis if session data is provided
      if (sessionData) {
        const gameAnalysis = await gameCoachService.analyzeGameSession(
          user.id,
          gameContext,
          sessionData
        );
        setAnalysis(gameAnalysis);
      }

      // Load personalized tips
      const personalizedTips = await gameCoachService.generatePersonalizedTips(
        user.id,
        gameContext.gameId,
        8
      );
      setTips(personalizedTips);
    } catch (error) {
      console.error('Error loading coaching data:', error);
      Alert.alert('Error', 'Failed to load coaching insights. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const filteredTips = selectedCategory === 'all' 
    ? tips 
    : tips.filter(tip => tip.category === selectedCategory);

  const renderAnalysisSection = () => {
    if (!analysis) return null;

    return (
      <View style={styles.analysisSection}>
        <Text style={styles.sectionTitle}>Session Analysis</Text>
        
        {/* Strengths */}
        {analysis.strengths.length > 0 && (
          <View style={styles.analysisCard}>
            <View style={styles.analysisHeader}>
              <Ionicons name="checkmark-circle" size={24} color={colors.success} />
              <Text style={styles.analysisCardTitle}>Strengths</Text>
            </View>
            {analysis.strengths.map((strength, index) => (
              <Text key={index} style={styles.analysisItem}>• {strength}</Text>
            ))}
          </View>
        )}

        {/* Areas for Improvement */}
        {analysis.weaknesses.length > 0 && (
          <View style={styles.analysisCard}>
            <View style={styles.analysisHeader}>
              <Ionicons name="trending-up" size={24} color={colors.warning} />
              <Text style={styles.analysisCardTitle}>Areas for Improvement</Text>
            </View>
            {analysis.weaknesses.map((weakness, index) => (
              <Text key={index} style={styles.analysisItem}>• {weakness}</Text>
            ))}
          </View>
        )}

        {/* Recommendations */}
        {analysis.recommendations.length > 0 && (
          <View style={styles.analysisCard}>
            <View style={styles.analysisHeader}>
              <Ionicons name="bulb" size={24} color={colors.primary} />
              <Text style={styles.analysisCardTitle}>Recommendations</Text>
            </View>
            {analysis.recommendations.map((recommendation, index) => (
              <Text key={index} style={styles.analysisItem}>• {recommendation}</Text>
            ))}
          </View>
        )}

        {/* Next Goals */}
        {analysis.nextGoals.length > 0 && (
          <View style={styles.analysisCard}>
            <View style={styles.analysisHeader}>
              <Ionicons name="flag" size={24} color={colors.secondary} />
              <Text style={styles.analysisCardTitle}>Next Goals</Text>
            </View>
            {analysis.nextGoals.map((goal, index) => (
              <Text key={index} style={styles.analysisItem}>• {goal}</Text>
            ))}
          </View>
        )}

        {/* Difficulty Adjustment */}
        {analysis.difficultyAdjustment && analysis.difficultyAdjustment !== 'maintain' && (
          <View style={[styles.analysisCard, styles.difficultyCard]}>
            <View style={styles.analysisHeader}>
              <Ionicons 
                name={analysis.difficultyAdjustment === 'harder' ? 'arrow-up' : 'arrow-down'} 
                size={24} 
                color={analysis.difficultyAdjustment === 'harder' ? colors.error : colors.success} 
              />
              <Text style={styles.analysisCardTitle}>Difficulty Suggestion</Text>
            </View>
            <Text style={styles.analysisItem}>
              Consider trying {analysis.difficultyAdjustment} difficulty for your next session
            </Text>
          </View>
        )}
      </View>
    );
  };

  const renderTipCategories = () => {
    const categories = [
      { id: 'all', name: 'All Tips', icon: 'apps' },
      { id: 'strategy', name: 'Strategy', icon: 'chess' },
      { id: 'mechanics', name: 'Mechanics', icon: 'settings' },
      { id: 'optimization', name: 'Optimization', icon: 'speedometer' },
      { id: 'mindset', name: 'Mindset', icon: 'heart' },
    ];

    return (
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.categoriesScroll}
        contentContainerStyle={styles.categoriesContainer}
      >
        {categories.map((category) => (
          <TouchableOpacity
            key={category.id}
            style={[
              styles.categoryButton,
              selectedCategory === category.id && styles.categoryButtonActive
            ]}
            onPress={() => setSelectedCategory(category.id as any)}
          >
            <Ionicons 
              name={category.icon as any} 
              size={20} 
              color={selectedCategory === category.id ? colors.text : colors.textSecondary} 
            />
            <Text style={[
              styles.categoryButtonText,
              selectedCategory === category.id && styles.categoryButtonTextActive
            ]}>
              {category.name}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderTip = (tip: GameTip) => (
    <View key={tip.id} style={styles.tipCard}>
      <View style={styles.tipHeader}>
        <View style={styles.tipCategory}>
          <Text style={styles.tipCategoryText}>{tip.category}</Text>
        </View>
        <View style={styles.tipMeta}>
          <Ionicons 
            name={tip.aiGenerated ? 'sparkles' : 'book'} 
            size={14} 
            color={colors.textSecondary} 
          />
          <Text style={styles.tipDifficulty}>{tip.difficulty}</Text>
        </View>
      </View>
      
      <Text style={styles.tipTitle}>{tip.title}</Text>
      <Text style={styles.tipContent}>{tip.content}</Text>
      
      <View style={styles.tipFooter}>
        <View style={styles.relevanceBar}>
          <View 
            style={[
              styles.relevanceFill, 
              { width: `${tip.relevanceScore * 100}%` }
            ]} 
          />
        </View>
        <Text style={styles.relevanceText}>
          {Math.round(tip.relevanceScore * 100)}% relevant
        </Text>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Analyzing your gameplay...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={colors.gradientPrimary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Ionicons name="school" size={32} color={colors.text} />
            <Text style={styles.headerTitle}>Game Coach</Text>
            <Text style={styles.headerSubtitle}>
              {gameContext ? `Coaching for ${gameContext.gameTitle}` : 'Personalized Gaming Insights'}
            </Text>
          </View>
        </View>
      </LinearGradient>

      {/* Analysis Section */}
      {renderAnalysisSection()}

      {/* Tips Section */}
      <View style={styles.tipsSection}>
        <Text style={styles.sectionTitle}>Personalized Tips</Text>
        
        {renderTipCategories()}
        
        <View style={styles.tipsContainer}>
          {filteredTips.length > 0 ? (
            filteredTips.map(renderTip)
          ) : (
            <View style={styles.emptyState}>
              <Ionicons name="bulb-outline" size={64} color={colors.textSecondary} />
              <Text style={styles.emptyStateTitle}>No tips available</Text>
              <Text style={styles.emptyStateText}>
                Play more games to get personalized coaching insights!
              </Text>
            </View>
          )}
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginTop: theme.spacing.md,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: theme.spacing.xs,
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    ...theme.typography.textStyles.h2,
    color: colors.text,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  headerSubtitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    opacity: 0.9,
    textAlign: 'center',
  },
  analysisSection: {
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginBottom: theme.spacing.md,
  },
  analysisCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  analysisHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  analysisCardTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginLeft: theme.spacing.sm,
  },
  analysisItem: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
    lineHeight: 20,
  },
  difficultyCard: {
    borderWidth: 1,
    borderColor: colors.primary,
  },
  tipsSection: {
    padding: theme.spacing.lg,
    paddingTop: 0,
  },
  categoriesScroll: {
    marginBottom: theme.spacing.md,
  },
  categoriesContainer: {
    paddingRight: theme.spacing.lg,
  },
  categoryButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundCard,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    marginRight: theme.spacing.sm,
  },
  categoryButtonActive: {
    backgroundColor: colors.primary,
  },
  categoryButtonText: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  categoryButtonTextActive: {
    color: colors.text,
  },
  tipsContainer: {
    marginTop: theme.spacing.sm,
  },
  tipCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  tipHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  tipCategory: {
    backgroundColor: colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  tipCategoryText: {
    ...theme.typography.textStyles.small,
    color: colors.text,
    fontSize: 10,
    fontWeight: '600',
  },
  tipMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  tipDifficulty: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    marginLeft: theme.spacing.xs,
    fontSize: 10,
  },
  tipTitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  tipContent: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    lineHeight: 20,
    marginBottom: theme.spacing.sm,
  },
  tipFooter: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  relevanceBar: {
    flex: 1,
    height: 4,
    backgroundColor: colors.background,
    borderRadius: 2,
    marginRight: theme.spacing.sm,
  },
  relevanceFill: {
    height: '100%',
    backgroundColor: colors.secondary,
    borderRadius: 2,
  },
  relevanceText: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    fontSize: 10,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing['3xl'],
  },
  emptyStateTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.sm,
  },
  emptyStateText: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    textAlign: 'center',
  },
});

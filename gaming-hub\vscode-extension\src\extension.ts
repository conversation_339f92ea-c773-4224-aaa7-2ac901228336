import * as vscode from 'vscode';
import express, { Request, Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';
import axios from 'axios';

let server: any;
const PORT = 3001;

export function activate(context: vscode.ExtensionContext) {
    console.log('Findz Gaming Hub extension is now active!');

    // Start local server for communication with Gaming Hub
    startLocalServer();

    // Register commands
    const createProjectCommand = vscode.commands.registerCommand('findz.createGameProject', createGameProject);
    const openHubCommand = vscode.commands.registerCommand('findz.openGameHub', openGameHub);
    const aiAssistCommand = vscode.commands.registerCommand('findz.aiAssist', aiAssist);
    const deployCommand = vscode.commands.registerCommand('findz.deployToHub', deployToHub);

    context.subscriptions.push(createProjectCommand, openHubCommand, aiAssistCommand, deployCommand);

    // Show welcome message
    vscode.window.showInformationMessage('Findz Gaming Hub extension activated! Ready for game development.');
}

export function deactivate() {
    if (server) {
        server.close();
    }
}

function startLocalServer() {
    const app = express();
    app.use(express.json());

    // Health check endpoint
    app.get('/health', (req: Request, res: Response) => {
        res.json({ status: 'ok', extension: 'findz-gaming-hub', version: '1.0.0' });
    });

    // Create project endpoint
    app.post('/create-project', async (req: Request, res: Response) => {
        try {
            const { template, projectName } = req.body;
            const projectPath = await createProjectFromTemplate(template, projectName);
            res.json({ success: true, projectPath });
        } catch (error: any) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // Open project endpoint
    app.post('/open-project', async (req: Request, res: Response) => {
        try {
            const { projectPath } = req.body;
            await openProjectInVSCode(projectPath);
            res.json({ success: true });
        } catch (error: any) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    // Get projects endpoint
    app.get('/projects', (req: Request, res: Response) => {
        try {
            const projects = getRecentProjects();
            res.json({ projects });
        } catch (error: any) {
            res.status(500).json({ error: error.message });
        }
    });

    // Execute command endpoint
    app.post('/execute-command', async (req: Request, res: Response) => {
        try {
            const { command, args } = req.body;
            const result = await vscode.commands.executeCommand(command, ...(args || []));
            res.json({ success: true, result });
        } catch (error: any) {
            res.status(500).json({ success: false, error: error.message });
        }
    });

    server = app.listen(PORT, () => {
        console.log(`Findz Gaming Hub extension server running on port ${PORT}`);
    });
}

async function createGameProject() {
    const templates = [
        { label: 'HTML5 Canvas Game', value: 'html5-basic' },
        { label: 'Phaser.js Platformer', value: 'phaser-platformer' },
        { label: 'React Game Component', value: 'react-game' },
    ];

    const selectedTemplate = await vscode.window.showQuickPick(templates, {
        placeHolder: 'Select a game template'
    });

    if (!selectedTemplate) return;

    const projectName = await vscode.window.showInputBox({
        prompt: 'Enter project name',
        placeHolder: 'my-awesome-game'
    });

    if (!projectName) return;

    try {
        const projectPath = await createProjectFromTemplate(selectedTemplate.value, projectName);
        
        const openProject = await vscode.window.showInformationMessage(
            `Project "${projectName}" created successfully!`,
            'Open Project',
            'Open in New Window'
        );

        if (openProject === 'Open Project') {
            await openProjectInVSCode(projectPath);
        } else if (openProject === 'Open in New Window') {
            await vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectPath), true);
        }
    } catch (error: any) {
        vscode.window.showErrorMessage(`Failed to create project: ${error.message}`);
    }
}

async function createProjectFromTemplate(templateId: string, projectName: string): Promise<string> {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
        throw new Error('No workspace folder open');
    }

    const projectPath = path.join(workspaceFolders[0].uri.fsPath, projectName);
    
    // Create project directory
    if (!fs.existsSync(projectPath)) {
        fs.mkdirSync(projectPath, { recursive: true });
    }

    // Get template files (this would normally come from the Gaming Hub)
    const templateFiles = getTemplateFiles(templateId);
    
    // Create files
    for (const file of templateFiles) {
        const filePath = path.join(projectPath, file.path);
        const fileDir = path.dirname(filePath);
        
        if (!fs.existsSync(fileDir)) {
            fs.mkdirSync(fileDir, { recursive: true });
        }
        
        fs.writeFileSync(filePath, file.content);
    }

    return projectPath;
}

function getTemplateFiles(templateId: string): Array<{ path: string; content: string }> {
    // This is a simplified version - in reality, this would fetch from the Gaming Hub
    const templates: any = {
        'html5-basic': [
            {
                path: 'index.html',
                content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Game</title>
    <style>
        body { margin: 0; padding: 0; background: #000; display: flex; justify-content: center; align-items: center; height: 100vh; }
        canvas { border: 1px solid #fff; }
    </style>
</head>
<body>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    <script src="game.js"></script>
</body>
</html>`
            },
            {
                path: 'game.js',
                content: `// Basic HTML5 Game Template
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Game state
const game = {
    player: { x: 400, y: 300, width: 50, height: 50, speed: 5 },
    keys: {},
    running: true
};

// Input handling
document.addEventListener('keydown', (e) => {
    game.keys[e.key] = true;
});

document.addEventListener('keyup', (e) => {
    game.keys[e.key] = false;
});

// Game loop
function update() {
    // Player movement
    if (game.keys['ArrowLeft'] || game.keys['a']) {
        game.player.x -= game.player.speed;
    }
    if (game.keys['ArrowRight'] || game.keys['d']) {
        game.player.x += game.player.speed;
    }
    if (game.keys['ArrowUp'] || game.keys['w']) {
        game.player.y -= game.player.speed;
    }
    if (game.keys['ArrowDown'] || game.keys['s']) {
        game.player.y += game.player.speed;
    }

    // Keep player in bounds
    game.player.x = Math.max(0, Math.min(canvas.width - game.player.width, game.player.x));
    game.player.y = Math.max(0, Math.min(canvas.height - game.player.height, game.player.y));
}

function render() {
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw player
    ctx.fillStyle = '#0f0';
    ctx.fillRect(game.player.x, game.player.y, game.player.width, game.player.height);

    // Draw instructions
    ctx.fillStyle = '#fff';
    ctx.font = '16px Arial';
    ctx.fillText('Use WASD or Arrow Keys to move', 10, 30);
}

function gameLoop() {
    if (game.running) {
        update();
        render();
        requestAnimationFrame(gameLoop);
    }
}

// Start the game
gameLoop();`
            },
            {
                path: 'README.md',
                content: `# My Game

A simple HTML5 canvas game created with the Findz Gaming Hub.

## How to Run

1. Open \`index.html\` in a web browser
2. Use WASD or Arrow Keys to move the player

## Features

- Basic player movement
- Canvas rendering
- Keyboard input handling

## Next Steps

- Add enemies or obstacles
- Implement collision detection
- Add sound effects
- Create multiple levels
`
            }
        ]
    };

    return templates[templateId] || [];
}

async function openProjectInVSCode(projectPath: string) {
    await vscode.commands.executeCommand('vscode.openFolder', vscode.Uri.file(projectPath));
}

function getRecentProjects(): any[] {
    // This would normally track recent projects
    // For now, return empty array
    return [];
}

async function openGameHub() {
    const config = vscode.workspace.getConfiguration('findz');
    const hubUrl = config.get('hubUrl', 'http://localhost:8082');
    
    await vscode.env.openExternal(vscode.Uri.parse(hubUrl));
}

async function aiAssist() {
    const editor = vscode.window.activeTextEditor;
    if (!editor) {
        vscode.window.showWarningMessage('No active editor found');
        return;
    }

    const selection = editor.selection;
    const selectedText = editor.document.getText(selection);
    
    if (!selectedText) {
        vscode.window.showWarningMessage('Please select some code for AI assistance');
        return;
    }

    try {
        // This would normally call the Gaming Hub AI API
        vscode.window.showInformationMessage('AI assistance feature coming soon!');
    } catch (error: any) {
        vscode.window.showErrorMessage(`AI assistance failed: ${error.message}`);
    }
}

async function deployToHub() {
    const workspaceFolders = vscode.workspace.workspaceFolders;
    if (!workspaceFolders) {
        vscode.window.showWarningMessage('No workspace folder open');
        return;
    }

    try {
        // This would normally deploy to the Gaming Hub
        vscode.window.showInformationMessage('Deploy to Gaming Hub feature coming soon!');
    } catch (error: any) {
        vscode.window.showErrorMessage(`Deployment failed: ${error.message}`);
    }
}

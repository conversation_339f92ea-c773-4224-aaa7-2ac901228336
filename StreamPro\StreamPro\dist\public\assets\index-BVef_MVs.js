var Vd=e=>{throw TypeError(e)};var Zl=(e,t,n)=>t.has(e)||Vd("Cannot "+n);var E=(e,t,n)=>(Zl(e,t,"read from private field"),n?n.call(e):t.get(e)),V=(e,t,n)=>t.has(e)?Vd("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,n),D=(e,t,n,r)=>(Zl(e,t,"write to private field"),r?r.call(e,n):t.set(e,n),n),G=(e,t,n)=>(Zl(e,t,"access private method"),n);var Qs=(e,t,n,r)=>({set _(o){D(e,t,o,n)},get _(){return E(e,t,r)}});function I0(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const o in r)if(o!=="default"&&!(o in e)){const s=Object.getOwnPropertyDescriptor(r,o);s&&Object.defineProperty(e,o,s.get?s:{enumerable:!0,get:()=>r[o]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))r(o);new MutationObserver(o=>{for(const s of o)if(s.type==="childList")for(const i of s.addedNodes)i.tagName==="LINK"&&i.rel==="modulepreload"&&r(i)}).observe(document,{childList:!0,subtree:!0});function n(o){const s={};return o.integrity&&(s.integrity=o.integrity),o.referrerPolicy&&(s.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?s.credentials="include":o.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(o){if(o.ep)return;o.ep=!0;const s=n(o);fetch(o.href,s)}})();function rh(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var oh={exports:{}},wl={},sh={exports:{}},Y={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ds=Symbol.for("react.element"),L0=Symbol.for("react.portal"),D0=Symbol.for("react.fragment"),F0=Symbol.for("react.strict_mode"),z0=Symbol.for("react.profiler"),$0=Symbol.for("react.provider"),U0=Symbol.for("react.context"),V0=Symbol.for("react.forward_ref"),B0=Symbol.for("react.suspense"),H0=Symbol.for("react.memo"),W0=Symbol.for("react.lazy"),Bd=Symbol.iterator;function K0(e){return e===null||typeof e!="object"?null:(e=Bd&&e[Bd]||e["@@iterator"],typeof e=="function"?e:null)}var ih={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},lh=Object.assign,ah={};function No(e,t,n){this.props=e,this.context=t,this.refs=ah,this.updater=n||ih}No.prototype.isReactComponent={};No.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};No.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function uh(){}uh.prototype=No.prototype;function hc(e,t,n){this.props=e,this.context=t,this.refs=ah,this.updater=n||ih}var mc=hc.prototype=new uh;mc.constructor=hc;lh(mc,No.prototype);mc.isPureReactComponent=!0;var Hd=Array.isArray,ch=Object.prototype.hasOwnProperty,vc={current:null},dh={key:!0,ref:!0,__self:!0,__source:!0};function fh(e,t,n){var r,o={},s=null,i=null;if(t!=null)for(r in t.ref!==void 0&&(i=t.ref),t.key!==void 0&&(s=""+t.key),t)ch.call(t,r)&&!dh.hasOwnProperty(r)&&(o[r]=t[r]);var l=arguments.length-2;if(l===1)o.children=n;else if(1<l){for(var a=Array(l),c=0;c<l;c++)a[c]=arguments[c+2];o.children=a}if(e&&e.defaultProps)for(r in l=e.defaultProps,l)o[r]===void 0&&(o[r]=l[r]);return{$$typeof:Ds,type:e,key:s,ref:i,props:o,_owner:vc.current}}function Q0(e,t){return{$$typeof:Ds,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function gc(e){return typeof e=="object"&&e!==null&&e.$$typeof===Ds}function G0(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var Wd=/\/+/g;function Jl(e,t){return typeof e=="object"&&e!==null&&e.key!=null?G0(""+e.key):t.toString(36)}function xi(e,t,n,r,o){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var i=!1;if(e===null)i=!0;else switch(s){case"string":case"number":i=!0;break;case"object":switch(e.$$typeof){case Ds:case L0:i=!0}}if(i)return i=e,o=o(i),e=r===""?"."+Jl(i,0):r,Hd(o)?(n="",e!=null&&(n=e.replace(Wd,"$&/")+"/"),xi(o,t,n,"",function(c){return c})):o!=null&&(gc(o)&&(o=Q0(o,n+(!o.key||i&&i.key===o.key?"":(""+o.key).replace(Wd,"$&/")+"/")+e)),t.push(o)),1;if(i=0,r=r===""?".":r+":",Hd(e))for(var l=0;l<e.length;l++){s=e[l];var a=r+Jl(s,l);i+=xi(s,t,n,a,o)}else if(a=K0(e),typeof a=="function")for(e=a.call(e),l=0;!(s=e.next()).done;)s=s.value,a=r+Jl(s,l++),i+=xi(s,t,n,a,o);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return i}function Gs(e,t,n){if(e==null)return e;var r=[],o=0;return xi(e,r,"","",function(s){return t.call(n,s,o++)}),r}function q0(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var We={current:null},wi={transition:null},Y0={ReactCurrentDispatcher:We,ReactCurrentBatchConfig:wi,ReactCurrentOwner:vc};function ph(){throw Error("act(...) is not supported in production builds of React.")}Y.Children={map:Gs,forEach:function(e,t,n){Gs(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return Gs(e,function(){t++}),t},toArray:function(e){return Gs(e,function(t){return t})||[]},only:function(e){if(!gc(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};Y.Component=No;Y.Fragment=D0;Y.Profiler=z0;Y.PureComponent=hc;Y.StrictMode=F0;Y.Suspense=B0;Y.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Y0;Y.act=ph;Y.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=lh({},e.props),o=e.key,s=e.ref,i=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,i=vc.current),t.key!==void 0&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(a in t)ch.call(t,a)&&!dh.hasOwnProperty(a)&&(r[a]=t[a]===void 0&&l!==void 0?l[a]:t[a])}var a=arguments.length-2;if(a===1)r.children=n;else if(1<a){l=Array(a);for(var c=0;c<a;c++)l[c]=arguments[c+2];r.children=l}return{$$typeof:Ds,type:e.type,key:o,ref:s,props:r,_owner:i}};Y.createContext=function(e){return e={$$typeof:U0,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:$0,_context:e},e.Consumer=e};Y.createElement=fh;Y.createFactory=function(e){var t=fh.bind(null,e);return t.type=e,t};Y.createRef=function(){return{current:null}};Y.forwardRef=function(e){return{$$typeof:V0,render:e}};Y.isValidElement=gc;Y.lazy=function(e){return{$$typeof:W0,_payload:{_status:-1,_result:e},_init:q0}};Y.memo=function(e,t){return{$$typeof:H0,type:e,compare:t===void 0?null:t}};Y.startTransition=function(e){var t=wi.transition;wi.transition={};try{e()}finally{wi.transition=t}};Y.unstable_act=ph;Y.useCallback=function(e,t){return We.current.useCallback(e,t)};Y.useContext=function(e){return We.current.useContext(e)};Y.useDebugValue=function(){};Y.useDeferredValue=function(e){return We.current.useDeferredValue(e)};Y.useEffect=function(e,t){return We.current.useEffect(e,t)};Y.useId=function(){return We.current.useId()};Y.useImperativeHandle=function(e,t,n){return We.current.useImperativeHandle(e,t,n)};Y.useInsertionEffect=function(e,t){return We.current.useInsertionEffect(e,t)};Y.useLayoutEffect=function(e,t){return We.current.useLayoutEffect(e,t)};Y.useMemo=function(e,t){return We.current.useMemo(e,t)};Y.useReducer=function(e,t,n){return We.current.useReducer(e,t,n)};Y.useRef=function(e){return We.current.useRef(e)};Y.useState=function(e){return We.current.useState(e)};Y.useSyncExternalStore=function(e,t,n){return We.current.useSyncExternalStore(e,t,n)};Y.useTransition=function(){return We.current.useTransition()};Y.version="18.3.1";sh.exports=Y;var f=sh.exports;const pn=rh(f),hh=I0({__proto__:null,default:pn},[f]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var X0=f,Z0=Symbol.for("react.element"),J0=Symbol.for("react.fragment"),ex=Object.prototype.hasOwnProperty,tx=X0.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,nx={key:!0,ref:!0,__self:!0,__source:!0};function mh(e,t,n){var r,o={},s=null,i=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(i=t.ref);for(r in t)ex.call(t,r)&&!nx.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)o[r]===void 0&&(o[r]=t[r]);return{$$typeof:Z0,type:e,key:s,ref:i,props:o,_owner:tx.current}}wl.Fragment=J0;wl.jsx=mh;wl.jsxs=mh;oh.exports=wl;var u=oh.exports,vh={exports:{}},ct={},gh={exports:{}},yh={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(j,A){var $=j.length;j.push(A);e:for(;0<$;){var W=$-1>>>1,re=j[W];if(0<o(re,A))j[W]=A,j[$]=re,$=W;else break e}}function n(j){return j.length===0?null:j[0]}function r(j){if(j.length===0)return null;var A=j[0],$=j.pop();if($!==A){j[0]=$;e:for(var W=0,re=j.length,Fe=re>>>1;W<Fe;){var Ce=2*(W+1)-1,Ot=j[Ce],ze=Ce+1,H=j[ze];if(0>o(Ot,$))ze<re&&0>o(H,Ot)?(j[W]=H,j[ze]=$,W=ze):(j[W]=Ot,j[Ce]=$,W=Ce);else if(ze<re&&0>o(H,$))j[W]=H,j[ze]=$,W=ze;else break e}}return A}function o(j,A){var $=j.sortIndex-A.sortIndex;return $!==0?$:j.id-A.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var i=Date,l=i.now();e.unstable_now=function(){return i.now()-l}}var a=[],c=[],p=1,d=null,g=3,S=!1,w=!1,m=!1,x=typeof setTimeout=="function"?setTimeout:null,v=typeof clearTimeout=="function"?clearTimeout:null,h=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function y(j){for(var A=n(c);A!==null;){if(A.callback===null)r(c);else if(A.startTime<=j)r(c),A.sortIndex=A.expirationTime,t(a,A);else break;A=n(c)}}function C(j){if(m=!1,y(j),!w)if(n(a)!==null)w=!0,z(b);else{var A=n(c);A!==null&&B(C,A.startTime-j)}}function b(j,A){w=!1,m&&(m=!1,v(P),P=-1),S=!0;var $=g;try{for(y(A),d=n(a);d!==null&&(!(d.expirationTime>A)||j&&!I());){var W=d.callback;if(typeof W=="function"){d.callback=null,g=d.priorityLevel;var re=W(d.expirationTime<=A);A=e.unstable_now(),typeof re=="function"?d.callback=re:d===n(a)&&r(a),y(A)}else r(a);d=n(a)}if(d!==null)var Fe=!0;else{var Ce=n(c);Ce!==null&&B(C,Ce.startTime-A),Fe=!1}return Fe}finally{d=null,g=$,S=!1}}var k=!1,N=null,P=-1,_=5,O=-1;function I(){return!(e.unstable_now()-O<_)}function M(){if(N!==null){var j=e.unstable_now();O=j;var A=!0;try{A=N(!0,j)}finally{A?L():(k=!1,N=null)}}else k=!1}var L;if(typeof h=="function")L=function(){h(M)};else if(typeof MessageChannel<"u"){var T=new MessageChannel,U=T.port2;T.port1.onmessage=M,L=function(){U.postMessage(null)}}else L=function(){x(M,0)};function z(j){N=j,k||(k=!0,L())}function B(j,A){P=x(function(){j(e.unstable_now())},A)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(j){j.callback=null},e.unstable_continueExecution=function(){w||S||(w=!0,z(b))},e.unstable_forceFrameRate=function(j){0>j||125<j?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):_=0<j?Math.floor(1e3/j):5},e.unstable_getCurrentPriorityLevel=function(){return g},e.unstable_getFirstCallbackNode=function(){return n(a)},e.unstable_next=function(j){switch(g){case 1:case 2:case 3:var A=3;break;default:A=g}var $=g;g=A;try{return j()}finally{g=$}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(j,A){switch(j){case 1:case 2:case 3:case 4:case 5:break;default:j=3}var $=g;g=j;try{return A()}finally{g=$}},e.unstable_scheduleCallback=function(j,A,$){var W=e.unstable_now();switch(typeof $=="object"&&$!==null?($=$.delay,$=typeof $=="number"&&0<$?W+$:W):$=W,j){case 1:var re=-1;break;case 2:re=250;break;case 5:re=**********;break;case 4:re=1e4;break;default:re=5e3}return re=$+re,j={id:p++,callback:A,priorityLevel:j,startTime:$,expirationTime:re,sortIndex:-1},$>W?(j.sortIndex=$,t(c,j),n(a)===null&&j===n(c)&&(m?(v(P),P=-1):m=!0,B(C,$-W))):(j.sortIndex=re,t(a,j),w||S||(w=!0,z(b))),j},e.unstable_shouldYield=I,e.unstable_wrapCallback=function(j){var A=g;return function(){var $=g;g=A;try{return j.apply(this,arguments)}finally{g=$}}}})(yh);gh.exports=yh;var rx=gh.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ox=f,at=rx;function R(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var xh=new Set,fs={};function Er(e,t){po(e,t),po(e+"Capture",t)}function po(e,t){for(fs[e]=t,e=0;e<t.length;e++)xh.add(t[e])}var nn=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Va=Object.prototype.hasOwnProperty,sx=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,Kd={},Qd={};function ix(e){return Va.call(Qd,e)?!0:Va.call(Kd,e)?!1:sx.test(e)?Qd[e]=!0:(Kd[e]=!0,!1)}function lx(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ax(e,t,n,r){if(t===null||typeof t>"u"||lx(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function Ke(e,t,n,r,o,s,i){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=i}var Re={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){Re[e]=new Ke(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];Re[t]=new Ke(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){Re[e]=new Ke(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){Re[e]=new Ke(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){Re[e]=new Ke(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){Re[e]=new Ke(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){Re[e]=new Ke(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){Re[e]=new Ke(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){Re[e]=new Ke(e,5,!1,e.toLowerCase(),null,!1,!1)});var yc=/[\-:]([a-z])/g;function xc(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(yc,xc);Re[t]=new Ke(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(yc,xc);Re[t]=new Ke(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(yc,xc);Re[t]=new Ke(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){Re[e]=new Ke(e,1,!1,e.toLowerCase(),null,!1,!1)});Re.xlinkHref=new Ke("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){Re[e]=new Ke(e,1,!1,e.toLowerCase(),null,!0,!0)});function wc(e,t,n,r){var o=Re.hasOwnProperty(t)?Re[t]:null;(o!==null?o.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ax(t,n,o,r)&&(n=null),r||o===null?ix(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=n===null?o.type===3?!1:"":n:(t=o.attributeName,r=o.attributeNamespace,n===null?e.removeAttribute(t):(o=o.type,n=o===3||o===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var un=ox.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,qs=Symbol.for("react.element"),_r=Symbol.for("react.portal"),Or=Symbol.for("react.fragment"),Sc=Symbol.for("react.strict_mode"),Ba=Symbol.for("react.profiler"),wh=Symbol.for("react.provider"),Sh=Symbol.for("react.context"),Cc=Symbol.for("react.forward_ref"),Ha=Symbol.for("react.suspense"),Wa=Symbol.for("react.suspense_list"),Ec=Symbol.for("react.memo"),vn=Symbol.for("react.lazy"),Ch=Symbol.for("react.offscreen"),Gd=Symbol.iterator;function Do(e){return e===null||typeof e!="object"?null:(e=Gd&&e[Gd]||e["@@iterator"],typeof e=="function"?e:null)}var ve=Object.assign,ea;function Qo(e){if(ea===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);ea=t&&t[1]||""}return`
`+ea+e}var ta=!1;function na(e,t){if(!e||ta)return"";ta=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(c){var r=c}Reflect.construct(e,[],t)}else{try{t.call()}catch(c){r=c}e.call(t.prototype)}else{try{throw Error()}catch(c){r=c}e()}}catch(c){if(c&&r&&typeof c.stack=="string"){for(var o=c.stack.split(`
`),s=r.stack.split(`
`),i=o.length-1,l=s.length-1;1<=i&&0<=l&&o[i]!==s[l];)l--;for(;1<=i&&0<=l;i--,l--)if(o[i]!==s[l]){if(i!==1||l!==1)do if(i--,l--,0>l||o[i]!==s[l]){var a=`
`+o[i].replace(" at new "," at ");return e.displayName&&a.includes("<anonymous>")&&(a=a.replace("<anonymous>",e.displayName)),a}while(1<=i&&0<=l);break}}}finally{ta=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?Qo(e):""}function ux(e){switch(e.tag){case 5:return Qo(e.type);case 16:return Qo("Lazy");case 13:return Qo("Suspense");case 19:return Qo("SuspenseList");case 0:case 2:case 15:return e=na(e.type,!1),e;case 11:return e=na(e.type.render,!1),e;case 1:return e=na(e.type,!0),e;default:return""}}function Ka(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case Or:return"Fragment";case _r:return"Portal";case Ba:return"Profiler";case Sc:return"StrictMode";case Ha:return"Suspense";case Wa:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case Sh:return(e.displayName||"Context")+".Consumer";case wh:return(e._context.displayName||"Context")+".Provider";case Cc:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ec:return t=e.displayName||null,t!==null?t:Ka(e.type)||"Memo";case vn:t=e._payload,e=e._init;try{return Ka(e(t))}catch{}}return null}function cx(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return Ka(t);case 8:return t===Sc?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function zn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Eh(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function dx(e){var t=Eh(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var o=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(i){r=""+i,s.call(this,i)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(i){r=""+i},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Ys(e){e._valueTracker||(e._valueTracker=dx(e))}function bh(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=Eh(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Di(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function Qa(e,t){var n=t.checked;return ve({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function qd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=zn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function Nh(e,t){t=t.checked,t!=null&&wc(e,"checked",t,!1)}function Ga(e,t){Nh(e,t);var n=zn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?qa(e,t.type,n):t.hasOwnProperty("defaultValue")&&qa(e,t.type,zn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Yd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function qa(e,t,n){(t!=="number"||Di(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var Go=Array.isArray;function Br(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+zn(n),t=null,o=0;o<e.length;o++){if(e[o].value===n){e[o].selected=!0,r&&(e[o].defaultSelected=!0);return}t!==null||e[o].disabled||(t=e[o])}t!==null&&(t.selected=!0)}}function Ya(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(R(91));return ve({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function Xd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(R(92));if(Go(n)){if(1<n.length)throw Error(R(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:zn(n)}}function kh(e,t){var n=zn(t.value),r=zn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function Zd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function Ph(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Xa(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?Ph(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var Xs,jh=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,o){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,o)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(Xs=Xs||document.createElement("div"),Xs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=Xs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function ps(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var es={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},fx=["Webkit","ms","Moz","O"];Object.keys(es).forEach(function(e){fx.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),es[t]=es[e]})});function Th(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||es.hasOwnProperty(e)&&es[e]?(""+t).trim():t+"px"}function Rh(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,o=Th(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}var px=ve({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Za(e,t){if(t){if(px[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(R(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(R(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(R(61))}if(t.style!=null&&typeof t.style!="object")throw Error(R(62))}}function Ja(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var eu=null;function bc(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var tu=null,Hr=null,Wr=null;function Jd(e){if(e=$s(e)){if(typeof tu!="function")throw Error(R(280));var t=e.stateNode;t&&(t=Nl(t),tu(e.stateNode,e.type,t))}}function _h(e){Hr?Wr?Wr.push(e):Wr=[e]:Hr=e}function Oh(){if(Hr){var e=Hr,t=Wr;if(Wr=Hr=null,Jd(e),t)for(e=0;e<t.length;e++)Jd(t[e])}}function Mh(e,t){return e(t)}function Ah(){}var ra=!1;function Ih(e,t,n){if(ra)return e(t,n);ra=!0;try{return Mh(e,t,n)}finally{ra=!1,(Hr!==null||Wr!==null)&&(Ah(),Oh())}}function hs(e,t){var n=e.stateNode;if(n===null)return null;var r=Nl(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(R(231,t,typeof n));return n}var nu=!1;if(nn)try{var Fo={};Object.defineProperty(Fo,"passive",{get:function(){nu=!0}}),window.addEventListener("test",Fo,Fo),window.removeEventListener("test",Fo,Fo)}catch{nu=!1}function hx(e,t,n,r,o,s,i,l,a){var c=Array.prototype.slice.call(arguments,3);try{t.apply(n,c)}catch(p){this.onError(p)}}var ts=!1,Fi=null,zi=!1,ru=null,mx={onError:function(e){ts=!0,Fi=e}};function vx(e,t,n,r,o,s,i,l,a){ts=!1,Fi=null,hx.apply(mx,arguments)}function gx(e,t,n,r,o,s,i,l,a){if(vx.apply(this,arguments),ts){if(ts){var c=Fi;ts=!1,Fi=null}else throw Error(R(198));zi||(zi=!0,ru=c)}}function br(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Lh(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function ef(e){if(br(e)!==e)throw Error(R(188))}function yx(e){var t=e.alternate;if(!t){if(t=br(e),t===null)throw Error(R(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(o===null)break;var s=o.alternate;if(s===null){if(r=o.return,r!==null){n=r;continue}break}if(o.child===s.child){for(s=o.child;s;){if(s===n)return ef(o),e;if(s===r)return ef(o),t;s=s.sibling}throw Error(R(188))}if(n.return!==r.return)n=o,r=s;else{for(var i=!1,l=o.child;l;){if(l===n){i=!0,n=o,r=s;break}if(l===r){i=!0,r=o,n=s;break}l=l.sibling}if(!i){for(l=s.child;l;){if(l===n){i=!0,n=s,r=o;break}if(l===r){i=!0,r=s,n=o;break}l=l.sibling}if(!i)throw Error(R(189))}}if(n.alternate!==r)throw Error(R(190))}if(n.tag!==3)throw Error(R(188));return n.stateNode.current===n?e:t}function Dh(e){return e=yx(e),e!==null?Fh(e):null}function Fh(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Fh(e);if(t!==null)return t;e=e.sibling}return null}var zh=at.unstable_scheduleCallback,tf=at.unstable_cancelCallback,xx=at.unstable_shouldYield,wx=at.unstable_requestPaint,xe=at.unstable_now,Sx=at.unstable_getCurrentPriorityLevel,Nc=at.unstable_ImmediatePriority,$h=at.unstable_UserBlockingPriority,$i=at.unstable_NormalPriority,Cx=at.unstable_LowPriority,Uh=at.unstable_IdlePriority,Sl=null,Vt=null;function Ex(e){if(Vt&&typeof Vt.onCommitFiberRoot=="function")try{Vt.onCommitFiberRoot(Sl,e,void 0,(e.current.flags&128)===128)}catch{}}var Pt=Math.clz32?Math.clz32:kx,bx=Math.log,Nx=Math.LN2;function kx(e){return e>>>=0,e===0?32:31-(bx(e)/Nx|0)|0}var Zs=64,Js=4194304;function qo(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Ui(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,o=e.suspendedLanes,s=e.pingedLanes,i=n&268435455;if(i!==0){var l=i&~o;l!==0?r=qo(l):(s&=i,s!==0&&(r=qo(s)))}else i=n&~o,i!==0?r=qo(i):s!==0&&(r=qo(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&o)&&(o=r&-r,s=t&-t,o>=s||o===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-Pt(t),o=1<<n,r|=e[n],t&=~o;return r}function Px(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function jx(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,s=e.pendingLanes;0<s;){var i=31-Pt(s),l=1<<i,a=o[i];a===-1?(!(l&n)||l&r)&&(o[i]=Px(l,t)):a<=t&&(e.expiredLanes|=l),s&=~l}}function ou(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Vh(){var e=Zs;return Zs<<=1,!(Zs&4194240)&&(Zs=64),e}function oa(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Fs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-Pt(t),e[t]=n}function Tx(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-Pt(n),s=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~s}}function kc(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-Pt(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var ne=0;function Bh(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Hh,Pc,Wh,Kh,Qh,su=!1,ei=[],_n=null,On=null,Mn=null,ms=new Map,vs=new Map,yn=[],Rx="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function nf(e,t){switch(e){case"focusin":case"focusout":_n=null;break;case"dragenter":case"dragleave":On=null;break;case"mouseover":case"mouseout":Mn=null;break;case"pointerover":case"pointerout":ms.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":vs.delete(t.pointerId)}}function zo(e,t,n,r,o,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[o]},t!==null&&(t=$s(t),t!==null&&Pc(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,o!==null&&t.indexOf(o)===-1&&t.push(o),e)}function _x(e,t,n,r,o){switch(t){case"focusin":return _n=zo(_n,e,t,n,r,o),!0;case"dragenter":return On=zo(On,e,t,n,r,o),!0;case"mouseover":return Mn=zo(Mn,e,t,n,r,o),!0;case"pointerover":var s=o.pointerId;return ms.set(s,zo(ms.get(s)||null,e,t,n,r,o)),!0;case"gotpointercapture":return s=o.pointerId,vs.set(s,zo(vs.get(s)||null,e,t,n,r,o)),!0}return!1}function Gh(e){var t=er(e.target);if(t!==null){var n=br(t);if(n!==null){if(t=n.tag,t===13){if(t=Lh(n),t!==null){e.blockedOn=t,Qh(e.priority,function(){Wh(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Si(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=iu(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);eu=r,n.target.dispatchEvent(r),eu=null}else return t=$s(n),t!==null&&Pc(t),e.blockedOn=n,!1;t.shift()}return!0}function rf(e,t,n){Si(e)&&n.delete(t)}function Ox(){su=!1,_n!==null&&Si(_n)&&(_n=null),On!==null&&Si(On)&&(On=null),Mn!==null&&Si(Mn)&&(Mn=null),ms.forEach(rf),vs.forEach(rf)}function $o(e,t){e.blockedOn===t&&(e.blockedOn=null,su||(su=!0,at.unstable_scheduleCallback(at.unstable_NormalPriority,Ox)))}function gs(e){function t(o){return $o(o,e)}if(0<ei.length){$o(ei[0],e);for(var n=1;n<ei.length;n++){var r=ei[n];r.blockedOn===e&&(r.blockedOn=null)}}for(_n!==null&&$o(_n,e),On!==null&&$o(On,e),Mn!==null&&$o(Mn,e),ms.forEach(t),vs.forEach(t),n=0;n<yn.length;n++)r=yn[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<yn.length&&(n=yn[0],n.blockedOn===null);)Gh(n),n.blockedOn===null&&yn.shift()}var Kr=un.ReactCurrentBatchConfig,Vi=!0;function Mx(e,t,n,r){var o=ne,s=Kr.transition;Kr.transition=null;try{ne=1,jc(e,t,n,r)}finally{ne=o,Kr.transition=s}}function Ax(e,t,n,r){var o=ne,s=Kr.transition;Kr.transition=null;try{ne=4,jc(e,t,n,r)}finally{ne=o,Kr.transition=s}}function jc(e,t,n,r){if(Vi){var o=iu(e,t,n,r);if(o===null)ha(e,t,r,Bi,n),nf(e,r);else if(_x(o,e,t,n,r))r.stopPropagation();else if(nf(e,r),t&4&&-1<Rx.indexOf(e)){for(;o!==null;){var s=$s(o);if(s!==null&&Hh(s),s=iu(e,t,n,r),s===null&&ha(e,t,r,Bi,n),s===o)break;o=s}o!==null&&r.stopPropagation()}else ha(e,t,r,null,n)}}var Bi=null;function iu(e,t,n,r){if(Bi=null,e=bc(r),e=er(e),e!==null)if(t=br(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Lh(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Bi=e,null}function qh(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Sx()){case Nc:return 1;case $h:return 4;case $i:case Cx:return 16;case Uh:return 536870912;default:return 16}default:return 16}}var Tn=null,Tc=null,Ci=null;function Yh(){if(Ci)return Ci;var e,t=Tc,n=t.length,r,o="value"in Tn?Tn.value:Tn.textContent,s=o.length;for(e=0;e<n&&t[e]===o[e];e++);var i=n-e;for(r=1;r<=i&&t[n-r]===o[s-r];r++);return Ci=o.slice(e,1<r?1-r:void 0)}function Ei(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function ti(){return!0}function of(){return!1}function dt(e){function t(n,r,o,s,i){this._reactName=n,this._targetInst=o,this.type=r,this.nativeEvent=s,this.target=i,this.currentTarget=null;for(var l in e)e.hasOwnProperty(l)&&(n=e[l],this[l]=n?n(s):s[l]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?ti:of,this.isPropagationStopped=of,this}return ve(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=ti)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=ti)},persist:function(){},isPersistent:ti}),t}var ko={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Rc=dt(ko),zs=ve({},ko,{view:0,detail:0}),Ix=dt(zs),sa,ia,Uo,Cl=ve({},zs,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_c,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Uo&&(Uo&&e.type==="mousemove"?(sa=e.screenX-Uo.screenX,ia=e.screenY-Uo.screenY):ia=sa=0,Uo=e),sa)},movementY:function(e){return"movementY"in e?e.movementY:ia}}),sf=dt(Cl),Lx=ve({},Cl,{dataTransfer:0}),Dx=dt(Lx),Fx=ve({},zs,{relatedTarget:0}),la=dt(Fx),zx=ve({},ko,{animationName:0,elapsedTime:0,pseudoElement:0}),$x=dt(zx),Ux=ve({},ko,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Vx=dt(Ux),Bx=ve({},ko,{data:0}),lf=dt(Bx),Hx={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Wx={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Kx={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Qx(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Kx[e])?!!t[e]:!1}function _c(){return Qx}var Gx=ve({},zs,{key:function(e){if(e.key){var t=Hx[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Ei(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Wx[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_c,charCode:function(e){return e.type==="keypress"?Ei(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Ei(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),qx=dt(Gx),Yx=ve({},Cl,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),af=dt(Yx),Xx=ve({},zs,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_c}),Zx=dt(Xx),Jx=ve({},ko,{propertyName:0,elapsedTime:0,pseudoElement:0}),ew=dt(Jx),tw=ve({},Cl,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),nw=dt(tw),rw=[9,13,27,32],Oc=nn&&"CompositionEvent"in window,ns=null;nn&&"documentMode"in document&&(ns=document.documentMode);var ow=nn&&"TextEvent"in window&&!ns,Xh=nn&&(!Oc||ns&&8<ns&&11>=ns),uf=" ",cf=!1;function Zh(e,t){switch(e){case"keyup":return rw.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Jh(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Mr=!1;function sw(e,t){switch(e){case"compositionend":return Jh(t);case"keypress":return t.which!==32?null:(cf=!0,uf);case"textInput":return e=t.data,e===uf&&cf?null:e;default:return null}}function iw(e,t){if(Mr)return e==="compositionend"||!Oc&&Zh(e,t)?(e=Yh(),Ci=Tc=Tn=null,Mr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Xh&&t.locale!=="ko"?null:t.data;default:return null}}var lw={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function df(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!lw[e.type]:t==="textarea"}function em(e,t,n,r){_h(r),t=Hi(t,"onChange"),0<t.length&&(n=new Rc("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var rs=null,ys=null;function aw(e){dm(e,0)}function El(e){var t=Lr(e);if(bh(t))return e}function uw(e,t){if(e==="change")return t}var tm=!1;if(nn){var aa;if(nn){var ua="oninput"in document;if(!ua){var ff=document.createElement("div");ff.setAttribute("oninput","return;"),ua=typeof ff.oninput=="function"}aa=ua}else aa=!1;tm=aa&&(!document.documentMode||9<document.documentMode)}function pf(){rs&&(rs.detachEvent("onpropertychange",nm),ys=rs=null)}function nm(e){if(e.propertyName==="value"&&El(ys)){var t=[];em(t,ys,e,bc(e)),Ih(aw,t)}}function cw(e,t,n){e==="focusin"?(pf(),rs=t,ys=n,rs.attachEvent("onpropertychange",nm)):e==="focusout"&&pf()}function dw(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return El(ys)}function fw(e,t){if(e==="click")return El(t)}function pw(e,t){if(e==="input"||e==="change")return El(t)}function hw(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:hw;function xs(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!Va.call(t,o)||!Tt(e[o],t[o]))return!1}return!0}function hf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function mf(e,t){var n=hf(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=hf(n)}}function rm(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?rm(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function om(){for(var e=window,t=Di();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Di(e.document)}return t}function Mc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function mw(e){var t=om(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&rm(n.ownerDocument.documentElement,n)){if(r!==null&&Mc(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var o=n.textContent.length,s=Math.min(r.start,o);r=r.end===void 0?s:Math.min(r.end,o),!e.extend&&s>r&&(o=r,r=s,s=o),o=mf(n,s);var i=mf(n,r);o&&i&&(e.rangeCount!==1||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&(t=t.createRange(),t.setStart(o.node,o.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var vw=nn&&"documentMode"in document&&11>=document.documentMode,Ar=null,lu=null,os=null,au=!1;function vf(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;au||Ar==null||Ar!==Di(r)||(r=Ar,"selectionStart"in r&&Mc(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),os&&xs(os,r)||(os=r,r=Hi(lu,"onSelect"),0<r.length&&(t=new Rc("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=Ar)))}function ni(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Ir={animationend:ni("Animation","AnimationEnd"),animationiteration:ni("Animation","AnimationIteration"),animationstart:ni("Animation","AnimationStart"),transitionend:ni("Transition","TransitionEnd")},ca={},sm={};nn&&(sm=document.createElement("div").style,"AnimationEvent"in window||(delete Ir.animationend.animation,delete Ir.animationiteration.animation,delete Ir.animationstart.animation),"TransitionEvent"in window||delete Ir.transitionend.transition);function bl(e){if(ca[e])return ca[e];if(!Ir[e])return e;var t=Ir[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in sm)return ca[e]=t[n];return e}var im=bl("animationend"),lm=bl("animationiteration"),am=bl("animationstart"),um=bl("transitionend"),cm=new Map,gf="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Wn(e,t){cm.set(e,t),Er(t,[e])}for(var da=0;da<gf.length;da++){var fa=gf[da],gw=fa.toLowerCase(),yw=fa[0].toUpperCase()+fa.slice(1);Wn(gw,"on"+yw)}Wn(im,"onAnimationEnd");Wn(lm,"onAnimationIteration");Wn(am,"onAnimationStart");Wn("dblclick","onDoubleClick");Wn("focusin","onFocus");Wn("focusout","onBlur");Wn(um,"onTransitionEnd");po("onMouseEnter",["mouseout","mouseover"]);po("onMouseLeave",["mouseout","mouseover"]);po("onPointerEnter",["pointerout","pointerover"]);po("onPointerLeave",["pointerout","pointerover"]);Er("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Er("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Er("onBeforeInput",["compositionend","keypress","textInput","paste"]);Er("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Er("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Er("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Yo="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),xw=new Set("cancel close invalid load scroll toggle".split(" ").concat(Yo));function yf(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,gx(r,t,void 0,e),e.currentTarget=null}function dm(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var i=r.length-1;0<=i;i--){var l=r[i],a=l.instance,c=l.currentTarget;if(l=l.listener,a!==s&&o.isPropagationStopped())break e;yf(o,l,c),s=a}else for(i=0;i<r.length;i++){if(l=r[i],a=l.instance,c=l.currentTarget,l=l.listener,a!==s&&o.isPropagationStopped())break e;yf(o,l,c),s=a}}}if(zi)throw e=ru,zi=!1,ru=null,e}function de(e,t){var n=t[pu];n===void 0&&(n=t[pu]=new Set);var r=e+"__bubble";n.has(r)||(fm(t,e,2,!1),n.add(r))}function pa(e,t,n){var r=0;t&&(r|=4),fm(n,e,r,t)}var ri="_reactListening"+Math.random().toString(36).slice(2);function ws(e){if(!e[ri]){e[ri]=!0,xh.forEach(function(n){n!=="selectionchange"&&(xw.has(n)||pa(n,!1,e),pa(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ri]||(t[ri]=!0,pa("selectionchange",!1,t))}}function fm(e,t,n,r){switch(qh(t)){case 1:var o=Mx;break;case 4:o=Ax;break;default:o=jc}n=o.bind(null,t,n,e),o=void 0,!nu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(o=!0),r?o!==void 0?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):o!==void 0?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function ha(e,t,n,r,o){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var i=r.tag;if(i===3||i===4){var l=r.stateNode.containerInfo;if(l===o||l.nodeType===8&&l.parentNode===o)break;if(i===4)for(i=r.return;i!==null;){var a=i.tag;if((a===3||a===4)&&(a=i.stateNode.containerInfo,a===o||a.nodeType===8&&a.parentNode===o))return;i=i.return}for(;l!==null;){if(i=er(l),i===null)return;if(a=i.tag,a===5||a===6){r=s=i;continue e}l=l.parentNode}}r=r.return}Ih(function(){var c=s,p=bc(n),d=[];e:{var g=cm.get(e);if(g!==void 0){var S=Rc,w=e;switch(e){case"keypress":if(Ei(n)===0)break e;case"keydown":case"keyup":S=qx;break;case"focusin":w="focus",S=la;break;case"focusout":w="blur",S=la;break;case"beforeblur":case"afterblur":S=la;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":S=sf;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":S=Dx;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":S=Zx;break;case im:case lm:case am:S=$x;break;case um:S=ew;break;case"scroll":S=Ix;break;case"wheel":S=nw;break;case"copy":case"cut":case"paste":S=Vx;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":S=af}var m=(t&4)!==0,x=!m&&e==="scroll",v=m?g!==null?g+"Capture":null:g;m=[];for(var h=c,y;h!==null;){y=h;var C=y.stateNode;if(y.tag===5&&C!==null&&(y=C,v!==null&&(C=hs(h,v),C!=null&&m.push(Ss(h,C,y)))),x)break;h=h.return}0<m.length&&(g=new S(g,w,null,n,p),d.push({event:g,listeners:m}))}}if(!(t&7)){e:{if(g=e==="mouseover"||e==="pointerover",S=e==="mouseout"||e==="pointerout",g&&n!==eu&&(w=n.relatedTarget||n.fromElement)&&(er(w)||w[rn]))break e;if((S||g)&&(g=p.window===p?p:(g=p.ownerDocument)?g.defaultView||g.parentWindow:window,S?(w=n.relatedTarget||n.toElement,S=c,w=w?er(w):null,w!==null&&(x=br(w),w!==x||w.tag!==5&&w.tag!==6)&&(w=null)):(S=null,w=c),S!==w)){if(m=sf,C="onMouseLeave",v="onMouseEnter",h="mouse",(e==="pointerout"||e==="pointerover")&&(m=af,C="onPointerLeave",v="onPointerEnter",h="pointer"),x=S==null?g:Lr(S),y=w==null?g:Lr(w),g=new m(C,h+"leave",S,n,p),g.target=x,g.relatedTarget=y,C=null,er(p)===c&&(m=new m(v,h+"enter",w,n,p),m.target=y,m.relatedTarget=x,C=m),x=C,S&&w)t:{for(m=S,v=w,h=0,y=m;y;y=Pr(y))h++;for(y=0,C=v;C;C=Pr(C))y++;for(;0<h-y;)m=Pr(m),h--;for(;0<y-h;)v=Pr(v),y--;for(;h--;){if(m===v||v!==null&&m===v.alternate)break t;m=Pr(m),v=Pr(v)}m=null}else m=null;S!==null&&xf(d,g,S,m,!1),w!==null&&x!==null&&xf(d,x,w,m,!0)}}e:{if(g=c?Lr(c):window,S=g.nodeName&&g.nodeName.toLowerCase(),S==="select"||S==="input"&&g.type==="file")var b=uw;else if(df(g))if(tm)b=pw;else{b=dw;var k=cw}else(S=g.nodeName)&&S.toLowerCase()==="input"&&(g.type==="checkbox"||g.type==="radio")&&(b=fw);if(b&&(b=b(e,c))){em(d,b,n,p);break e}k&&k(e,g,c),e==="focusout"&&(k=g._wrapperState)&&k.controlled&&g.type==="number"&&qa(g,"number",g.value)}switch(k=c?Lr(c):window,e){case"focusin":(df(k)||k.contentEditable==="true")&&(Ar=k,lu=c,os=null);break;case"focusout":os=lu=Ar=null;break;case"mousedown":au=!0;break;case"contextmenu":case"mouseup":case"dragend":au=!1,vf(d,n,p);break;case"selectionchange":if(vw)break;case"keydown":case"keyup":vf(d,n,p)}var N;if(Oc)e:{switch(e){case"compositionstart":var P="onCompositionStart";break e;case"compositionend":P="onCompositionEnd";break e;case"compositionupdate":P="onCompositionUpdate";break e}P=void 0}else Mr?Zh(e,n)&&(P="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(P="onCompositionStart");P&&(Xh&&n.locale!=="ko"&&(Mr||P!=="onCompositionStart"?P==="onCompositionEnd"&&Mr&&(N=Yh()):(Tn=p,Tc="value"in Tn?Tn.value:Tn.textContent,Mr=!0)),k=Hi(c,P),0<k.length&&(P=new lf(P,e,null,n,p),d.push({event:P,listeners:k}),N?P.data=N:(N=Jh(n),N!==null&&(P.data=N)))),(N=ow?sw(e,n):iw(e,n))&&(c=Hi(c,"onBeforeInput"),0<c.length&&(p=new lf("onBeforeInput","beforeinput",null,n,p),d.push({event:p,listeners:c}),p.data=N))}dm(d,t)})}function Ss(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Hi(e,t){for(var n=t+"Capture",r=[];e!==null;){var o=e,s=o.stateNode;o.tag===5&&s!==null&&(o=s,s=hs(e,n),s!=null&&r.unshift(Ss(e,s,o)),s=hs(e,t),s!=null&&r.push(Ss(e,s,o))),e=e.return}return r}function Pr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function xf(e,t,n,r,o){for(var s=t._reactName,i=[];n!==null&&n!==r;){var l=n,a=l.alternate,c=l.stateNode;if(a!==null&&a===r)break;l.tag===5&&c!==null&&(l=c,o?(a=hs(n,s),a!=null&&i.unshift(Ss(n,a,l))):o||(a=hs(n,s),a!=null&&i.push(Ss(n,a,l)))),n=n.return}i.length!==0&&e.push({event:t,listeners:i})}var ww=/\r\n?/g,Sw=/\u0000|\uFFFD/g;function wf(e){return(typeof e=="string"?e:""+e).replace(ww,`
`).replace(Sw,"")}function oi(e,t,n){if(t=wf(t),wf(e)!==t&&n)throw Error(R(425))}function Wi(){}var uu=null,cu=null;function du(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var fu=typeof setTimeout=="function"?setTimeout:void 0,Cw=typeof clearTimeout=="function"?clearTimeout:void 0,Sf=typeof Promise=="function"?Promise:void 0,Ew=typeof queueMicrotask=="function"?queueMicrotask:typeof Sf<"u"?function(e){return Sf.resolve(null).then(e).catch(bw)}:fu;function bw(e){setTimeout(function(){throw e})}function ma(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&o.nodeType===8)if(n=o.data,n==="/$"){if(r===0){e.removeChild(o),gs(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=o}while(n);gs(t)}function An(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Cf(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Po=Math.random().toString(36).slice(2),$t="__reactFiber$"+Po,Cs="__reactProps$"+Po,rn="__reactContainer$"+Po,pu="__reactEvents$"+Po,Nw="__reactListeners$"+Po,kw="__reactHandles$"+Po;function er(e){var t=e[$t];if(t)return t;for(var n=e.parentNode;n;){if(t=n[rn]||n[$t]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Cf(e);e!==null;){if(n=e[$t])return n;e=Cf(e)}return t}e=n,n=e.parentNode}return null}function $s(e){return e=e[$t]||e[rn],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function Lr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(R(33))}function Nl(e){return e[Cs]||null}var hu=[],Dr=-1;function Kn(e){return{current:e}}function fe(e){0>Dr||(e.current=hu[Dr],hu[Dr]=null,Dr--)}function ae(e,t){Dr++,hu[Dr]=e.current,e.current=t}var $n={},Le=Kn($n),Ze=Kn(!1),fr=$n;function ho(e,t){var n=e.type.contextTypes;if(!n)return $n;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o={},s;for(s in n)o[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Je(e){return e=e.childContextTypes,e!=null}function Ki(){fe(Ze),fe(Le)}function Ef(e,t,n){if(Le.current!==$n)throw Error(R(168));ae(Le,t),ae(Ze,n)}function pm(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var o in r)if(!(o in t))throw Error(R(108,cx(e)||"Unknown",o));return ve({},n,r)}function Qi(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||$n,fr=Le.current,ae(Le,e),ae(Ze,Ze.current),!0}function bf(e,t,n){var r=e.stateNode;if(!r)throw Error(R(169));n?(e=pm(e,t,fr),r.__reactInternalMemoizedMergedChildContext=e,fe(Ze),fe(Le),ae(Le,e)):fe(Ze),ae(Ze,n)}var Yt=null,kl=!1,va=!1;function hm(e){Yt===null?Yt=[e]:Yt.push(e)}function Pw(e){kl=!0,hm(e)}function Qn(){if(!va&&Yt!==null){va=!0;var e=0,t=ne;try{var n=Yt;for(ne=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Yt=null,kl=!1}catch(o){throw Yt!==null&&(Yt=Yt.slice(e+1)),zh(Nc,Qn),o}finally{ne=t,va=!1}}return null}var Fr=[],zr=0,Gi=null,qi=0,ht=[],mt=0,pr=null,Zt=1,Jt="";function Yn(e,t){Fr[zr++]=qi,Fr[zr++]=Gi,Gi=e,qi=t}function mm(e,t,n){ht[mt++]=Zt,ht[mt++]=Jt,ht[mt++]=pr,pr=e;var r=Zt;e=Jt;var o=32-Pt(r)-1;r&=~(1<<o),n+=1;var s=32-Pt(t)+o;if(30<s){var i=o-o%5;s=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Zt=1<<32-Pt(t)+o|n<<o|r,Jt=s+e}else Zt=1<<s|n<<o|r,Jt=e}function Ac(e){e.return!==null&&(Yn(e,1),mm(e,1,0))}function Ic(e){for(;e===Gi;)Gi=Fr[--zr],Fr[zr]=null,qi=Fr[--zr],Fr[zr]=null;for(;e===pr;)pr=ht[--mt],ht[mt]=null,Jt=ht[--mt],ht[mt]=null,Zt=ht[--mt],ht[mt]=null}var it=null,st=null,pe=!1,Nt=null;function vm(e,t){var n=vt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Nf(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,it=e,st=An(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,it=e,st=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=pr!==null?{id:Zt,overflow:Jt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=vt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,it=e,st=null,!0):!1;default:return!1}}function mu(e){return(e.mode&1)!==0&&(e.flags&128)===0}function vu(e){if(pe){var t=st;if(t){var n=t;if(!Nf(e,t)){if(mu(e))throw Error(R(418));t=An(n.nextSibling);var r=it;t&&Nf(e,t)?vm(r,n):(e.flags=e.flags&-4097|2,pe=!1,it=e)}}else{if(mu(e))throw Error(R(418));e.flags=e.flags&-4097|2,pe=!1,it=e}}}function kf(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;it=e}function si(e){if(e!==it)return!1;if(!pe)return kf(e),pe=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!du(e.type,e.memoizedProps)),t&&(t=st)){if(mu(e))throw gm(),Error(R(418));for(;t;)vm(e,t),t=An(t.nextSibling)}if(kf(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(R(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){st=An(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}st=null}}else st=it?An(e.stateNode.nextSibling):null;return!0}function gm(){for(var e=st;e;)e=An(e.nextSibling)}function mo(){st=it=null,pe=!1}function Lc(e){Nt===null?Nt=[e]:Nt.push(e)}var jw=un.ReactCurrentBatchConfig;function Vo(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(R(309));var r=n.stateNode}if(!r)throw Error(R(147,e));var o=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(i){var l=o.refs;i===null?delete l[s]:l[s]=i},t._stringRef=s,t)}if(typeof e!="string")throw Error(R(284));if(!n._owner)throw Error(R(290,e))}return e}function ii(e,t){throw e=Object.prototype.toString.call(t),Error(R(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Pf(e){var t=e._init;return t(e._payload)}function ym(e){function t(v,h){if(e){var y=v.deletions;y===null?(v.deletions=[h],v.flags|=16):y.push(h)}}function n(v,h){if(!e)return null;for(;h!==null;)t(v,h),h=h.sibling;return null}function r(v,h){for(v=new Map;h!==null;)h.key!==null?v.set(h.key,h):v.set(h.index,h),h=h.sibling;return v}function o(v,h){return v=Fn(v,h),v.index=0,v.sibling=null,v}function s(v,h,y){return v.index=y,e?(y=v.alternate,y!==null?(y=y.index,y<h?(v.flags|=2,h):y):(v.flags|=2,h)):(v.flags|=1048576,h)}function i(v){return e&&v.alternate===null&&(v.flags|=2),v}function l(v,h,y,C){return h===null||h.tag!==6?(h=Ea(y,v.mode,C),h.return=v,h):(h=o(h,y),h.return=v,h)}function a(v,h,y,C){var b=y.type;return b===Or?p(v,h,y.props.children,C,y.key):h!==null&&(h.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===vn&&Pf(b)===h.type)?(C=o(h,y.props),C.ref=Vo(v,h,y),C.return=v,C):(C=Ri(y.type,y.key,y.props,null,v.mode,C),C.ref=Vo(v,h,y),C.return=v,C)}function c(v,h,y,C){return h===null||h.tag!==4||h.stateNode.containerInfo!==y.containerInfo||h.stateNode.implementation!==y.implementation?(h=ba(y,v.mode,C),h.return=v,h):(h=o(h,y.children||[]),h.return=v,h)}function p(v,h,y,C,b){return h===null||h.tag!==7?(h=dr(y,v.mode,C,b),h.return=v,h):(h=o(h,y),h.return=v,h)}function d(v,h,y){if(typeof h=="string"&&h!==""||typeof h=="number")return h=Ea(""+h,v.mode,y),h.return=v,h;if(typeof h=="object"&&h!==null){switch(h.$$typeof){case qs:return y=Ri(h.type,h.key,h.props,null,v.mode,y),y.ref=Vo(v,null,h),y.return=v,y;case _r:return h=ba(h,v.mode,y),h.return=v,h;case vn:var C=h._init;return d(v,C(h._payload),y)}if(Go(h)||Do(h))return h=dr(h,v.mode,y,null),h.return=v,h;ii(v,h)}return null}function g(v,h,y,C){var b=h!==null?h.key:null;if(typeof y=="string"&&y!==""||typeof y=="number")return b!==null?null:l(v,h,""+y,C);if(typeof y=="object"&&y!==null){switch(y.$$typeof){case qs:return y.key===b?a(v,h,y,C):null;case _r:return y.key===b?c(v,h,y,C):null;case vn:return b=y._init,g(v,h,b(y._payload),C)}if(Go(y)||Do(y))return b!==null?null:p(v,h,y,C,null);ii(v,y)}return null}function S(v,h,y,C,b){if(typeof C=="string"&&C!==""||typeof C=="number")return v=v.get(y)||null,l(h,v,""+C,b);if(typeof C=="object"&&C!==null){switch(C.$$typeof){case qs:return v=v.get(C.key===null?y:C.key)||null,a(h,v,C,b);case _r:return v=v.get(C.key===null?y:C.key)||null,c(h,v,C,b);case vn:var k=C._init;return S(v,h,y,k(C._payload),b)}if(Go(C)||Do(C))return v=v.get(y)||null,p(h,v,C,b,null);ii(h,C)}return null}function w(v,h,y,C){for(var b=null,k=null,N=h,P=h=0,_=null;N!==null&&P<y.length;P++){N.index>P?(_=N,N=null):_=N.sibling;var O=g(v,N,y[P],C);if(O===null){N===null&&(N=_);break}e&&N&&O.alternate===null&&t(v,N),h=s(O,h,P),k===null?b=O:k.sibling=O,k=O,N=_}if(P===y.length)return n(v,N),pe&&Yn(v,P),b;if(N===null){for(;P<y.length;P++)N=d(v,y[P],C),N!==null&&(h=s(N,h,P),k===null?b=N:k.sibling=N,k=N);return pe&&Yn(v,P),b}for(N=r(v,N);P<y.length;P++)_=S(N,v,P,y[P],C),_!==null&&(e&&_.alternate!==null&&N.delete(_.key===null?P:_.key),h=s(_,h,P),k===null?b=_:k.sibling=_,k=_);return e&&N.forEach(function(I){return t(v,I)}),pe&&Yn(v,P),b}function m(v,h,y,C){var b=Do(y);if(typeof b!="function")throw Error(R(150));if(y=b.call(y),y==null)throw Error(R(151));for(var k=b=null,N=h,P=h=0,_=null,O=y.next();N!==null&&!O.done;P++,O=y.next()){N.index>P?(_=N,N=null):_=N.sibling;var I=g(v,N,O.value,C);if(I===null){N===null&&(N=_);break}e&&N&&I.alternate===null&&t(v,N),h=s(I,h,P),k===null?b=I:k.sibling=I,k=I,N=_}if(O.done)return n(v,N),pe&&Yn(v,P),b;if(N===null){for(;!O.done;P++,O=y.next())O=d(v,O.value,C),O!==null&&(h=s(O,h,P),k===null?b=O:k.sibling=O,k=O);return pe&&Yn(v,P),b}for(N=r(v,N);!O.done;P++,O=y.next())O=S(N,v,P,O.value,C),O!==null&&(e&&O.alternate!==null&&N.delete(O.key===null?P:O.key),h=s(O,h,P),k===null?b=O:k.sibling=O,k=O);return e&&N.forEach(function(M){return t(v,M)}),pe&&Yn(v,P),b}function x(v,h,y,C){if(typeof y=="object"&&y!==null&&y.type===Or&&y.key===null&&(y=y.props.children),typeof y=="object"&&y!==null){switch(y.$$typeof){case qs:e:{for(var b=y.key,k=h;k!==null;){if(k.key===b){if(b=y.type,b===Or){if(k.tag===7){n(v,k.sibling),h=o(k,y.props.children),h.return=v,v=h;break e}}else if(k.elementType===b||typeof b=="object"&&b!==null&&b.$$typeof===vn&&Pf(b)===k.type){n(v,k.sibling),h=o(k,y.props),h.ref=Vo(v,k,y),h.return=v,v=h;break e}n(v,k);break}else t(v,k);k=k.sibling}y.type===Or?(h=dr(y.props.children,v.mode,C,y.key),h.return=v,v=h):(C=Ri(y.type,y.key,y.props,null,v.mode,C),C.ref=Vo(v,h,y),C.return=v,v=C)}return i(v);case _r:e:{for(k=y.key;h!==null;){if(h.key===k)if(h.tag===4&&h.stateNode.containerInfo===y.containerInfo&&h.stateNode.implementation===y.implementation){n(v,h.sibling),h=o(h,y.children||[]),h.return=v,v=h;break e}else{n(v,h);break}else t(v,h);h=h.sibling}h=ba(y,v.mode,C),h.return=v,v=h}return i(v);case vn:return k=y._init,x(v,h,k(y._payload),C)}if(Go(y))return w(v,h,y,C);if(Do(y))return m(v,h,y,C);ii(v,y)}return typeof y=="string"&&y!==""||typeof y=="number"?(y=""+y,h!==null&&h.tag===6?(n(v,h.sibling),h=o(h,y),h.return=v,v=h):(n(v,h),h=Ea(y,v.mode,C),h.return=v,v=h),i(v)):n(v,h)}return x}var vo=ym(!0),xm=ym(!1),Yi=Kn(null),Xi=null,$r=null,Dc=null;function Fc(){Dc=$r=Xi=null}function zc(e){var t=Yi.current;fe(Yi),e._currentValue=t}function gu(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Qr(e,t){Xi=e,Dc=$r=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Xe=!0),e.firstContext=null)}function yt(e){var t=e._currentValue;if(Dc!==e)if(e={context:e,memoizedValue:t,next:null},$r===null){if(Xi===null)throw Error(R(308));$r=e,Xi.dependencies={lanes:0,firstContext:e}}else $r=$r.next=e;return t}var tr=null;function $c(e){tr===null?tr=[e]:tr.push(e)}function wm(e,t,n,r){var o=t.interleaved;return o===null?(n.next=n,$c(t)):(n.next=o.next,o.next=n),t.interleaved=n,on(e,r)}function on(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var gn=!1;function Uc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Sm(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function en(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function In(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,J&2){var o=r.pending;return o===null?t.next=t:(t.next=o.next,o.next=t),r.pending=t,on(e,n)}return o=r.interleaved,o===null?(t.next=t,$c(r)):(t.next=o.next,o.next=t),r.interleaved=t,on(e,n)}function bi(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,kc(e,n)}}function jf(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var o=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?o=s=i:s=s.next=i,n=n.next}while(n!==null);s===null?o=s=t:s=s.next=t}else o=s=t;n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Zi(e,t,n,r){var o=e.updateQueue;gn=!1;var s=o.firstBaseUpdate,i=o.lastBaseUpdate,l=o.shared.pending;if(l!==null){o.shared.pending=null;var a=l,c=a.next;a.next=null,i===null?s=c:i.next=c,i=a;var p=e.alternate;p!==null&&(p=p.updateQueue,l=p.lastBaseUpdate,l!==i&&(l===null?p.firstBaseUpdate=c:l.next=c,p.lastBaseUpdate=a))}if(s!==null){var d=o.baseState;i=0,p=c=a=null,l=s;do{var g=l.lane,S=l.eventTime;if((r&g)===g){p!==null&&(p=p.next={eventTime:S,lane:0,tag:l.tag,payload:l.payload,callback:l.callback,next:null});e:{var w=e,m=l;switch(g=t,S=n,m.tag){case 1:if(w=m.payload,typeof w=="function"){d=w.call(S,d,g);break e}d=w;break e;case 3:w.flags=w.flags&-65537|128;case 0:if(w=m.payload,g=typeof w=="function"?w.call(S,d,g):w,g==null)break e;d=ve({},d,g);break e;case 2:gn=!0}}l.callback!==null&&l.lane!==0&&(e.flags|=64,g=o.effects,g===null?o.effects=[l]:g.push(l))}else S={eventTime:S,lane:g,tag:l.tag,payload:l.payload,callback:l.callback,next:null},p===null?(c=p=S,a=d):p=p.next=S,i|=g;if(l=l.next,l===null){if(l=o.shared.pending,l===null)break;g=l,l=g.next,g.next=null,o.lastBaseUpdate=g,o.shared.pending=null}}while(!0);if(p===null&&(a=d),o.baseState=a,o.firstBaseUpdate=c,o.lastBaseUpdate=p,t=o.shared.interleaved,t!==null){o=t;do i|=o.lane,o=o.next;while(o!==t)}else s===null&&(o.shared.lanes=0);mr|=i,e.lanes=i,e.memoizedState=d}}function Tf(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(o!==null){if(r.callback=null,r=n,typeof o!="function")throw Error(R(191,o));o.call(r)}}}var Us={},Bt=Kn(Us),Es=Kn(Us),bs=Kn(Us);function nr(e){if(e===Us)throw Error(R(174));return e}function Vc(e,t){switch(ae(bs,t),ae(Es,e),ae(Bt,Us),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Xa(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Xa(t,e)}fe(Bt),ae(Bt,t)}function go(){fe(Bt),fe(Es),fe(bs)}function Cm(e){nr(bs.current);var t=nr(Bt.current),n=Xa(t,e.type);t!==n&&(ae(Es,e),ae(Bt,n))}function Bc(e){Es.current===e&&(fe(Bt),fe(Es))}var he=Kn(0);function Ji(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ga=[];function Hc(){for(var e=0;e<ga.length;e++)ga[e]._workInProgressVersionPrimary=null;ga.length=0}var Ni=un.ReactCurrentDispatcher,ya=un.ReactCurrentBatchConfig,hr=0,me=null,Ee=null,ke=null,el=!1,ss=!1,Ns=0,Tw=0;function Oe(){throw Error(R(321))}function Wc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Tt(e[n],t[n]))return!1;return!0}function Kc(e,t,n,r,o,s){if(hr=s,me=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Ni.current=e===null||e.memoizedState===null?Mw:Aw,e=n(r,o),ss){s=0;do{if(ss=!1,Ns=0,25<=s)throw Error(R(301));s+=1,ke=Ee=null,t.updateQueue=null,Ni.current=Iw,e=n(r,o)}while(ss)}if(Ni.current=tl,t=Ee!==null&&Ee.next!==null,hr=0,ke=Ee=me=null,el=!1,t)throw Error(R(300));return e}function Qc(){var e=Ns!==0;return Ns=0,e}function It(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return ke===null?me.memoizedState=ke=e:ke=ke.next=e,ke}function xt(){if(Ee===null){var e=me.alternate;e=e!==null?e.memoizedState:null}else e=Ee.next;var t=ke===null?me.memoizedState:ke.next;if(t!==null)ke=t,Ee=e;else{if(e===null)throw Error(R(310));Ee=e,e={memoizedState:Ee.memoizedState,baseState:Ee.baseState,baseQueue:Ee.baseQueue,queue:Ee.queue,next:null},ke===null?me.memoizedState=ke=e:ke=ke.next=e}return ke}function ks(e,t){return typeof t=="function"?t(e):t}function xa(e){var t=xt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=Ee,o=r.baseQueue,s=n.pending;if(s!==null){if(o!==null){var i=o.next;o.next=s.next,s.next=i}r.baseQueue=o=s,n.pending=null}if(o!==null){s=o.next,r=r.baseState;var l=i=null,a=null,c=s;do{var p=c.lane;if((hr&p)===p)a!==null&&(a=a.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var d={lane:p,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};a===null?(l=a=d,i=r):a=a.next=d,me.lanes|=p,mr|=p}c=c.next}while(c!==null&&c!==s);a===null?i=r:a.next=l,Tt(r,t.memoizedState)||(Xe=!0),t.memoizedState=r,t.baseState=i,t.baseQueue=a,n.lastRenderedState=r}if(e=n.interleaved,e!==null){o=e;do s=o.lane,me.lanes|=s,mr|=s,o=o.next;while(o!==e)}else o===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function wa(e){var t=xt(),n=t.queue;if(n===null)throw Error(R(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,s=t.memoizedState;if(o!==null){n.pending=null;var i=o=o.next;do s=e(s,i.action),i=i.next;while(i!==o);Tt(s,t.memoizedState)||(Xe=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function Em(){}function bm(e,t){var n=me,r=xt(),o=t(),s=!Tt(r.memoizedState,o);if(s&&(r.memoizedState=o,Xe=!0),r=r.queue,Gc(Pm.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||ke!==null&&ke.memoizedState.tag&1){if(n.flags|=2048,Ps(9,km.bind(null,n,r,o,t),void 0,null),Pe===null)throw Error(R(349));hr&30||Nm(n,t,o)}return o}function Nm(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function km(e,t,n,r){t.value=n,t.getSnapshot=r,jm(t)&&Tm(e)}function Pm(e,t,n){return n(function(){jm(t)&&Tm(e)})}function jm(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Tt(e,n)}catch{return!0}}function Tm(e){var t=on(e,1);t!==null&&jt(t,e,1,-1)}function Rf(e){var t=It();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:ks,lastRenderedState:e},t.queue=e,e=e.dispatch=Ow.bind(null,me,e),[t.memoizedState,e]}function Ps(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=me.updateQueue,t===null?(t={lastEffect:null,stores:null},me.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function Rm(){return xt().memoizedState}function ki(e,t,n,r){var o=It();me.flags|=e,o.memoizedState=Ps(1|t,n,void 0,r===void 0?null:r)}function Pl(e,t,n,r){var o=xt();r=r===void 0?null:r;var s=void 0;if(Ee!==null){var i=Ee.memoizedState;if(s=i.destroy,r!==null&&Wc(r,i.deps)){o.memoizedState=Ps(t,n,s,r);return}}me.flags|=e,o.memoizedState=Ps(1|t,n,s,r)}function _f(e,t){return ki(8390656,8,e,t)}function Gc(e,t){return Pl(2048,8,e,t)}function _m(e,t){return Pl(4,2,e,t)}function Om(e,t){return Pl(4,4,e,t)}function Mm(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Am(e,t,n){return n=n!=null?n.concat([e]):null,Pl(4,4,Mm.bind(null,t,e),n)}function qc(){}function Im(e,t){var n=xt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Lm(e,t){var n=xt();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&Wc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Dm(e,t,n){return hr&21?(Tt(n,t)||(n=Vh(),me.lanes|=n,mr|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Xe=!0),e.memoizedState=n)}function Rw(e,t){var n=ne;ne=n!==0&&4>n?n:4,e(!0);var r=ya.transition;ya.transition={};try{e(!1),t()}finally{ne=n,ya.transition=r}}function Fm(){return xt().memoizedState}function _w(e,t,n){var r=Dn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},zm(e))$m(t,n);else if(n=wm(e,t,n,r),n!==null){var o=He();jt(n,e,r,o),Um(n,t,r)}}function Ow(e,t,n){var r=Dn(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(zm(e))$m(t,o);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var i=t.lastRenderedState,l=s(i,n);if(o.hasEagerState=!0,o.eagerState=l,Tt(l,i)){var a=t.interleaved;a===null?(o.next=o,$c(t)):(o.next=a.next,a.next=o),t.interleaved=o;return}}catch{}finally{}n=wm(e,t,o,r),n!==null&&(o=He(),jt(n,e,r,o),Um(n,t,r))}}function zm(e){var t=e.alternate;return e===me||t!==null&&t===me}function $m(e,t){ss=el=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Um(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,kc(e,n)}}var tl={readContext:yt,useCallback:Oe,useContext:Oe,useEffect:Oe,useImperativeHandle:Oe,useInsertionEffect:Oe,useLayoutEffect:Oe,useMemo:Oe,useReducer:Oe,useRef:Oe,useState:Oe,useDebugValue:Oe,useDeferredValue:Oe,useTransition:Oe,useMutableSource:Oe,useSyncExternalStore:Oe,useId:Oe,unstable_isNewReconciler:!1},Mw={readContext:yt,useCallback:function(e,t){return It().memoizedState=[e,t===void 0?null:t],e},useContext:yt,useEffect:_f,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,ki(4194308,4,Mm.bind(null,t,e),n)},useLayoutEffect:function(e,t){return ki(4194308,4,e,t)},useInsertionEffect:function(e,t){return ki(4,2,e,t)},useMemo:function(e,t){var n=It();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=It();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=_w.bind(null,me,e),[r.memoizedState,e]},useRef:function(e){var t=It();return e={current:e},t.memoizedState=e},useState:Rf,useDebugValue:qc,useDeferredValue:function(e){return It().memoizedState=e},useTransition:function(){var e=Rf(!1),t=e[0];return e=Rw.bind(null,e[1]),It().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=me,o=It();if(pe){if(n===void 0)throw Error(R(407));n=n()}else{if(n=t(),Pe===null)throw Error(R(349));hr&30||Nm(r,t,n)}o.memoizedState=n;var s={value:n,getSnapshot:t};return o.queue=s,_f(Pm.bind(null,r,s,e),[e]),r.flags|=2048,Ps(9,km.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=It(),t=Pe.identifierPrefix;if(pe){var n=Jt,r=Zt;n=(r&~(1<<32-Pt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Ns++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=Tw++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},Aw={readContext:yt,useCallback:Im,useContext:yt,useEffect:Gc,useImperativeHandle:Am,useInsertionEffect:_m,useLayoutEffect:Om,useMemo:Lm,useReducer:xa,useRef:Rm,useState:function(){return xa(ks)},useDebugValue:qc,useDeferredValue:function(e){var t=xt();return Dm(t,Ee.memoizedState,e)},useTransition:function(){var e=xa(ks)[0],t=xt().memoizedState;return[e,t]},useMutableSource:Em,useSyncExternalStore:bm,useId:Fm,unstable_isNewReconciler:!1},Iw={readContext:yt,useCallback:Im,useContext:yt,useEffect:Gc,useImperativeHandle:Am,useInsertionEffect:_m,useLayoutEffect:Om,useMemo:Lm,useReducer:wa,useRef:Rm,useState:function(){return wa(ks)},useDebugValue:qc,useDeferredValue:function(e){var t=xt();return Ee===null?t.memoizedState=e:Dm(t,Ee.memoizedState,e)},useTransition:function(){var e=wa(ks)[0],t=xt().memoizedState;return[e,t]},useMutableSource:Em,useSyncExternalStore:bm,useId:Fm,unstable_isNewReconciler:!1};function Ct(e,t){if(e&&e.defaultProps){t=ve({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function yu(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:ve({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var jl={isMounted:function(e){return(e=e._reactInternals)?br(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=He(),o=Dn(e),s=en(r,o);s.payload=t,n!=null&&(s.callback=n),t=In(e,s,o),t!==null&&(jt(t,e,o,r),bi(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=He(),o=Dn(e),s=en(r,o);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=In(e,s,o),t!==null&&(jt(t,e,o,r),bi(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=He(),r=Dn(e),o=en(n,r);o.tag=2,t!=null&&(o.callback=t),t=In(e,o,r),t!==null&&(jt(t,e,r,n),bi(t,e,r))}};function Of(e,t,n,r,o,s,i){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,i):t.prototype&&t.prototype.isPureReactComponent?!xs(n,r)||!xs(o,s):!0}function Vm(e,t,n){var r=!1,o=$n,s=t.contextType;return typeof s=="object"&&s!==null?s=yt(s):(o=Je(t)?fr:Le.current,r=t.contextTypes,s=(r=r!=null)?ho(e,o):$n),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=jl,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=s),t}function Mf(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&jl.enqueueReplaceState(t,t.state,null)}function xu(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Uc(e);var s=t.contextType;typeof s=="object"&&s!==null?o.context=yt(s):(s=Je(t)?fr:Le.current,o.context=ho(e,s)),o.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(yu(e,t,s,n),o.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof o.getSnapshotBeforeUpdate=="function"||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(t=o.state,typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount(),t!==o.state&&jl.enqueueReplaceState(o,o.state,null),Zi(e,n,o,r),o.state=e.memoizedState),typeof o.componentDidMount=="function"&&(e.flags|=4194308)}function yo(e,t){try{var n="",r=t;do n+=ux(r),r=r.return;while(r);var o=n}catch(s){o=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:o,digest:null}}function Sa(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function wu(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var Lw=typeof WeakMap=="function"?WeakMap:Map;function Bm(e,t,n){n=en(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){rl||(rl=!0,Ru=r),wu(e,t)},n}function Hm(e,t,n){n=en(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){wu(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){wu(e,t),typeof r!="function"&&(Ln===null?Ln=new Set([this]):Ln.add(this));var i=t.stack;this.componentDidCatch(t.value,{componentStack:i!==null?i:""})}),n}function Af(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new Lw;var o=new Set;r.set(t,o)}else o=r.get(t),o===void 0&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Yw.bind(null,e,t,n),t.then(e,e))}function If(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Lf(e,t,n,r,o){return e.mode&1?(e.flags|=65536,e.lanes=o,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=en(-1,1),t.tag=2,In(n,t,1))),n.lanes|=1),e)}var Dw=un.ReactCurrentOwner,Xe=!1;function Ve(e,t,n,r){t.child=e===null?xm(t,null,n,r):vo(t,e.child,n,r)}function Df(e,t,n,r,o){n=n.render;var s=t.ref;return Qr(t,o),r=Kc(e,t,n,r,s,o),n=Qc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,sn(e,t,o)):(pe&&n&&Ac(t),t.flags|=1,Ve(e,t,r,o),t.child)}function Ff(e,t,n,r,o){if(e===null){var s=n.type;return typeof s=="function"&&!rd(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,Wm(e,t,s,r,o)):(e=Ri(n.type,null,r,t,t.mode,o),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&o)){var i=s.memoizedProps;if(n=n.compare,n=n!==null?n:xs,n(i,r)&&e.ref===t.ref)return sn(e,t,o)}return t.flags|=1,e=Fn(s,r),e.ref=t.ref,e.return=t,t.child=e}function Wm(e,t,n,r,o){if(e!==null){var s=e.memoizedProps;if(xs(s,r)&&e.ref===t.ref)if(Xe=!1,t.pendingProps=r=s,(e.lanes&o)!==0)e.flags&131072&&(Xe=!0);else return t.lanes=e.lanes,sn(e,t,o)}return Su(e,t,n,r,o)}function Km(e,t,n){var r=t.pendingProps,o=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},ae(Vr,rt),rt|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,ae(Vr,rt),rt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,ae(Vr,rt),rt|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,ae(Vr,rt),rt|=r;return Ve(e,t,o,n),t.child}function Qm(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Su(e,t,n,r,o){var s=Je(n)?fr:Le.current;return s=ho(t,s),Qr(t,o),n=Kc(e,t,n,r,s,o),r=Qc(),e!==null&&!Xe?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,sn(e,t,o)):(pe&&r&&Ac(t),t.flags|=1,Ve(e,t,n,o),t.child)}function zf(e,t,n,r,o){if(Je(n)){var s=!0;Qi(t)}else s=!1;if(Qr(t,o),t.stateNode===null)Pi(e,t),Vm(t,n,r),xu(t,n,r,o),r=!0;else if(e===null){var i=t.stateNode,l=t.memoizedProps;i.props=l;var a=i.context,c=n.contextType;typeof c=="object"&&c!==null?c=yt(c):(c=Je(n)?fr:Le.current,c=ho(t,c));var p=n.getDerivedStateFromProps,d=typeof p=="function"||typeof i.getSnapshotBeforeUpdate=="function";d||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==r||a!==c)&&Mf(t,i,r,c),gn=!1;var g=t.memoizedState;i.state=g,Zi(t,r,i,o),a=t.memoizedState,l!==r||g!==a||Ze.current||gn?(typeof p=="function"&&(yu(t,n,p,r),a=t.memoizedState),(l=gn||Of(t,n,l,r,g,a,c))?(d||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount()),typeof i.componentDidMount=="function"&&(t.flags|=4194308)):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=a),i.props=r,i.state=a,i.context=c,r=l):(typeof i.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Sm(e,t),l=t.memoizedProps,c=t.type===t.elementType?l:Ct(t.type,l),i.props=c,d=t.pendingProps,g=i.context,a=n.contextType,typeof a=="object"&&a!==null?a=yt(a):(a=Je(n)?fr:Le.current,a=ho(t,a));var S=n.getDerivedStateFromProps;(p=typeof S=="function"||typeof i.getSnapshotBeforeUpdate=="function")||typeof i.UNSAFE_componentWillReceiveProps!="function"&&typeof i.componentWillReceiveProps!="function"||(l!==d||g!==a)&&Mf(t,i,r,a),gn=!1,g=t.memoizedState,i.state=g,Zi(t,r,i,o);var w=t.memoizedState;l!==d||g!==w||Ze.current||gn?(typeof S=="function"&&(yu(t,n,S,r),w=t.memoizedState),(c=gn||Of(t,n,c,r,g,w,a)||!1)?(p||typeof i.UNSAFE_componentWillUpdate!="function"&&typeof i.componentWillUpdate!="function"||(typeof i.componentWillUpdate=="function"&&i.componentWillUpdate(r,w,a),typeof i.UNSAFE_componentWillUpdate=="function"&&i.UNSAFE_componentWillUpdate(r,w,a)),typeof i.componentDidUpdate=="function"&&(t.flags|=4),typeof i.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=w),i.props=r,i.state=w,i.context=a,r=c):(typeof i.componentDidUpdate!="function"||l===e.memoizedProps&&g===e.memoizedState||(t.flags|=4),typeof i.getSnapshotBeforeUpdate!="function"||l===e.memoizedProps&&g===e.memoizedState||(t.flags|=1024),r=!1)}return Cu(e,t,n,r,s,o)}function Cu(e,t,n,r,o,s){Qm(e,t);var i=(t.flags&128)!==0;if(!r&&!i)return o&&bf(t,n,!1),sn(e,t,s);r=t.stateNode,Dw.current=t;var l=i&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&i?(t.child=vo(t,e.child,null,s),t.child=vo(t,null,l,s)):Ve(e,t,l,s),t.memoizedState=r.state,o&&bf(t,n,!0),t.child}function Gm(e){var t=e.stateNode;t.pendingContext?Ef(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Ef(e,t.context,!1),Vc(e,t.containerInfo)}function $f(e,t,n,r,o){return mo(),Lc(o),t.flags|=256,Ve(e,t,n,r),t.child}var Eu={dehydrated:null,treeContext:null,retryLane:0};function bu(e){return{baseLanes:e,cachePool:null,transitions:null}}function qm(e,t,n){var r=t.pendingProps,o=he.current,s=!1,i=(t.flags&128)!==0,l;if((l=i)||(l=e!==null&&e.memoizedState===null?!1:(o&2)!==0),l?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(o|=1),ae(he,o&1),e===null)return vu(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(i=r.children,e=r.fallback,s?(r=t.mode,s=t.child,i={mode:"hidden",children:i},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=i):s=_l(i,r,0,null),e=dr(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=bu(n),t.memoizedState=Eu,e):Yc(t,i));if(o=e.memoizedState,o!==null&&(l=o.dehydrated,l!==null))return Fw(e,t,i,r,l,o,n);if(s){s=r.fallback,i=t.mode,o=e.child,l=o.sibling;var a={mode:"hidden",children:r.children};return!(i&1)&&t.child!==o?(r=t.child,r.childLanes=0,r.pendingProps=a,t.deletions=null):(r=Fn(o,a),r.subtreeFlags=o.subtreeFlags&14680064),l!==null?s=Fn(l,s):(s=dr(s,i,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,i=e.child.memoizedState,i=i===null?bu(n):{baseLanes:i.baseLanes|n,cachePool:null,transitions:i.transitions},s.memoizedState=i,s.childLanes=e.childLanes&~n,t.memoizedState=Eu,r}return s=e.child,e=s.sibling,r=Fn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function Yc(e,t){return t=_l({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function li(e,t,n,r){return r!==null&&Lc(r),vo(t,e.child,null,n),e=Yc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Fw(e,t,n,r,o,s,i){if(n)return t.flags&256?(t.flags&=-257,r=Sa(Error(R(422))),li(e,t,i,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,o=t.mode,r=_l({mode:"visible",children:r.children},o,0,null),s=dr(s,o,i,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&vo(t,e.child,null,i),t.child.memoizedState=bu(i),t.memoizedState=Eu,s);if(!(t.mode&1))return li(e,t,i,null);if(o.data==="$!"){if(r=o.nextSibling&&o.nextSibling.dataset,r)var l=r.dgst;return r=l,s=Error(R(419)),r=Sa(s,r,void 0),li(e,t,i,r)}if(l=(i&e.childLanes)!==0,Xe||l){if(r=Pe,r!==null){switch(i&-i){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}o=o&(r.suspendedLanes|i)?0:o,o!==0&&o!==s.retryLane&&(s.retryLane=o,on(e,o),jt(r,e,o,-1))}return nd(),r=Sa(Error(R(421))),li(e,t,i,r)}return o.data==="$?"?(t.flags|=128,t.child=e.child,t=Xw.bind(null,e),o._reactRetry=t,null):(e=s.treeContext,st=An(o.nextSibling),it=t,pe=!0,Nt=null,e!==null&&(ht[mt++]=Zt,ht[mt++]=Jt,ht[mt++]=pr,Zt=e.id,Jt=e.overflow,pr=t),t=Yc(t,r.children),t.flags|=4096,t)}function Uf(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),gu(e.return,t,n)}function Ca(e,t,n,r,o){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=o)}function Ym(e,t,n){var r=t.pendingProps,o=r.revealOrder,s=r.tail;if(Ve(e,t,r.children,n),r=he.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Uf(e,n,t);else if(e.tag===19)Uf(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(ae(he,r),!(t.mode&1))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;n!==null;)e=n.alternate,e!==null&&Ji(e)===null&&(o=n),n=n.sibling;n=o,n===null?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Ca(t,!1,o,n,s);break;case"backwards":for(n=null,o=t.child,t.child=null;o!==null;){if(e=o.alternate,e!==null&&Ji(e)===null){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Ca(t,!0,n,null,s);break;case"together":Ca(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Pi(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function sn(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),mr|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(R(153));if(t.child!==null){for(e=t.child,n=Fn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=Fn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function zw(e,t,n){switch(t.tag){case 3:Gm(t),mo();break;case 5:Cm(t);break;case 1:Je(t.type)&&Qi(t);break;case 4:Vc(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;ae(Yi,r._currentValue),r._currentValue=o;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(ae(he,he.current&1),t.flags|=128,null):n&t.child.childLanes?qm(e,t,n):(ae(he,he.current&1),e=sn(e,t,n),e!==null?e.sibling:null);ae(he,he.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return Ym(e,t,n);t.flags|=128}if(o=t.memoizedState,o!==null&&(o.rendering=null,o.tail=null,o.lastEffect=null),ae(he,he.current),r)break;return null;case 22:case 23:return t.lanes=0,Km(e,t,n)}return sn(e,t,n)}var Xm,Nu,Zm,Jm;Xm=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Nu=function(){};Zm=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,nr(Bt.current);var s=null;switch(n){case"input":o=Qa(e,o),r=Qa(e,r),s=[];break;case"select":o=ve({},o,{value:void 0}),r=ve({},r,{value:void 0}),s=[];break;case"textarea":o=Ya(e,o),r=Ya(e,r),s=[];break;default:typeof o.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=Wi)}Za(n,r);var i;n=null;for(c in o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&o[c]!=null)if(c==="style"){var l=o[c];for(i in l)l.hasOwnProperty(i)&&(n||(n={}),n[i]="")}else c!=="dangerouslySetInnerHTML"&&c!=="children"&&c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&c!=="autoFocus"&&(fs.hasOwnProperty(c)?s||(s=[]):(s=s||[]).push(c,null));for(c in r){var a=r[c];if(l=o!=null?o[c]:void 0,r.hasOwnProperty(c)&&a!==l&&(a!=null||l!=null))if(c==="style")if(l){for(i in l)!l.hasOwnProperty(i)||a&&a.hasOwnProperty(i)||(n||(n={}),n[i]="");for(i in a)a.hasOwnProperty(i)&&l[i]!==a[i]&&(n||(n={}),n[i]=a[i])}else n||(s||(s=[]),s.push(c,n)),n=a;else c==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,l=l?l.__html:void 0,a!=null&&l!==a&&(s=s||[]).push(c,a)):c==="children"?typeof a!="string"&&typeof a!="number"||(s=s||[]).push(c,""+a):c!=="suppressContentEditableWarning"&&c!=="suppressHydrationWarning"&&(fs.hasOwnProperty(c)?(a!=null&&c==="onScroll"&&de("scroll",e),s||l===a||(s=[])):(s=s||[]).push(c,a))}n&&(s=s||[]).push("style",n);var c=s;(t.updateQueue=c)&&(t.flags|=4)}};Jm=function(e,t,n,r){n!==r&&(t.flags|=4)};function Bo(e,t){if(!pe)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Me(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags&14680064,r|=o.flags&14680064,o.return=e,o=o.sibling;else for(o=e.child;o!==null;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function $w(e,t,n){var r=t.pendingProps;switch(Ic(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Me(t),null;case 1:return Je(t.type)&&Ki(),Me(t),null;case 3:return r=t.stateNode,go(),fe(Ze),fe(Le),Hc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(si(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,Nt!==null&&(Mu(Nt),Nt=null))),Nu(e,t),Me(t),null;case 5:Bc(t);var o=nr(bs.current);if(n=t.type,e!==null&&t.stateNode!=null)Zm(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(R(166));return Me(t),null}if(e=nr(Bt.current),si(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[$t]=t,r[Cs]=s,e=(t.mode&1)!==0,n){case"dialog":de("cancel",r),de("close",r);break;case"iframe":case"object":case"embed":de("load",r);break;case"video":case"audio":for(o=0;o<Yo.length;o++)de(Yo[o],r);break;case"source":de("error",r);break;case"img":case"image":case"link":de("error",r),de("load",r);break;case"details":de("toggle",r);break;case"input":qd(r,s),de("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},de("invalid",r);break;case"textarea":Xd(r,s),de("invalid",r)}Za(n,s),o=null;for(var i in s)if(s.hasOwnProperty(i)){var l=s[i];i==="children"?typeof l=="string"?r.textContent!==l&&(s.suppressHydrationWarning!==!0&&oi(r.textContent,l,e),o=["children",l]):typeof l=="number"&&r.textContent!==""+l&&(s.suppressHydrationWarning!==!0&&oi(r.textContent,l,e),o=["children",""+l]):fs.hasOwnProperty(i)&&l!=null&&i==="onScroll"&&de("scroll",r)}switch(n){case"input":Ys(r),Yd(r,s,!0);break;case"textarea":Ys(r),Zd(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=Wi)}r=o,t.updateQueue=r,r!==null&&(t.flags|=4)}else{i=o.nodeType===9?o:o.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=Ph(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=i.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=i.createElement(n,{is:r.is}):(e=i.createElement(n),n==="select"&&(i=e,r.multiple?i.multiple=!0:r.size&&(i.size=r.size))):e=i.createElementNS(e,n),e[$t]=t,e[Cs]=r,Xm(e,t,!1,!1),t.stateNode=e;e:{switch(i=Ja(n,r),n){case"dialog":de("cancel",e),de("close",e),o=r;break;case"iframe":case"object":case"embed":de("load",e),o=r;break;case"video":case"audio":for(o=0;o<Yo.length;o++)de(Yo[o],e);o=r;break;case"source":de("error",e),o=r;break;case"img":case"image":case"link":de("error",e),de("load",e),o=r;break;case"details":de("toggle",e),o=r;break;case"input":qd(e,r),o=Qa(e,r),de("invalid",e);break;case"option":o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=ve({},r,{value:void 0}),de("invalid",e);break;case"textarea":Xd(e,r),o=Ya(e,r),de("invalid",e);break;default:o=r}Za(n,o),l=o;for(s in l)if(l.hasOwnProperty(s)){var a=l[s];s==="style"?Rh(e,a):s==="dangerouslySetInnerHTML"?(a=a?a.__html:void 0,a!=null&&jh(e,a)):s==="children"?typeof a=="string"?(n!=="textarea"||a!=="")&&ps(e,a):typeof a=="number"&&ps(e,""+a):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(fs.hasOwnProperty(s)?a!=null&&s==="onScroll"&&de("scroll",e):a!=null&&wc(e,s,a,i))}switch(n){case"input":Ys(e),Yd(e,r,!1);break;case"textarea":Ys(e),Zd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+zn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Br(e,!!r.multiple,s,!1):r.defaultValue!=null&&Br(e,!!r.multiple,r.defaultValue,!0);break;default:typeof o.onClick=="function"&&(e.onclick=Wi)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return Me(t),null;case 6:if(e&&t.stateNode!=null)Jm(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(R(166));if(n=nr(bs.current),nr(Bt.current),si(t)){if(r=t.stateNode,n=t.memoizedProps,r[$t]=t,(s=r.nodeValue!==n)&&(e=it,e!==null))switch(e.tag){case 3:oi(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&oi(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[$t]=t,t.stateNode=r}return Me(t),null;case 13:if(fe(he),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(pe&&st!==null&&t.mode&1&&!(t.flags&128))gm(),mo(),t.flags|=98560,s=!1;else if(s=si(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(R(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(R(317));s[$t]=t}else mo(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;Me(t),s=!1}else Nt!==null&&(Mu(Nt),Nt=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||he.current&1?Ne===0&&(Ne=3):nd())),t.updateQueue!==null&&(t.flags|=4),Me(t),null);case 4:return go(),Nu(e,t),e===null&&ws(t.stateNode.containerInfo),Me(t),null;case 10:return zc(t.type._context),Me(t),null;case 17:return Je(t.type)&&Ki(),Me(t),null;case 19:if(fe(he),s=t.memoizedState,s===null)return Me(t),null;if(r=(t.flags&128)!==0,i=s.rendering,i===null)if(r)Bo(s,!1);else{if(Ne!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(i=Ji(e),i!==null){for(t.flags|=128,Bo(s,!1),r=i.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,i=s.alternate,i===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=i.childLanes,s.lanes=i.lanes,s.child=i.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=i.memoizedProps,s.memoizedState=i.memoizedState,s.updateQueue=i.updateQueue,s.type=i.type,e=i.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return ae(he,he.current&1|2),t.child}e=e.sibling}s.tail!==null&&xe()>xo&&(t.flags|=128,r=!0,Bo(s,!1),t.lanes=4194304)}else{if(!r)if(e=Ji(i),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),Bo(s,!0),s.tail===null&&s.tailMode==="hidden"&&!i.alternate&&!pe)return Me(t),null}else 2*xe()-s.renderingStartTime>xo&&n!==1073741824&&(t.flags|=128,r=!0,Bo(s,!1),t.lanes=4194304);s.isBackwards?(i.sibling=t.child,t.child=i):(n=s.last,n!==null?n.sibling=i:t.child=i,s.last=i)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=xe(),t.sibling=null,n=he.current,ae(he,r?n&1|2:n&1),t):(Me(t),null);case 22:case 23:return td(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?rt&1073741824&&(Me(t),t.subtreeFlags&6&&(t.flags|=8192)):Me(t),null;case 24:return null;case 25:return null}throw Error(R(156,t.tag))}function Uw(e,t){switch(Ic(t),t.tag){case 1:return Je(t.type)&&Ki(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return go(),fe(Ze),fe(Le),Hc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return Bc(t),null;case 13:if(fe(he),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(R(340));mo()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return fe(he),null;case 4:return go(),null;case 10:return zc(t.type._context),null;case 22:case 23:return td(),null;case 24:return null;default:return null}}var ai=!1,Ie=!1,Vw=typeof WeakSet=="function"?WeakSet:Set,F=null;function Ur(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ye(e,t,r)}else n.current=null}function ku(e,t,n){try{n()}catch(r){ye(e,t,r)}}var Vf=!1;function Bw(e,t){if(uu=Vi,e=om(),Mc(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var o=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var i=0,l=-1,a=-1,c=0,p=0,d=e,g=null;t:for(;;){for(var S;d!==n||o!==0&&d.nodeType!==3||(l=i+o),d!==s||r!==0&&d.nodeType!==3||(a=i+r),d.nodeType===3&&(i+=d.nodeValue.length),(S=d.firstChild)!==null;)g=d,d=S;for(;;){if(d===e)break t;if(g===n&&++c===o&&(l=i),g===s&&++p===r&&(a=i),(S=d.nextSibling)!==null)break;d=g,g=d.parentNode}d=S}n=l===-1||a===-1?null:{start:l,end:a}}else n=null}n=n||{start:0,end:0}}else n=null;for(cu={focusedElem:e,selectionRange:n},Vi=!1,F=t;F!==null;)if(t=F,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,F=e;else for(;F!==null;){t=F;try{var w=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(w!==null){var m=w.memoizedProps,x=w.memoizedState,v=t.stateNode,h=v.getSnapshotBeforeUpdate(t.elementType===t.type?m:Ct(t.type,m),x);v.__reactInternalSnapshotBeforeUpdate=h}break;case 3:var y=t.stateNode.containerInfo;y.nodeType===1?y.textContent="":y.nodeType===9&&y.documentElement&&y.removeChild(y.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(R(163))}}catch(C){ye(t,t.return,C)}if(e=t.sibling,e!==null){e.return=t.return,F=e;break}F=t.return}return w=Vf,Vf=!1,w}function is(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var o=r=r.next;do{if((o.tag&e)===e){var s=o.destroy;o.destroy=void 0,s!==void 0&&ku(t,n,s)}o=o.next}while(o!==r)}}function Tl(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Pu(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function ev(e){var t=e.alternate;t!==null&&(e.alternate=null,ev(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[$t],delete t[Cs],delete t[pu],delete t[Nw],delete t[kw])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function tv(e){return e.tag===5||e.tag===3||e.tag===4}function Bf(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||tv(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function ju(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=Wi));else if(r!==4&&(e=e.child,e!==null))for(ju(e,t,n),e=e.sibling;e!==null;)ju(e,t,n),e=e.sibling}function Tu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(Tu(e,t,n),e=e.sibling;e!==null;)Tu(e,t,n),e=e.sibling}var je=null,bt=!1;function cn(e,t,n){for(n=n.child;n!==null;)nv(e,t,n),n=n.sibling}function nv(e,t,n){if(Vt&&typeof Vt.onCommitFiberUnmount=="function")try{Vt.onCommitFiberUnmount(Sl,n)}catch{}switch(n.tag){case 5:Ie||Ur(n,t);case 6:var r=je,o=bt;je=null,cn(e,t,n),je=r,bt=o,je!==null&&(bt?(e=je,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):je.removeChild(n.stateNode));break;case 18:je!==null&&(bt?(e=je,n=n.stateNode,e.nodeType===8?ma(e.parentNode,n):e.nodeType===1&&ma(e,n),gs(e)):ma(je,n.stateNode));break;case 4:r=je,o=bt,je=n.stateNode.containerInfo,bt=!0,cn(e,t,n),je=r,bt=o;break;case 0:case 11:case 14:case 15:if(!Ie&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){o=r=r.next;do{var s=o,i=s.destroy;s=s.tag,i!==void 0&&(s&2||s&4)&&ku(n,t,i),o=o.next}while(o!==r)}cn(e,t,n);break;case 1:if(!Ie&&(Ur(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(l){ye(n,t,l)}cn(e,t,n);break;case 21:cn(e,t,n);break;case 22:n.mode&1?(Ie=(r=Ie)||n.memoizedState!==null,cn(e,t,n),Ie=r):cn(e,t,n);break;default:cn(e,t,n)}}function Hf(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new Vw),t.forEach(function(r){var o=Zw.bind(null,e,r);n.has(r)||(n.add(r),r.then(o,o))})}}function wt(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var o=n[r];try{var s=e,i=t,l=i;e:for(;l!==null;){switch(l.tag){case 5:je=l.stateNode,bt=!1;break e;case 3:je=l.stateNode.containerInfo,bt=!0;break e;case 4:je=l.stateNode.containerInfo,bt=!0;break e}l=l.return}if(je===null)throw Error(R(160));nv(s,i,o),je=null,bt=!1;var a=o.alternate;a!==null&&(a.return=null),o.return=null}catch(c){ye(o,t,c)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)rv(t,e),t=t.sibling}function rv(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(wt(t,e),At(e),r&4){try{is(3,e,e.return),Tl(3,e)}catch(m){ye(e,e.return,m)}try{is(5,e,e.return)}catch(m){ye(e,e.return,m)}}break;case 1:wt(t,e),At(e),r&512&&n!==null&&Ur(n,n.return);break;case 5:if(wt(t,e),At(e),r&512&&n!==null&&Ur(n,n.return),e.flags&32){var o=e.stateNode;try{ps(o,"")}catch(m){ye(e,e.return,m)}}if(r&4&&(o=e.stateNode,o!=null)){var s=e.memoizedProps,i=n!==null?n.memoizedProps:s,l=e.type,a=e.updateQueue;if(e.updateQueue=null,a!==null)try{l==="input"&&s.type==="radio"&&s.name!=null&&Nh(o,s),Ja(l,i);var c=Ja(l,s);for(i=0;i<a.length;i+=2){var p=a[i],d=a[i+1];p==="style"?Rh(o,d):p==="dangerouslySetInnerHTML"?jh(o,d):p==="children"?ps(o,d):wc(o,p,d,c)}switch(l){case"input":Ga(o,s);break;case"textarea":kh(o,s);break;case"select":var g=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!s.multiple;var S=s.value;S!=null?Br(o,!!s.multiple,S,!1):g!==!!s.multiple&&(s.defaultValue!=null?Br(o,!!s.multiple,s.defaultValue,!0):Br(o,!!s.multiple,s.multiple?[]:"",!1))}o[Cs]=s}catch(m){ye(e,e.return,m)}}break;case 6:if(wt(t,e),At(e),r&4){if(e.stateNode===null)throw Error(R(162));o=e.stateNode,s=e.memoizedProps;try{o.nodeValue=s}catch(m){ye(e,e.return,m)}}break;case 3:if(wt(t,e),At(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{gs(t.containerInfo)}catch(m){ye(e,e.return,m)}break;case 4:wt(t,e),At(e);break;case 13:wt(t,e),At(e),o=e.child,o.flags&8192&&(s=o.memoizedState!==null,o.stateNode.isHidden=s,!s||o.alternate!==null&&o.alternate.memoizedState!==null||(Jc=xe())),r&4&&Hf(e);break;case 22:if(p=n!==null&&n.memoizedState!==null,e.mode&1?(Ie=(c=Ie)||p,wt(t,e),Ie=c):wt(t,e),At(e),r&8192){if(c=e.memoizedState!==null,(e.stateNode.isHidden=c)&&!p&&e.mode&1)for(F=e,p=e.child;p!==null;){for(d=F=p;F!==null;){switch(g=F,S=g.child,g.tag){case 0:case 11:case 14:case 15:is(4,g,g.return);break;case 1:Ur(g,g.return);var w=g.stateNode;if(typeof w.componentWillUnmount=="function"){r=g,n=g.return;try{t=r,w.props=t.memoizedProps,w.state=t.memoizedState,w.componentWillUnmount()}catch(m){ye(r,n,m)}}break;case 5:Ur(g,g.return);break;case 22:if(g.memoizedState!==null){Kf(d);continue}}S!==null?(S.return=g,F=S):Kf(d)}p=p.sibling}e:for(p=null,d=e;;){if(d.tag===5){if(p===null){p=d;try{o=d.stateNode,c?(s=o.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(l=d.stateNode,a=d.memoizedProps.style,i=a!=null&&a.hasOwnProperty("display")?a.display:null,l.style.display=Th("display",i))}catch(m){ye(e,e.return,m)}}}else if(d.tag===6){if(p===null)try{d.stateNode.nodeValue=c?"":d.memoizedProps}catch(m){ye(e,e.return,m)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;p===d&&(p=null),d=d.return}p===d&&(p=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:wt(t,e),At(e),r&4&&Hf(e);break;case 21:break;default:wt(t,e),At(e)}}function At(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(tv(n)){var r=n;break e}n=n.return}throw Error(R(160))}switch(r.tag){case 5:var o=r.stateNode;r.flags&32&&(ps(o,""),r.flags&=-33);var s=Bf(e);Tu(e,s,o);break;case 3:case 4:var i=r.stateNode.containerInfo,l=Bf(e);ju(e,l,i);break;default:throw Error(R(161))}}catch(a){ye(e,e.return,a)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function Hw(e,t,n){F=e,ov(e)}function ov(e,t,n){for(var r=(e.mode&1)!==0;F!==null;){var o=F,s=o.child;if(o.tag===22&&r){var i=o.memoizedState!==null||ai;if(!i){var l=o.alternate,a=l!==null&&l.memoizedState!==null||Ie;l=ai;var c=Ie;if(ai=i,(Ie=a)&&!c)for(F=o;F!==null;)i=F,a=i.child,i.tag===22&&i.memoizedState!==null?Qf(o):a!==null?(a.return=i,F=a):Qf(o);for(;s!==null;)F=s,ov(s),s=s.sibling;F=o,ai=l,Ie=c}Wf(e)}else o.subtreeFlags&8772&&s!==null?(s.return=o,F=s):Wf(e)}}function Wf(e){for(;F!==null;){var t=F;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Ie||Tl(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Ie)if(n===null)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:Ct(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Tf(t,s,r);break;case 3:var i=t.updateQueue;if(i!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Tf(t,i,n)}break;case 5:var l=t.stateNode;if(n===null&&t.flags&4){n=l;var a=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":a.autoFocus&&n.focus();break;case"img":a.src&&(n.src=a.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var c=t.alternate;if(c!==null){var p=c.memoizedState;if(p!==null){var d=p.dehydrated;d!==null&&gs(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(R(163))}Ie||t.flags&512&&Pu(t)}catch(g){ye(t,t.return,g)}}if(t===e){F=null;break}if(n=t.sibling,n!==null){n.return=t.return,F=n;break}F=t.return}}function Kf(e){for(;F!==null;){var t=F;if(t===e){F=null;break}var n=t.sibling;if(n!==null){n.return=t.return,F=n;break}F=t.return}}function Qf(e){for(;F!==null;){var t=F;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Tl(4,t)}catch(a){ye(t,n,a)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var o=t.return;try{r.componentDidMount()}catch(a){ye(t,o,a)}}var s=t.return;try{Pu(t)}catch(a){ye(t,s,a)}break;case 5:var i=t.return;try{Pu(t)}catch(a){ye(t,i,a)}}}catch(a){ye(t,t.return,a)}if(t===e){F=null;break}var l=t.sibling;if(l!==null){l.return=t.return,F=l;break}F=t.return}}var Ww=Math.ceil,nl=un.ReactCurrentDispatcher,Xc=un.ReactCurrentOwner,gt=un.ReactCurrentBatchConfig,J=0,Pe=null,Se=null,Te=0,rt=0,Vr=Kn(0),Ne=0,js=null,mr=0,Rl=0,Zc=0,ls=null,qe=null,Jc=0,xo=1/0,qt=null,rl=!1,Ru=null,Ln=null,ui=!1,Rn=null,ol=0,as=0,_u=null,ji=-1,Ti=0;function He(){return J&6?xe():ji!==-1?ji:ji=xe()}function Dn(e){return e.mode&1?J&2&&Te!==0?Te&-Te:jw.transition!==null?(Ti===0&&(Ti=Vh()),Ti):(e=ne,e!==0||(e=window.event,e=e===void 0?16:qh(e.type)),e):1}function jt(e,t,n,r){if(50<as)throw as=0,_u=null,Error(R(185));Fs(e,n,r),(!(J&2)||e!==Pe)&&(e===Pe&&(!(J&2)&&(Rl|=n),Ne===4&&xn(e,Te)),et(e,r),n===1&&J===0&&!(t.mode&1)&&(xo=xe()+500,kl&&Qn()))}function et(e,t){var n=e.callbackNode;jx(e,t);var r=Ui(e,e===Pe?Te:0);if(r===0)n!==null&&tf(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&tf(n),t===1)e.tag===0?Pw(Gf.bind(null,e)):hm(Gf.bind(null,e)),Ew(function(){!(J&6)&&Qn()}),n=null;else{switch(Bh(r)){case 1:n=Nc;break;case 4:n=$h;break;case 16:n=$i;break;case 536870912:n=Uh;break;default:n=$i}n=fv(n,sv.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function sv(e,t){if(ji=-1,Ti=0,J&6)throw Error(R(327));var n=e.callbackNode;if(Gr()&&e.callbackNode!==n)return null;var r=Ui(e,e===Pe?Te:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=sl(e,r);else{t=r;var o=J;J|=2;var s=lv();(Pe!==e||Te!==t)&&(qt=null,xo=xe()+500,cr(e,t));do try{Gw();break}catch(l){iv(e,l)}while(!0);Fc(),nl.current=s,J=o,Se!==null?t=0:(Pe=null,Te=0,t=Ne)}if(t!==0){if(t===2&&(o=ou(e),o!==0&&(r=o,t=Ou(e,o))),t===1)throw n=js,cr(e,0),xn(e,r),et(e,xe()),n;if(t===6)xn(e,r);else{if(o=e.current.alternate,!(r&30)&&!Kw(o)&&(t=sl(e,r),t===2&&(s=ou(e),s!==0&&(r=s,t=Ou(e,s))),t===1))throw n=js,cr(e,0),xn(e,r),et(e,xe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(R(345));case 2:Xn(e,qe,qt);break;case 3:if(xn(e,r),(r&130023424)===r&&(t=Jc+500-xe(),10<t)){if(Ui(e,0)!==0)break;if(o=e.suspendedLanes,(o&r)!==r){He(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=fu(Xn.bind(null,e,qe,qt),t);break}Xn(e,qe,qt);break;case 4:if(xn(e,r),(r&4194240)===r)break;for(t=e.eventTimes,o=-1;0<r;){var i=31-Pt(r);s=1<<i,i=t[i],i>o&&(o=i),r&=~s}if(r=o,r=xe()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*Ww(r/1960))-r,10<r){e.timeoutHandle=fu(Xn.bind(null,e,qe,qt),r);break}Xn(e,qe,qt);break;case 5:Xn(e,qe,qt);break;default:throw Error(R(329))}}}return et(e,xe()),e.callbackNode===n?sv.bind(null,e):null}function Ou(e,t){var n=ls;return e.current.memoizedState.isDehydrated&&(cr(e,t).flags|=256),e=sl(e,t),e!==2&&(t=qe,qe=n,t!==null&&Mu(t)),e}function Mu(e){qe===null?qe=e:qe.push.apply(qe,e)}function Kw(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var o=n[r],s=o.getSnapshot;o=o.value;try{if(!Tt(s(),o))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function xn(e,t){for(t&=~Zc,t&=~Rl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-Pt(t),r=1<<n;e[n]=-1,t&=~r}}function Gf(e){if(J&6)throw Error(R(327));Gr();var t=Ui(e,0);if(!(t&1))return et(e,xe()),null;var n=sl(e,t);if(e.tag!==0&&n===2){var r=ou(e);r!==0&&(t=r,n=Ou(e,r))}if(n===1)throw n=js,cr(e,0),xn(e,t),et(e,xe()),n;if(n===6)throw Error(R(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,Xn(e,qe,qt),et(e,xe()),null}function ed(e,t){var n=J;J|=1;try{return e(t)}finally{J=n,J===0&&(xo=xe()+500,kl&&Qn())}}function vr(e){Rn!==null&&Rn.tag===0&&!(J&6)&&Gr();var t=J;J|=1;var n=gt.transition,r=ne;try{if(gt.transition=null,ne=1,e)return e()}finally{ne=r,gt.transition=n,J=t,!(J&6)&&Qn()}}function td(){rt=Vr.current,fe(Vr)}function cr(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Cw(n)),Se!==null)for(n=Se.return;n!==null;){var r=n;switch(Ic(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&Ki();break;case 3:go(),fe(Ze),fe(Le),Hc();break;case 5:Bc(r);break;case 4:go();break;case 13:fe(he);break;case 19:fe(he);break;case 10:zc(r.type._context);break;case 22:case 23:td()}n=n.return}if(Pe=e,Se=e=Fn(e.current,null),Te=rt=t,Ne=0,js=null,Zc=Rl=mr=0,qe=ls=null,tr!==null){for(t=0;t<tr.length;t++)if(n=tr[t],r=n.interleaved,r!==null){n.interleaved=null;var o=r.next,s=n.pending;if(s!==null){var i=s.next;s.next=o,r.next=i}n.pending=r}tr=null}return e}function iv(e,t){do{var n=Se;try{if(Fc(),Ni.current=tl,el){for(var r=me.memoizedState;r!==null;){var o=r.queue;o!==null&&(o.pending=null),r=r.next}el=!1}if(hr=0,ke=Ee=me=null,ss=!1,Ns=0,Xc.current=null,n===null||n.return===null){Ne=1,js=t,Se=null;break}e:{var s=e,i=n.return,l=n,a=t;if(t=Te,l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){var c=a,p=l,d=p.tag;if(!(p.mode&1)&&(d===0||d===11||d===15)){var g=p.alternate;g?(p.updateQueue=g.updateQueue,p.memoizedState=g.memoizedState,p.lanes=g.lanes):(p.updateQueue=null,p.memoizedState=null)}var S=If(i);if(S!==null){S.flags&=-257,Lf(S,i,l,s,t),S.mode&1&&Af(s,c,t),t=S,a=c;var w=t.updateQueue;if(w===null){var m=new Set;m.add(a),t.updateQueue=m}else w.add(a);break e}else{if(!(t&1)){Af(s,c,t),nd();break e}a=Error(R(426))}}else if(pe&&l.mode&1){var x=If(i);if(x!==null){!(x.flags&65536)&&(x.flags|=256),Lf(x,i,l,s,t),Lc(yo(a,l));break e}}s=a=yo(a,l),Ne!==4&&(Ne=2),ls===null?ls=[s]:ls.push(s),s=i;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var v=Bm(s,a,t);jf(s,v);break e;case 1:l=a;var h=s.type,y=s.stateNode;if(!(s.flags&128)&&(typeof h.getDerivedStateFromError=="function"||y!==null&&typeof y.componentDidCatch=="function"&&(Ln===null||!Ln.has(y)))){s.flags|=65536,t&=-t,s.lanes|=t;var C=Hm(s,l,t);jf(s,C);break e}}s=s.return}while(s!==null)}uv(n)}catch(b){t=b,Se===n&&n!==null&&(Se=n=n.return);continue}break}while(!0)}function lv(){var e=nl.current;return nl.current=tl,e===null?tl:e}function nd(){(Ne===0||Ne===3||Ne===2)&&(Ne=4),Pe===null||!(mr&268435455)&&!(Rl&268435455)||xn(Pe,Te)}function sl(e,t){var n=J;J|=2;var r=lv();(Pe!==e||Te!==t)&&(qt=null,cr(e,t));do try{Qw();break}catch(o){iv(e,o)}while(!0);if(Fc(),J=n,nl.current=r,Se!==null)throw Error(R(261));return Pe=null,Te=0,Ne}function Qw(){for(;Se!==null;)av(Se)}function Gw(){for(;Se!==null&&!xx();)av(Se)}function av(e){var t=dv(e.alternate,e,rt);e.memoizedProps=e.pendingProps,t===null?uv(e):Se=t,Xc.current=null}function uv(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=Uw(n,t),n!==null){n.flags&=32767,Se=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Ne=6,Se=null;return}}else if(n=$w(n,t,rt),n!==null){Se=n;return}if(t=t.sibling,t!==null){Se=t;return}Se=t=e}while(t!==null);Ne===0&&(Ne=5)}function Xn(e,t,n){var r=ne,o=gt.transition;try{gt.transition=null,ne=1,qw(e,t,n,r)}finally{gt.transition=o,ne=r}return null}function qw(e,t,n,r){do Gr();while(Rn!==null);if(J&6)throw Error(R(327));n=e.finishedWork;var o=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(R(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Tx(e,s),e===Pe&&(Se=Pe=null,Te=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||ui||(ui=!0,fv($i,function(){return Gr(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=gt.transition,gt.transition=null;var i=ne;ne=1;var l=J;J|=4,Xc.current=null,Bw(e,n),rv(n,e),mw(cu),Vi=!!uu,cu=uu=null,e.current=n,Hw(n),wx(),J=l,ne=i,gt.transition=s}else e.current=n;if(ui&&(ui=!1,Rn=e,ol=o),s=e.pendingLanes,s===0&&(Ln=null),Ex(n.stateNode),et(e,xe()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(rl)throw rl=!1,e=Ru,Ru=null,e;return ol&1&&e.tag!==0&&Gr(),s=e.pendingLanes,s&1?e===_u?as++:(as=0,_u=e):as=0,Qn(),null}function Gr(){if(Rn!==null){var e=Bh(ol),t=gt.transition,n=ne;try{if(gt.transition=null,ne=16>e?16:e,Rn===null)var r=!1;else{if(e=Rn,Rn=null,ol=0,J&6)throw Error(R(331));var o=J;for(J|=4,F=e.current;F!==null;){var s=F,i=s.child;if(F.flags&16){var l=s.deletions;if(l!==null){for(var a=0;a<l.length;a++){var c=l[a];for(F=c;F!==null;){var p=F;switch(p.tag){case 0:case 11:case 15:is(8,p,s)}var d=p.child;if(d!==null)d.return=p,F=d;else for(;F!==null;){p=F;var g=p.sibling,S=p.return;if(ev(p),p===c){F=null;break}if(g!==null){g.return=S,F=g;break}F=S}}}var w=s.alternate;if(w!==null){var m=w.child;if(m!==null){w.child=null;do{var x=m.sibling;m.sibling=null,m=x}while(m!==null)}}F=s}}if(s.subtreeFlags&2064&&i!==null)i.return=s,F=i;else e:for(;F!==null;){if(s=F,s.flags&2048)switch(s.tag){case 0:case 11:case 15:is(9,s,s.return)}var v=s.sibling;if(v!==null){v.return=s.return,F=v;break e}F=s.return}}var h=e.current;for(F=h;F!==null;){i=F;var y=i.child;if(i.subtreeFlags&2064&&y!==null)y.return=i,F=y;else e:for(i=h;F!==null;){if(l=F,l.flags&2048)try{switch(l.tag){case 0:case 11:case 15:Tl(9,l)}}catch(b){ye(l,l.return,b)}if(l===i){F=null;break e}var C=l.sibling;if(C!==null){C.return=l.return,F=C;break e}F=l.return}}if(J=o,Qn(),Vt&&typeof Vt.onPostCommitFiberRoot=="function")try{Vt.onPostCommitFiberRoot(Sl,e)}catch{}r=!0}return r}finally{ne=n,gt.transition=t}}return!1}function qf(e,t,n){t=yo(n,t),t=Bm(e,t,1),e=In(e,t,1),t=He(),e!==null&&(Fs(e,1,t),et(e,t))}function ye(e,t,n){if(e.tag===3)qf(e,e,n);else for(;t!==null;){if(t.tag===3){qf(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(Ln===null||!Ln.has(r))){e=yo(n,e),e=Hm(t,e,1),t=In(t,e,1),e=He(),t!==null&&(Fs(t,1,e),et(t,e));break}}t=t.return}}function Yw(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=He(),e.pingedLanes|=e.suspendedLanes&n,Pe===e&&(Te&n)===n&&(Ne===4||Ne===3&&(Te&130023424)===Te&&500>xe()-Jc?cr(e,0):Zc|=n),et(e,t)}function cv(e,t){t===0&&(e.mode&1?(t=Js,Js<<=1,!(Js&130023424)&&(Js=4194304)):t=1);var n=He();e=on(e,t),e!==null&&(Fs(e,t,n),et(e,n))}function Xw(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),cv(e,n)}function Zw(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;o!==null&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(R(314))}r!==null&&r.delete(t),cv(e,n)}var dv;dv=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ze.current)Xe=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Xe=!1,zw(e,t,n);Xe=!!(e.flags&131072)}else Xe=!1,pe&&t.flags&1048576&&mm(t,qi,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Pi(e,t),e=t.pendingProps;var o=ho(t,Le.current);Qr(t,n),o=Kc(null,t,r,e,o,n);var s=Qc();return t.flags|=1,typeof o=="object"&&o!==null&&typeof o.render=="function"&&o.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Je(r)?(s=!0,Qi(t)):s=!1,t.memoizedState=o.state!==null&&o.state!==void 0?o.state:null,Uc(t),o.updater=jl,t.stateNode=o,o._reactInternals=t,xu(t,r,e,n),t=Cu(null,t,r,!0,s,n)):(t.tag=0,pe&&s&&Ac(t),Ve(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Pi(e,t),e=t.pendingProps,o=r._init,r=o(r._payload),t.type=r,o=t.tag=e1(r),e=Ct(r,e),o){case 0:t=Su(null,t,r,e,n);break e;case 1:t=zf(null,t,r,e,n);break e;case 11:t=Df(null,t,r,e,n);break e;case 14:t=Ff(null,t,r,Ct(r.type,e),n);break e}throw Error(R(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Su(e,t,r,o,n);case 1:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),zf(e,t,r,o,n);case 3:e:{if(Gm(t),e===null)throw Error(R(387));r=t.pendingProps,s=t.memoizedState,o=s.element,Sm(e,t),Zi(t,r,null,n);var i=t.memoizedState;if(r=i.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:i.cache,pendingSuspenseBoundaries:i.pendingSuspenseBoundaries,transitions:i.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){o=yo(Error(R(423)),t),t=$f(e,t,r,n,o);break e}else if(r!==o){o=yo(Error(R(424)),t),t=$f(e,t,r,n,o);break e}else for(st=An(t.stateNode.containerInfo.firstChild),it=t,pe=!0,Nt=null,n=xm(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(mo(),r===o){t=sn(e,t,n);break e}Ve(e,t,r,n)}t=t.child}return t;case 5:return Cm(t),e===null&&vu(t),r=t.type,o=t.pendingProps,s=e!==null?e.memoizedProps:null,i=o.children,du(r,o)?i=null:s!==null&&du(r,s)&&(t.flags|=32),Qm(e,t),Ve(e,t,i,n),t.child;case 6:return e===null&&vu(t),null;case 13:return qm(e,t,n);case 4:return Vc(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=vo(t,null,r,n):Ve(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Df(e,t,r,o,n);case 7:return Ve(e,t,t.pendingProps,n),t.child;case 8:return Ve(e,t,t.pendingProps.children,n),t.child;case 12:return Ve(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,s=t.memoizedProps,i=o.value,ae(Yi,r._currentValue),r._currentValue=i,s!==null)if(Tt(s.value,i)){if(s.children===o.children&&!Ze.current){t=sn(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var l=s.dependencies;if(l!==null){i=s.child;for(var a=l.firstContext;a!==null;){if(a.context===r){if(s.tag===1){a=en(-1,n&-n),a.tag=2;var c=s.updateQueue;if(c!==null){c=c.shared;var p=c.pending;p===null?a.next=a:(a.next=p.next,p.next=a),c.pending=a}}s.lanes|=n,a=s.alternate,a!==null&&(a.lanes|=n),gu(s.return,n,t),l.lanes|=n;break}a=a.next}}else if(s.tag===10)i=s.type===t.type?null:s.child;else if(s.tag===18){if(i=s.return,i===null)throw Error(R(341));i.lanes|=n,l=i.alternate,l!==null&&(l.lanes|=n),gu(i,n,t),i=s.sibling}else i=s.child;if(i!==null)i.return=s;else for(i=s;i!==null;){if(i===t){i=null;break}if(s=i.sibling,s!==null){s.return=i.return,i=s;break}i=i.return}s=i}Ve(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Qr(t,n),o=yt(o),r=r(o),t.flags|=1,Ve(e,t,r,n),t.child;case 14:return r=t.type,o=Ct(r,t.pendingProps),o=Ct(r.type,o),Ff(e,t,r,o,n);case 15:return Wm(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:Ct(r,o),Pi(e,t),t.tag=1,Je(r)?(e=!0,Qi(t)):e=!1,Qr(t,n),Vm(t,r,o),xu(t,r,o,n),Cu(null,t,r,!0,e,n);case 19:return Ym(e,t,n);case 22:return Km(e,t,n)}throw Error(R(156,t.tag))};function fv(e,t){return zh(e,t)}function Jw(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function vt(e,t,n,r){return new Jw(e,t,n,r)}function rd(e){return e=e.prototype,!(!e||!e.isReactComponent)}function e1(e){if(typeof e=="function")return rd(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Cc)return 11;if(e===Ec)return 14}return 2}function Fn(e,t){var n=e.alternate;return n===null?(n=vt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Ri(e,t,n,r,o,s){var i=2;if(r=e,typeof e=="function")rd(e)&&(i=1);else if(typeof e=="string")i=5;else e:switch(e){case Or:return dr(n.children,o,s,t);case Sc:i=8,o|=8;break;case Ba:return e=vt(12,n,t,o|2),e.elementType=Ba,e.lanes=s,e;case Ha:return e=vt(13,n,t,o),e.elementType=Ha,e.lanes=s,e;case Wa:return e=vt(19,n,t,o),e.elementType=Wa,e.lanes=s,e;case Ch:return _l(n,o,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case wh:i=10;break e;case Sh:i=9;break e;case Cc:i=11;break e;case Ec:i=14;break e;case vn:i=16,r=null;break e}throw Error(R(130,e==null?e:typeof e,""))}return t=vt(i,n,t,o),t.elementType=e,t.type=r,t.lanes=s,t}function dr(e,t,n,r){return e=vt(7,e,r,t),e.lanes=n,e}function _l(e,t,n,r){return e=vt(22,e,r,t),e.elementType=Ch,e.lanes=n,e.stateNode={isHidden:!1},e}function Ea(e,t,n){return e=vt(6,e,null,t),e.lanes=n,e}function ba(e,t,n){return t=vt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function t1(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=oa(0),this.expirationTimes=oa(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=oa(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function od(e,t,n,r,o,s,i,l,a){return e=new t1(e,t,n,l,a),t===1?(t=1,s===!0&&(t|=8)):t=0,s=vt(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Uc(s),e}function n1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:_r,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function pv(e){if(!e)return $n;e=e._reactInternals;e:{if(br(e)!==e||e.tag!==1)throw Error(R(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Je(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(R(171))}if(e.tag===1){var n=e.type;if(Je(n))return pm(e,n,t)}return t}function hv(e,t,n,r,o,s,i,l,a){return e=od(n,r,!0,e,o,s,i,l,a),e.context=pv(null),n=e.current,r=He(),o=Dn(n),s=en(r,o),s.callback=t??null,In(n,s,o),e.current.lanes=o,Fs(e,o,r),et(e,r),e}function Ol(e,t,n,r){var o=t.current,s=He(),i=Dn(o);return n=pv(n),t.context===null?t.context=n:t.pendingContext=n,t=en(s,i),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=In(o,t,i),e!==null&&(jt(e,o,i,s),bi(e,o,i)),i}function il(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function Yf(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function sd(e,t){Yf(e,t),(e=e.alternate)&&Yf(e,t)}function r1(){return null}var mv=typeof reportError=="function"?reportError:function(e){console.error(e)};function id(e){this._internalRoot=e}Ml.prototype.render=id.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(R(409));Ol(e,t,null,null)};Ml.prototype.unmount=id.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;vr(function(){Ol(null,e,null,null)}),t[rn]=null}};function Ml(e){this._internalRoot=e}Ml.prototype.unstable_scheduleHydration=function(e){if(e){var t=Kh();e={blockedOn:null,target:e,priority:t};for(var n=0;n<yn.length&&t!==0&&t<yn[n].priority;n++);yn.splice(n,0,e),n===0&&Gh(e)}};function ld(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Al(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function Xf(){}function o1(e,t,n,r,o){if(o){if(typeof r=="function"){var s=r;r=function(){var c=il(i);s.call(c)}}var i=hv(t,r,e,0,null,!1,!1,"",Xf);return e._reactRootContainer=i,e[rn]=i.current,ws(e.nodeType===8?e.parentNode:e),vr(),i}for(;o=e.lastChild;)e.removeChild(o);if(typeof r=="function"){var l=r;r=function(){var c=il(a);l.call(c)}}var a=od(e,0,!1,null,null,!1,!1,"",Xf);return e._reactRootContainer=a,e[rn]=a.current,ws(e.nodeType===8?e.parentNode:e),vr(function(){Ol(t,a,n,r)}),a}function Il(e,t,n,r,o){var s=n._reactRootContainer;if(s){var i=s;if(typeof o=="function"){var l=o;o=function(){var a=il(i);l.call(a)}}Ol(t,i,e,o)}else i=o1(n,t,e,o,r);return il(i)}Hh=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=qo(t.pendingLanes);n!==0&&(kc(t,n|1),et(t,xe()),!(J&6)&&(xo=xe()+500,Qn()))}break;case 13:vr(function(){var r=on(e,1);if(r!==null){var o=He();jt(r,e,1,o)}}),sd(e,1)}};Pc=function(e){if(e.tag===13){var t=on(e,134217728);if(t!==null){var n=He();jt(t,e,134217728,n)}sd(e,134217728)}};Wh=function(e){if(e.tag===13){var t=Dn(e),n=on(e,t);if(n!==null){var r=He();jt(n,e,t,r)}sd(e,t)}};Kh=function(){return ne};Qh=function(e,t){var n=ne;try{return ne=e,t()}finally{ne=n}};tu=function(e,t,n){switch(t){case"input":if(Ga(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=Nl(r);if(!o)throw Error(R(90));bh(r),Ga(r,o)}}}break;case"textarea":kh(e,n);break;case"select":t=n.value,t!=null&&Br(e,!!n.multiple,t,!1)}};Mh=ed;Ah=vr;var s1={usingClientEntryPoint:!1,Events:[$s,Lr,Nl,_h,Oh,ed]},Ho={findFiberByHostInstance:er,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},i1={bundleType:Ho.bundleType,version:Ho.version,rendererPackageName:Ho.rendererPackageName,rendererConfig:Ho.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:un.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Dh(e),e===null?null:e.stateNode},findFiberByHostInstance:Ho.findFiberByHostInstance||r1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ci=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ci.isDisabled&&ci.supportsFiber)try{Sl=ci.inject(i1),Vt=ci}catch{}}ct.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=s1;ct.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!ld(t))throw Error(R(200));return n1(e,t,null,n)};ct.createRoot=function(e,t){if(!ld(e))throw Error(R(299));var n=!1,r="",o=mv;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(o=t.onRecoverableError)),t=od(e,1,!1,null,null,n,!1,r,o),e[rn]=t.current,ws(e.nodeType===8?e.parentNode:e),new id(t)};ct.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(R(188)):(e=Object.keys(e).join(","),Error(R(268,e)));return e=Dh(t),e=e===null?null:e.stateNode,e};ct.flushSync=function(e){return vr(e)};ct.hydrate=function(e,t,n){if(!Al(t))throw Error(R(200));return Il(null,e,t,!0,n)};ct.hydrateRoot=function(e,t,n){if(!ld(e))throw Error(R(405));var r=n!=null&&n.hydratedSources||null,o=!1,s="",i=mv;if(n!=null&&(n.unstable_strictMode===!0&&(o=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(i=n.onRecoverableError)),t=hv(t,null,e,1,n??null,o,!1,s,i),e[rn]=t.current,ws(e),r)for(e=0;e<r.length;e++)n=r[e],o=n._getVersion,o=o(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Ml(t)};ct.render=function(e,t,n){if(!Al(t))throw Error(R(200));return Il(null,e,t,!1,n)};ct.unmountComponentAtNode=function(e){if(!Al(e))throw Error(R(40));return e._reactRootContainer?(vr(function(){Il(null,null,e,!1,function(){e._reactRootContainer=null,e[rn]=null})}),!0):!1};ct.unstable_batchedUpdates=ed;ct.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Al(n))throw Error(R(200));if(e==null||e._reactInternals===void 0)throw Error(R(38));return Il(e,t,n,!1,r)};ct.version="18.3.1-next-f1338f8080-20240426";function vv(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(vv)}catch(e){console.error(e)}}vv(),vh.exports=ct;var Nr=vh.exports;const l1=rh(Nr);var gv,Zf=Nr;gv=Zf.createRoot,Zf.hydrateRoot;function a1(e,t){if(e instanceof RegExp)return{keys:!1,pattern:e};var n,r,o,s,i=[],l="",a=e.split("/");for(a[0]||a.shift();o=a.shift();)n=o[0],n==="*"?(i.push(n),l+=o[1]==="?"?"(?:/(.*))?":"/(.*)"):n===":"?(r=o.indexOf("?",1),s=o.indexOf(".",1),i.push(o.substring(1,~r?r:~s?s:o.length)),l+=~r&&!~s?"(?:/([^/]+?))?":"/([^/]+?)",~s&&(l+=(~r?"?":"")+"\\"+o.substring(s))):l+="/"+o;return{keys:i,pattern:new RegExp("^"+l+(t?"(?=$|/)":"/?$"),"i")}}var yv={exports:{}},xv={};/**
 * @license React
 * use-sync-external-store-shim.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var wo=f;function u1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var c1=typeof Object.is=="function"?Object.is:u1,d1=wo.useState,f1=wo.useEffect,p1=wo.useLayoutEffect,h1=wo.useDebugValue;function m1(e,t){var n=t(),r=d1({inst:{value:n,getSnapshot:t}}),o=r[0].inst,s=r[1];return p1(function(){o.value=n,o.getSnapshot=t,Na(o)&&s({inst:o})},[e,n,t]),f1(function(){return Na(o)&&s({inst:o}),e(function(){Na(o)&&s({inst:o})})},[e]),h1(n),n}function Na(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!c1(e,n)}catch{return!0}}function v1(e,t){return t()}var g1=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?v1:m1;xv.useSyncExternalStore=wo.useSyncExternalStore!==void 0?wo.useSyncExternalStore:g1;yv.exports=xv;var y1=yv.exports;const x1=hh.useInsertionEffect,w1=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",S1=w1?f.useLayoutEffect:f.useEffect,C1=x1||S1,wv=e=>{const t=f.useRef([e,(...n)=>t[0](...n)]).current;return C1(()=>{t[0]=e}),t[1]},E1="popstate",ad="pushState",ud="replaceState",b1="hashchange",Jf=[E1,ad,ud,b1],N1=e=>{for(const t of Jf)addEventListener(t,e);return()=>{for(const t of Jf)removeEventListener(t,e)}},Sv=(e,t)=>y1.useSyncExternalStore(N1,e,t),k1=()=>location.search,P1=({ssrSearch:e=""}={})=>Sv(k1,()=>e),ep=()=>location.pathname,j1=({ssrPath:e}={})=>Sv(ep,e?()=>e:ep),T1=(e,{replace:t=!1,state:n=null}={})=>history[t?ud:ad](n,"",e),R1=(e={})=>[j1(e),T1],tp=Symbol.for("wouter_v3");if(typeof history<"u"&&typeof window[tp]>"u"){for(const e of[ad,ud]){const t=history[e];history[e]=function(){const n=t.apply(this,arguments),r=new Event(e);return r.arguments=arguments,dispatchEvent(r),n}}Object.defineProperty(window,tp,{value:!0})}const _1=(e,t)=>t.toLowerCase().indexOf(e.toLowerCase())?"~"+t:t.slice(e.length)||"/",Cv=(e="")=>e==="/"?"":e,O1=(e,t)=>e[0]==="~"?e.slice(1):Cv(t)+e,M1=(e="",t)=>_1(np(Cv(e)),np(t)),np=e=>{try{return decodeURI(e)}catch{return e}},Ev={hook:R1,searchHook:P1,parser:a1,base:"",ssrPath:void 0,ssrSearch:void 0,hrefs:e=>e},bv=f.createContext(Ev),Ll=()=>f.useContext(bv),Nv={},kv=f.createContext(Nv),Pv=()=>f.useContext(kv),cd=e=>{const[t,n]=e.hook(e);return[M1(e.base,t),wv((r,o)=>n(O1(r,e.base),o))]},jv=(e,t,n,r)=>{const{pattern:o,keys:s}=t instanceof RegExp?{keys:!1,pattern:t}:e(t||"*",r),i=o.exec(n)||[],[l,...a]=i;return l!==void 0?[!0,(()=>{const c=s!==!1?Object.fromEntries(s.map((d,g)=>[d,a[g]])):i.groups;let p={...a};return c&&Object.assign(p,c),p})(),...r?[l]:[]]:[!1,null]},A1=({children:e,...t})=>{var p,d;const n=Ll(),r=t.hook?Ev:n;let o=r;const[s,i]=((p=t.ssrPath)==null?void 0:p.split("?"))??[];i&&(t.ssrSearch=i,t.ssrPath=s),t.hrefs=t.hrefs??((d=t.hook)==null?void 0:d.hrefs);let l=f.useRef({}),a=l.current,c=a;for(let g in r){const S=g==="base"?r[g]+(t[g]||""):t[g]||r[g];a===c&&S!==c[g]&&(l.current=c={...c}),c[g]=S,S!==r[g]&&(o=c)}return f.createElement(bv.Provider,{value:o,children:e})},rp=({children:e,component:t},n)=>t?f.createElement(t,{params:n}):typeof e=="function"?e(n):e,I1=e=>{let t=f.useRef(Nv),n=t.current;for(const r in e)e[r]!==n[r]&&(n=e);return Object.keys(e).length===0&&(n=e),t.current=n},ka=({path:e,nest:t,match:n,...r})=>{const o=Ll(),[s]=cd(o),[i,l,a]=n??jv(o.parser,e,s,t),c=I1({...Pv(),...l});if(!i)return null;const p=a?f.createElement(A1,{base:a},rp(r,c)):rp(r,c);return f.createElement(kv.Provider,{value:c,children:p})},ll=f.forwardRef((e,t)=>{const n=Ll(),[r,o]=cd(n),{to:s="",href:i=s,onClick:l,asChild:a,children:c,className:p,replace:d,state:g,...S}=e,w=wv(x=>{x.ctrlKey||x.metaKey||x.altKey||x.shiftKey||x.button!==0||(l==null||l(x),x.defaultPrevented||(x.preventDefault(),o(i,e)))}),m=n.hrefs(i[0]==="~"?i.slice(1):n.base+i,n);return a&&f.isValidElement(c)?f.cloneElement(c,{onClick:w,href:m}):f.createElement("a",{...S,onClick:w,href:m,className:p!=null&&p.call?p(r===i):p,children:c,ref:t})}),Tv=e=>Array.isArray(e)?e.flatMap(t=>Tv(t&&t.type===f.Fragment?t.props.children:t)):[e],L1=({children:e,location:t})=>{const n=Ll(),[r]=cd(n);for(const o of Tv(e)){let s=0;if(f.isValidElement(o)&&(s=jv(n.parser,o.props.path,t||r,o.props.nest))[0])return f.cloneElement(o,{match:s})}return null};var jo=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}},gr=typeof window>"u"||"Deno"in globalThis;function pt(){}function D1(e,t){return typeof e=="function"?e(t):e}function Au(e){return typeof e=="number"&&e>=0&&e!==1/0}function Rv(e,t){return Math.max(e+(t||0)-Date.now(),0)}function qr(e,t){return typeof e=="function"?e(t):e}function kt(e,t){return typeof e=="function"?e(t):e}function op(e,t){const{type:n="all",exact:r,fetchStatus:o,predicate:s,queryKey:i,stale:l}=e;if(i){if(r){if(t.queryHash!==dd(i,t.options))return!1}else if(!Ts(t.queryKey,i))return!1}if(n!=="all"){const a=t.isActive();if(n==="active"&&!a||n==="inactive"&&a)return!1}return!(typeof l=="boolean"&&t.isStale()!==l||o&&o!==t.state.fetchStatus||s&&!s(t))}function sp(e,t){const{exact:n,status:r,predicate:o,mutationKey:s}=e;if(s){if(!t.options.mutationKey)return!1;if(n){if(yr(t.options.mutationKey)!==yr(s))return!1}else if(!Ts(t.options.mutationKey,s))return!1}return!(r&&t.state.status!==r||o&&!o(t))}function dd(e,t){return((t==null?void 0:t.queryKeyHashFn)||yr)(e)}function yr(e){return JSON.stringify(e,(t,n)=>Iu(n)?Object.keys(n).sort().reduce((r,o)=>(r[o]=n[o],r),{}):n)}function Ts(e,t){return e===t?!0:typeof e!=typeof t?!1:e&&t&&typeof e=="object"&&typeof t=="object"?!Object.keys(t).some(n=>!Ts(e[n],t[n])):!1}function _v(e,t){if(e===t)return e;const n=ip(e)&&ip(t);if(n||Iu(e)&&Iu(t)){const r=n?e:Object.keys(e),o=r.length,s=n?t:Object.keys(t),i=s.length,l=n?[]:{};let a=0;for(let c=0;c<i;c++){const p=n?c:s[c];(!n&&r.includes(p)||n)&&e[p]===void 0&&t[p]===void 0?(l[p]=void 0,a++):(l[p]=_v(e[p],t[p]),l[p]===e[p]&&e[p]!==void 0&&a++)}return o===i&&a===o?e:l}return t}function al(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(const n in e)if(e[n]!==t[n])return!1;return!0}function ip(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function Iu(e){if(!lp(e))return!1;const t=e.constructor;if(t===void 0)return!0;const n=t.prototype;return!(!lp(n)||!n.hasOwnProperty("isPrototypeOf")||Object.getPrototypeOf(e)!==Object.prototype)}function lp(e){return Object.prototype.toString.call(e)==="[object Object]"}function F1(e){return new Promise(t=>{setTimeout(t,e)})}function Lu(e,t,n){return typeof n.structuralSharing=="function"?n.structuralSharing(e,t):n.structuralSharing!==!1?_v(e,t):t}function z1(e,t,n=0){const r=[...e,t];return n&&r.length>n?r.slice(1):r}function $1(e,t,n=0){const r=[t,...e];return n&&r.length>n?r.slice(0,-1):r}var fd=Symbol();function Ov(e,t){return!e.queryFn&&(t!=null&&t.initialPromise)?()=>t.initialPromise:!e.queryFn||e.queryFn===fd?()=>Promise.reject(new Error(`Missing queryFn: '${e.queryHash}'`)):e.queryFn}var rr,wn,Jr,Qp,U1=(Qp=class extends jo{constructor(){super();V(this,rr);V(this,wn);V(this,Jr);D(this,Jr,t=>{if(!gr&&window.addEventListener){const n=()=>t();return window.addEventListener("visibilitychange",n,!1),()=>{window.removeEventListener("visibilitychange",n)}}})}onSubscribe(){E(this,wn)||this.setEventListener(E(this,Jr))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,wn))==null||t.call(this),D(this,wn,void 0))}setEventListener(t){var n;D(this,Jr,t),(n=E(this,wn))==null||n.call(this),D(this,wn,t(r=>{typeof r=="boolean"?this.setFocused(r):this.onFocus()}))}setFocused(t){E(this,rr)!==t&&(D(this,rr,t),this.onFocus())}onFocus(){const t=this.isFocused();this.listeners.forEach(n=>{n(t)})}isFocused(){var t;return typeof E(this,rr)=="boolean"?E(this,rr):((t=globalThis.document)==null?void 0:t.visibilityState)!=="hidden"}},rr=new WeakMap,wn=new WeakMap,Jr=new WeakMap,Qp),pd=new U1,eo,Sn,to,Gp,V1=(Gp=class extends jo{constructor(){super();V(this,eo,!0);V(this,Sn);V(this,to);D(this,to,t=>{if(!gr&&window.addEventListener){const n=()=>t(!0),r=()=>t(!1);return window.addEventListener("online",n,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",n),window.removeEventListener("offline",r)}}})}onSubscribe(){E(this,Sn)||this.setEventListener(E(this,to))}onUnsubscribe(){var t;this.hasListeners()||((t=E(this,Sn))==null||t.call(this),D(this,Sn,void 0))}setEventListener(t){var n;D(this,to,t),(n=E(this,Sn))==null||n.call(this),D(this,Sn,t(this.setOnline.bind(this)))}setOnline(t){E(this,eo)!==t&&(D(this,eo,t),this.listeners.forEach(r=>{r(t)}))}isOnline(){return E(this,eo)}},eo=new WeakMap,Sn=new WeakMap,to=new WeakMap,Gp),ul=new V1;function Du(){let e,t;const n=new Promise((o,s)=>{e=o,t=s});n.status="pending",n.catch(()=>{});function r(o){Object.assign(n,o),delete n.resolve,delete n.reject}return n.resolve=o=>{r({status:"fulfilled",value:o}),e(o)},n.reject=o=>{r({status:"rejected",reason:o}),t(o)},n}function B1(e){return Math.min(1e3*2**e,3e4)}function Mv(e){return(e??"online")==="online"?ul.isOnline():!0}var Av=class extends Error{constructor(e){super("CancelledError"),this.revert=e==null?void 0:e.revert,this.silent=e==null?void 0:e.silent}};function Pa(e){return e instanceof Av}function Iv(e){let t=!1,n=0,r=!1,o;const s=Du(),i=m=>{var x;r||(g(new Av(m)),(x=e.abort)==null||x.call(e))},l=()=>{t=!0},a=()=>{t=!1},c=()=>pd.isFocused()&&(e.networkMode==="always"||ul.isOnline())&&e.canRun(),p=()=>Mv(e.networkMode)&&e.canRun(),d=m=>{var x;r||(r=!0,(x=e.onSuccess)==null||x.call(e,m),o==null||o(),s.resolve(m))},g=m=>{var x;r||(r=!0,(x=e.onError)==null||x.call(e,m),o==null||o(),s.reject(m))},S=()=>new Promise(m=>{var x;o=v=>{(r||c())&&m(v)},(x=e.onPause)==null||x.call(e)}).then(()=>{var m;o=void 0,r||(m=e.onContinue)==null||m.call(e)}),w=()=>{if(r)return;let m;const x=n===0?e.initialPromise:void 0;try{m=x??e.fn()}catch(v){m=Promise.reject(v)}Promise.resolve(m).then(d).catch(v=>{var k;if(r)return;const h=e.retry??(gr?0:3),y=e.retryDelay??B1,C=typeof y=="function"?y(n,v):y,b=h===!0||typeof h=="number"&&n<h||typeof h=="function"&&h(n,v);if(t||!b){g(v);return}n++,(k=e.onFail)==null||k.call(e,n,v),F1(C).then(()=>c()?void 0:S()).then(()=>{t?g(v):w()})})};return{promise:s,cancel:i,continue:()=>(o==null||o(),s),cancelRetry:l,continueRetry:a,canStart:p,start:()=>(p()?w():S().then(w),s)}}function H1(){let e=[],t=0,n=l=>{l()},r=l=>{l()},o=l=>setTimeout(l,0);const s=l=>{t?e.push(l):o(()=>{n(l)})},i=()=>{const l=e;e=[],l.length&&o(()=>{r(()=>{l.forEach(a=>{n(a)})})})};return{batch:l=>{let a;t++;try{a=l()}finally{t--,t||i()}return a},batchCalls:l=>(...a)=>{s(()=>{l(...a)})},schedule:s,setNotifyFunction:l=>{n=l},setBatchNotifyFunction:l=>{r=l},setScheduler:l=>{o=l}}}var be=H1(),or,qp,Lv=(qp=class{constructor(){V(this,or)}destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),Au(this.gcTime)&&D(this,or,setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(gr?1/0:5*60*1e3))}clearGcTimeout(){E(this,or)&&(clearTimeout(E(this,or)),D(this,or,void 0))}},or=new WeakMap,qp),no,ro,ft,Ae,Ms,sr,Et,Gt,Yp,W1=(Yp=class extends Lv{constructor(t){super();V(this,Et);V(this,no);V(this,ro);V(this,ft);V(this,Ae);V(this,Ms);V(this,sr);D(this,sr,!1),D(this,Ms,t.defaultOptions),this.setOptions(t.options),this.observers=[],D(this,ft,t.cache),this.queryKey=t.queryKey,this.queryHash=t.queryHash,D(this,no,K1(this.options)),this.state=t.state??E(this,no),this.scheduleGc()}get meta(){return this.options.meta}get promise(){var t;return(t=E(this,Ae))==null?void 0:t.promise}setOptions(t){this.options={...E(this,Ms),...t},this.updateGcTime(this.options.gcTime)}optionalRemove(){!this.observers.length&&this.state.fetchStatus==="idle"&&E(this,ft).remove(this)}setData(t,n){const r=Lu(this.state.data,t,this.options);return G(this,Et,Gt).call(this,{data:r,type:"success",dataUpdatedAt:n==null?void 0:n.updatedAt,manual:n==null?void 0:n.manual}),r}setState(t,n){G(this,Et,Gt).call(this,{type:"setState",state:t,setStateOptions:n})}cancel(t){var r,o;const n=(r=E(this,Ae))==null?void 0:r.promise;return(o=E(this,Ae))==null||o.cancel(t),n?n.then(pt).catch(pt):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(E(this,no))}isActive(){return this.observers.some(t=>kt(t.options.enabled,this)!==!1)}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===fd||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return this.state.isInvalidated?!0:this.getObserversCount()>0?this.observers.some(t=>t.getCurrentResult().isStale):this.state.data===void 0}isStaleByTime(t=0){return this.state.isInvalidated||this.state.data===void 0||!Rv(this.state.dataUpdatedAt,t)}onFocus(){var n;const t=this.observers.find(r=>r.shouldFetchOnWindowFocus());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,Ae))==null||n.continue()}onOnline(){var n;const t=this.observers.find(r=>r.shouldFetchOnReconnect());t==null||t.refetch({cancelRefetch:!1}),(n=E(this,Ae))==null||n.continue()}addObserver(t){this.observers.includes(t)||(this.observers.push(t),this.clearGcTimeout(),E(this,ft).notify({type:"observerAdded",query:this,observer:t}))}removeObserver(t){this.observers.includes(t)&&(this.observers=this.observers.filter(n=>n!==t),this.observers.length||(E(this,Ae)&&(E(this,sr)?E(this,Ae).cancel({revert:!0}):E(this,Ae).cancelRetry()),this.scheduleGc()),E(this,ft).notify({type:"observerRemoved",query:this,observer:t}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||G(this,Et,Gt).call(this,{type:"invalidate"})}fetch(t,n){var a,c,p;if(this.state.fetchStatus!=="idle"){if(this.state.data!==void 0&&(n!=null&&n.cancelRefetch))this.cancel({silent:!0});else if(E(this,Ae))return E(this,Ae).continueRetry(),E(this,Ae).promise}if(t&&this.setOptions(t),!this.options.queryFn){const d=this.observers.find(g=>g.options.queryFn);d&&this.setOptions(d.options)}const r=new AbortController,o=d=>{Object.defineProperty(d,"signal",{enumerable:!0,get:()=>(D(this,sr,!0),r.signal)})},s=()=>{const d=Ov(this.options,n),g={queryKey:this.queryKey,meta:this.meta};return o(g),D(this,sr,!1),this.options.persister?this.options.persister(d,g,this):d(g)},i={fetchOptions:n,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:s};o(i),(a=this.options.behavior)==null||a.onFetch(i,this),D(this,ro,this.state),(this.state.fetchStatus==="idle"||this.state.fetchMeta!==((c=i.fetchOptions)==null?void 0:c.meta))&&G(this,Et,Gt).call(this,{type:"fetch",meta:(p=i.fetchOptions)==null?void 0:p.meta});const l=d=>{var g,S,w,m;Pa(d)&&d.silent||G(this,Et,Gt).call(this,{type:"error",error:d}),Pa(d)||((S=(g=E(this,ft).config).onError)==null||S.call(g,d,this),(m=(w=E(this,ft).config).onSettled)==null||m.call(w,this.state.data,d,this)),this.scheduleGc()};return D(this,Ae,Iv({initialPromise:n==null?void 0:n.initialPromise,fn:i.fetchFn,abort:r.abort.bind(r),onSuccess:d=>{var g,S,w,m;if(d===void 0){l(new Error(`${this.queryHash} data is undefined`));return}try{this.setData(d)}catch(x){l(x);return}(S=(g=E(this,ft).config).onSuccess)==null||S.call(g,d,this),(m=(w=E(this,ft).config).onSettled)==null||m.call(w,d,this.state.error,this),this.scheduleGc()},onError:l,onFail:(d,g)=>{G(this,Et,Gt).call(this,{type:"failed",failureCount:d,error:g})},onPause:()=>{G(this,Et,Gt).call(this,{type:"pause"})},onContinue:()=>{G(this,Et,Gt).call(this,{type:"continue"})},retry:i.options.retry,retryDelay:i.options.retryDelay,networkMode:i.options.networkMode,canRun:()=>!0})),E(this,Ae).start()}},no=new WeakMap,ro=new WeakMap,ft=new WeakMap,Ae=new WeakMap,Ms=new WeakMap,sr=new WeakMap,Et=new WeakSet,Gt=function(t){const n=r=>{switch(t.type){case"failed":return{...r,fetchFailureCount:t.failureCount,fetchFailureReason:t.error};case"pause":return{...r,fetchStatus:"paused"};case"continue":return{...r,fetchStatus:"fetching"};case"fetch":return{...r,...Dv(r.data,this.options),fetchMeta:t.meta??null};case"success":return{...r,data:t.data,dataUpdateCount:r.dataUpdateCount+1,dataUpdatedAt:t.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!t.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":const o=t.error;return Pa(o)&&o.revert&&E(this,ro)?{...E(this,ro),fetchStatus:"idle"}:{...r,error:o,errorUpdateCount:r.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:r.fetchFailureCount+1,fetchFailureReason:o,fetchStatus:"idle",status:"error"};case"invalidate":return{...r,isInvalidated:!0};case"setState":return{...r,...t.state}}};this.state=n(this.state),be.batch(()=>{this.observers.forEach(r=>{r.onQueryUpdate()}),E(this,ft).notify({query:this,type:"updated",action:t})})},Yp);function Dv(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:Mv(t.networkMode)?"fetching":"paused",...e===void 0&&{error:null,status:"pending"}}}function K1(e){const t=typeof e.initialData=="function"?e.initialData():e.initialData,n=t!==void 0,r=n?typeof e.initialDataUpdatedAt=="function"?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:n?r??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:n?"success":"pending",fetchStatus:"idle"}}var Lt,Xp,Q1=(Xp=class extends jo{constructor(t={}){super();V(this,Lt);this.config=t,D(this,Lt,new Map)}build(t,n,r){const o=n.queryKey,s=n.queryHash??dd(o,n);let i=this.get(s);return i||(i=new W1({cache:this,queryKey:o,queryHash:s,options:t.defaultQueryOptions(n),state:r,defaultOptions:t.getQueryDefaults(o)}),this.add(i)),i}add(t){E(this,Lt).has(t.queryHash)||(E(this,Lt).set(t.queryHash,t),this.notify({type:"added",query:t}))}remove(t){const n=E(this,Lt).get(t.queryHash);n&&(t.destroy(),n===t&&E(this,Lt).delete(t.queryHash),this.notify({type:"removed",query:t}))}clear(){be.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}get(t){return E(this,Lt).get(t)}getAll(){return[...E(this,Lt).values()]}find(t){const n={exact:!0,...t};return this.getAll().find(r=>op(n,r))}findAll(t={}){const n=this.getAll();return Object.keys(t).length>0?n.filter(r=>op(t,r)):n}notify(t){be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}onFocus(){be.batch(()=>{this.getAll().forEach(t=>{t.onFocus()})})}onOnline(){be.batch(()=>{this.getAll().forEach(t=>{t.onOnline()})})}},Lt=new WeakMap,Xp),Dt,$e,ir,Ft,hn,Zp,G1=(Zp=class extends Lv{constructor(t){super();V(this,Ft);V(this,Dt);V(this,$e);V(this,ir);this.mutationId=t.mutationId,D(this,$e,t.mutationCache),D(this,Dt,[]),this.state=t.state||Fv(),this.setOptions(t.options),this.scheduleGc()}setOptions(t){this.options=t,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(t){E(this,Dt).includes(t)||(E(this,Dt).push(t),this.clearGcTimeout(),E(this,$e).notify({type:"observerAdded",mutation:this,observer:t}))}removeObserver(t){D(this,Dt,E(this,Dt).filter(n=>n!==t)),this.scheduleGc(),E(this,$e).notify({type:"observerRemoved",mutation:this,observer:t})}optionalRemove(){E(this,Dt).length||(this.state.status==="pending"?this.scheduleGc():E(this,$e).remove(this))}continue(){var t;return((t=E(this,ir))==null?void 0:t.continue())??this.execute(this.state.variables)}async execute(t){var o,s,i,l,a,c,p,d,g,S,w,m,x,v,h,y,C,b,k,N;D(this,ir,Iv({fn:()=>this.options.mutationFn?this.options.mutationFn(t):Promise.reject(new Error("No mutationFn found")),onFail:(P,_)=>{G(this,Ft,hn).call(this,{type:"failed",failureCount:P,error:_})},onPause:()=>{G(this,Ft,hn).call(this,{type:"pause"})},onContinue:()=>{G(this,Ft,hn).call(this,{type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>E(this,$e).canRun(this)}));const n=this.state.status==="pending",r=!E(this,ir).canStart();try{if(!n){G(this,Ft,hn).call(this,{type:"pending",variables:t,isPaused:r}),await((s=(o=E(this,$e).config).onMutate)==null?void 0:s.call(o,t,this));const _=await((l=(i=this.options).onMutate)==null?void 0:l.call(i,t));_!==this.state.context&&G(this,Ft,hn).call(this,{type:"pending",context:_,variables:t,isPaused:r})}const P=await E(this,ir).start();return await((c=(a=E(this,$e).config).onSuccess)==null?void 0:c.call(a,P,t,this.state.context,this)),await((d=(p=this.options).onSuccess)==null?void 0:d.call(p,P,t,this.state.context)),await((S=(g=E(this,$e).config).onSettled)==null?void 0:S.call(g,P,null,this.state.variables,this.state.context,this)),await((m=(w=this.options).onSettled)==null?void 0:m.call(w,P,null,t,this.state.context)),G(this,Ft,hn).call(this,{type:"success",data:P}),P}catch(P){try{throw await((v=(x=E(this,$e).config).onError)==null?void 0:v.call(x,P,t,this.state.context,this)),await((y=(h=this.options).onError)==null?void 0:y.call(h,P,t,this.state.context)),await((b=(C=E(this,$e).config).onSettled)==null?void 0:b.call(C,void 0,P,this.state.variables,this.state.context,this)),await((N=(k=this.options).onSettled)==null?void 0:N.call(k,void 0,P,t,this.state.context)),P}finally{G(this,Ft,hn).call(this,{type:"error",error:P})}}finally{E(this,$e).runNext(this)}}},Dt=new WeakMap,$e=new WeakMap,ir=new WeakMap,Ft=new WeakSet,hn=function(t){const n=r=>{switch(t.type){case"failed":return{...r,failureCount:t.failureCount,failureReason:t.error};case"pause":return{...r,isPaused:!0};case"continue":return{...r,isPaused:!1};case"pending":return{...r,context:t.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:t.isPaused,status:"pending",variables:t.variables,submittedAt:Date.now()};case"success":return{...r,data:t.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...r,data:void 0,error:t.error,failureCount:r.failureCount+1,failureReason:t.error,isPaused:!1,status:"error"}}};this.state=n(this.state),be.batch(()=>{E(this,Dt).forEach(r=>{r.onMutationUpdate(t)}),E(this,$e).notify({mutation:this,type:"updated",action:t})})},Zp);function Fv(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}var nt,As,Jp,q1=(Jp=class extends jo{constructor(t={}){super();V(this,nt);V(this,As);this.config=t,D(this,nt,new Map),D(this,As,Date.now())}build(t,n,r){const o=new G1({mutationCache:this,mutationId:++Qs(this,As)._,options:t.defaultMutationOptions(n),state:r});return this.add(o),o}add(t){const n=di(t),r=E(this,nt).get(n)??[];r.push(t),E(this,nt).set(n,r),this.notify({type:"added",mutation:t})}remove(t){var r;const n=di(t);if(E(this,nt).has(n)){const o=(r=E(this,nt).get(n))==null?void 0:r.filter(s=>s!==t);o&&(o.length===0?E(this,nt).delete(n):E(this,nt).set(n,o))}this.notify({type:"removed",mutation:t})}canRun(t){var r;const n=(r=E(this,nt).get(di(t)))==null?void 0:r.find(o=>o.state.status==="pending");return!n||n===t}runNext(t){var r;const n=(r=E(this,nt).get(di(t)))==null?void 0:r.find(o=>o!==t&&o.state.isPaused);return(n==null?void 0:n.continue())??Promise.resolve()}clear(){be.batch(()=>{this.getAll().forEach(t=>{this.remove(t)})})}getAll(){return[...E(this,nt).values()].flat()}find(t){const n={exact:!0,...t};return this.getAll().find(r=>sp(n,r))}findAll(t={}){return this.getAll().filter(n=>sp(t,n))}notify(t){be.batch(()=>{this.listeners.forEach(n=>{n(t)})})}resumePausedMutations(){const t=this.getAll().filter(n=>n.state.isPaused);return be.batch(()=>Promise.all(t.map(n=>n.continue().catch(pt))))}},nt=new WeakMap,As=new WeakMap,Jp);function di(e){var t;return((t=e.options.scope)==null?void 0:t.id)??String(e.mutationId)}function ap(e){return{onFetch:(t,n)=>{var p,d,g,S,w;const r=t.options,o=(g=(d=(p=t.fetchOptions)==null?void 0:p.meta)==null?void 0:d.fetchMore)==null?void 0:g.direction,s=((S=t.state.data)==null?void 0:S.pages)||[],i=((w=t.state.data)==null?void 0:w.pageParams)||[];let l={pages:[],pageParams:[]},a=0;const c=async()=>{let m=!1;const x=y=>{Object.defineProperty(y,"signal",{enumerable:!0,get:()=>(t.signal.aborted?m=!0:t.signal.addEventListener("abort",()=>{m=!0}),t.signal)})},v=Ov(t.options,t.fetchOptions),h=async(y,C,b)=>{if(m)return Promise.reject();if(C==null&&y.pages.length)return Promise.resolve(y);const k={queryKey:t.queryKey,pageParam:C,direction:b?"backward":"forward",meta:t.options.meta};x(k);const N=await v(k),{maxPages:P}=t.options,_=b?$1:z1;return{pages:_(y.pages,N,P),pageParams:_(y.pageParams,C,P)}};if(o&&s.length){const y=o==="backward",C=y?Y1:up,b={pages:s,pageParams:i},k=C(r,b);l=await h(b,k,y)}else{const y=e??s.length;do{const C=a===0?i[0]??r.initialPageParam:up(r,l);if(a>0&&C==null)break;l=await h(l,C),a++}while(a<y)}return l};t.options.persister?t.fetchFn=()=>{var m,x;return(x=(m=t.options).persister)==null?void 0:x.call(m,c,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},n)}:t.fetchFn=c}}}function up(e,{pages:t,pageParams:n}){const r=t.length-1;return t.length>0?e.getNextPageParam(t[r],t,n[r],n):void 0}function Y1(e,{pages:t,pageParams:n}){var r;return t.length>0?(r=e.getPreviousPageParam)==null?void 0:r.call(e,t[0],t,n[0],n):void 0}var ge,Cn,En,oo,so,bn,io,lo,eh,X1=(eh=class{constructor(e={}){V(this,ge);V(this,Cn);V(this,En);V(this,oo);V(this,so);V(this,bn);V(this,io);V(this,lo);D(this,ge,e.queryCache||new Q1),D(this,Cn,e.mutationCache||new q1),D(this,En,e.defaultOptions||{}),D(this,oo,new Map),D(this,so,new Map),D(this,bn,0)}mount(){Qs(this,bn)._++,E(this,bn)===1&&(D(this,io,pd.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,ge).onFocus())})),D(this,lo,ul.subscribe(async e=>{e&&(await this.resumePausedMutations(),E(this,ge).onOnline())})))}unmount(){var e,t;Qs(this,bn)._--,E(this,bn)===0&&((e=E(this,io))==null||e.call(this),D(this,io,void 0),(t=E(this,lo))==null||t.call(this),D(this,lo,void 0))}isFetching(e){return E(this,ge).findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return E(this,Cn).findAll({...e,status:"pending"}).length}getQueryData(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,ge).get(t.queryHash))==null?void 0:n.state.data}ensureQueryData(e){const t=this.getQueryData(e.queryKey);if(t===void 0)return this.fetchQuery(e);{const n=this.defaultQueryOptions(e),r=E(this,ge).build(this,n);return e.revalidateIfStale&&r.isStaleByTime(qr(n.staleTime,r))&&this.prefetchQuery(n),Promise.resolve(t)}}getQueriesData(e){return E(this,ge).findAll(e).map(({queryKey:t,state:n})=>{const r=n.data;return[t,r]})}setQueryData(e,t,n){const r=this.defaultQueryOptions({queryKey:e}),o=E(this,ge).get(r.queryHash),s=o==null?void 0:o.state.data,i=D1(t,s);if(i!==void 0)return E(this,ge).build(this,r).setData(i,{...n,manual:!0})}setQueriesData(e,t,n){return be.batch(()=>E(this,ge).findAll(e).map(({queryKey:r})=>[r,this.setQueryData(r,t,n)]))}getQueryState(e){var n;const t=this.defaultQueryOptions({queryKey:e});return(n=E(this,ge).get(t.queryHash))==null?void 0:n.state}removeQueries(e){const t=E(this,ge);be.batch(()=>{t.findAll(e).forEach(n=>{t.remove(n)})})}resetQueries(e,t){const n=E(this,ge),r={type:"active",...e};return be.batch(()=>(n.findAll(e).forEach(o=>{o.reset()}),this.refetchQueries(r,t)))}cancelQueries(e={},t={}){const n={revert:!0,...t},r=be.batch(()=>E(this,ge).findAll(e).map(o=>o.cancel(n)));return Promise.all(r).then(pt).catch(pt)}invalidateQueries(e={},t={}){return be.batch(()=>{if(E(this,ge).findAll(e).forEach(r=>{r.invalidate()}),e.refetchType==="none")return Promise.resolve();const n={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(n,t)})}refetchQueries(e={},t){const n={...t,cancelRefetch:(t==null?void 0:t.cancelRefetch)??!0},r=be.batch(()=>E(this,ge).findAll(e).filter(o=>!o.isDisabled()).map(o=>{let s=o.fetch(void 0,n);return n.throwOnError||(s=s.catch(pt)),o.state.fetchStatus==="paused"?Promise.resolve():s}));return Promise.all(r).then(pt)}fetchQuery(e){const t=this.defaultQueryOptions(e);t.retry===void 0&&(t.retry=!1);const n=E(this,ge).build(this,t);return n.isStaleByTime(qr(t.staleTime,n))?n.fetch(t):Promise.resolve(n.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(pt).catch(pt)}fetchInfiniteQuery(e){return e.behavior=ap(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(pt).catch(pt)}ensureInfiniteQueryData(e){return e.behavior=ap(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return ul.isOnline()?E(this,Cn).resumePausedMutations():Promise.resolve()}getQueryCache(){return E(this,ge)}getMutationCache(){return E(this,Cn)}getDefaultOptions(){return E(this,En)}setDefaultOptions(e){D(this,En,e)}setQueryDefaults(e,t){E(this,oo).set(yr(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){const t=[...E(this,oo).values()];let n={};return t.forEach(r=>{Ts(e,r.queryKey)&&(n={...n,...r.defaultOptions})}),n}setMutationDefaults(e,t){E(this,so).set(yr(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){const t=[...E(this,so).values()];let n={};return t.forEach(r=>{Ts(e,r.mutationKey)&&(n={...n,...r.defaultOptions})}),n}defaultQueryOptions(e){if(e._defaulted)return e;const t={...E(this,En).queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=dd(t.queryKey,t)),t.refetchOnReconnect===void 0&&(t.refetchOnReconnect=t.networkMode!=="always"),t.throwOnError===void 0&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),t.enabled!==!0&&t.queryFn===fd&&(t.enabled=!1),t}defaultMutationOptions(e){return e!=null&&e._defaulted?e:{...E(this,En).mutations,...(e==null?void 0:e.mutationKey)&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){E(this,ge).clear(),E(this,Cn).clear()}},ge=new WeakMap,Cn=new WeakMap,En=new WeakMap,oo=new WeakMap,so=new WeakMap,bn=new WeakMap,io=new WeakMap,lo=new WeakMap,eh),Qe,X,Is,Ue,lr,ao,Nn,zt,Ls,uo,co,ar,ur,kn,fo,te,Xo,Fu,zu,$u,Uu,Vu,Bu,Hu,zv,th,Z1=(th=class extends jo{constructor(t,n){super();V(this,te);V(this,Qe);V(this,X);V(this,Is);V(this,Ue);V(this,lr);V(this,ao);V(this,Nn);V(this,zt);V(this,Ls);V(this,uo);V(this,co);V(this,ar);V(this,ur);V(this,kn);V(this,fo,new Set);this.options=n,D(this,Qe,t),D(this,zt,null),D(this,Nn,Du()),this.options.experimental_prefetchInRender||E(this,Nn).reject(new Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(n)}bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){this.listeners.size===1&&(E(this,X).addObserver(this),cp(E(this,X),this.options)?G(this,te,Xo).call(this):this.updateResult(),G(this,te,Uu).call(this))}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return Wu(E(this,X),this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return Wu(E(this,X),this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,G(this,te,Vu).call(this),G(this,te,Bu).call(this),E(this,X).removeObserver(this)}setOptions(t,n){const r=this.options,o=E(this,X);if(this.options=E(this,Qe).defaultQueryOptions(t),this.options.enabled!==void 0&&typeof this.options.enabled!="boolean"&&typeof this.options.enabled!="function"&&typeof kt(this.options.enabled,E(this,X))!="boolean")throw new Error("Expected enabled to be a boolean or a callback that returns a boolean");G(this,te,Hu).call(this),E(this,X).setOptions(this.options),r._defaulted&&!al(this.options,r)&&E(this,Qe).getQueryCache().notify({type:"observerOptionsUpdated",query:E(this,X),observer:this});const s=this.hasListeners();s&&dp(E(this,X),o,this.options,r)&&G(this,te,Xo).call(this),this.updateResult(n),s&&(E(this,X)!==o||kt(this.options.enabled,E(this,X))!==kt(r.enabled,E(this,X))||qr(this.options.staleTime,E(this,X))!==qr(r.staleTime,E(this,X)))&&G(this,te,Fu).call(this);const i=G(this,te,zu).call(this);s&&(E(this,X)!==o||kt(this.options.enabled,E(this,X))!==kt(r.enabled,E(this,X))||i!==E(this,kn))&&G(this,te,$u).call(this,i)}getOptimisticResult(t){const n=E(this,Qe).getQueryCache().build(E(this,Qe),t),r=this.createResult(n,t);return eS(this,r)&&(D(this,Ue,r),D(this,ao,this.options),D(this,lr,E(this,X).state)),r}getCurrentResult(){return E(this,Ue)}trackResult(t,n){const r={};return Object.keys(t).forEach(o=>{Object.defineProperty(r,o,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(o),n==null||n(o),t[o])})}),r}trackProp(t){E(this,fo).add(t)}getCurrentQuery(){return E(this,X)}refetch({...t}={}){return this.fetch({...t})}fetchOptimistic(t){const n=E(this,Qe).defaultQueryOptions(t),r=E(this,Qe).getQueryCache().build(E(this,Qe),n);return r.fetch().then(()=>this.createResult(r,n))}fetch(t){return G(this,te,Xo).call(this,{...t,cancelRefetch:t.cancelRefetch??!0}).then(()=>(this.updateResult(),E(this,Ue)))}createResult(t,n){var P;const r=E(this,X),o=this.options,s=E(this,Ue),i=E(this,lr),l=E(this,ao),c=t!==r?t.state:E(this,Is),{state:p}=t;let d={...p},g=!1,S;if(n._optimisticResults){const _=this.hasListeners(),O=!_&&cp(t,n),I=_&&dp(t,r,n,o);(O||I)&&(d={...d,...Dv(p.data,t.options)}),n._optimisticResults==="isRestoring"&&(d.fetchStatus="idle")}let{error:w,errorUpdatedAt:m,status:x}=d;if(n.select&&d.data!==void 0)if(s&&d.data===(i==null?void 0:i.data)&&n.select===E(this,Ls))S=E(this,uo);else try{D(this,Ls,n.select),S=n.select(d.data),S=Lu(s==null?void 0:s.data,S,n),D(this,uo,S),D(this,zt,null)}catch(_){D(this,zt,_)}else S=d.data;if(n.placeholderData!==void 0&&S===void 0&&x==="pending"){let _;if(s!=null&&s.isPlaceholderData&&n.placeholderData===(l==null?void 0:l.placeholderData))_=s.data;else if(_=typeof n.placeholderData=="function"?n.placeholderData((P=E(this,co))==null?void 0:P.state.data,E(this,co)):n.placeholderData,n.select&&_!==void 0)try{_=n.select(_),D(this,zt,null)}catch(O){D(this,zt,O)}_!==void 0&&(x="success",S=Lu(s==null?void 0:s.data,_,n),g=!0)}E(this,zt)&&(w=E(this,zt),S=E(this,uo),m=Date.now(),x="error");const v=d.fetchStatus==="fetching",h=x==="pending",y=x==="error",C=h&&v,b=S!==void 0,N={status:x,fetchStatus:d.fetchStatus,isPending:h,isSuccess:x==="success",isError:y,isInitialLoading:C,isLoading:C,data:S,dataUpdatedAt:d.dataUpdatedAt,error:w,errorUpdatedAt:m,failureCount:d.fetchFailureCount,failureReason:d.fetchFailureReason,errorUpdateCount:d.errorUpdateCount,isFetched:d.dataUpdateCount>0||d.errorUpdateCount>0,isFetchedAfterMount:d.dataUpdateCount>c.dataUpdateCount||d.errorUpdateCount>c.errorUpdateCount,isFetching:v,isRefetching:v&&!h,isLoadingError:y&&!b,isPaused:d.fetchStatus==="paused",isPlaceholderData:g,isRefetchError:y&&b,isStale:hd(t,n),refetch:this.refetch,promise:E(this,Nn)};if(this.options.experimental_prefetchInRender){const _=M=>{N.status==="error"?M.reject(N.error):N.data!==void 0&&M.resolve(N.data)},O=()=>{const M=D(this,Nn,N.promise=Du());_(M)},I=E(this,Nn);switch(I.status){case"pending":t.queryHash===r.queryHash&&_(I);break;case"fulfilled":(N.status==="error"||N.data!==I.value)&&O();break;case"rejected":(N.status!=="error"||N.error!==I.reason)&&O();break}}return N}updateResult(t){const n=E(this,Ue),r=this.createResult(E(this,X),this.options);if(D(this,lr,E(this,X).state),D(this,ao,this.options),E(this,lr).data!==void 0&&D(this,co,E(this,X)),al(r,n))return;D(this,Ue,r);const o={},s=()=>{if(!n)return!0;const{notifyOnChangeProps:i}=this.options,l=typeof i=="function"?i():i;if(l==="all"||!l&&!E(this,fo).size)return!0;const a=new Set(l??E(this,fo));return this.options.throwOnError&&a.add("error"),Object.keys(E(this,Ue)).some(c=>{const p=c;return E(this,Ue)[p]!==n[p]&&a.has(p)})};(t==null?void 0:t.listeners)!==!1&&s()&&(o.listeners=!0),G(this,te,zv).call(this,{...o,...t})}onQueryUpdate(){this.updateResult(),this.hasListeners()&&G(this,te,Uu).call(this)}},Qe=new WeakMap,X=new WeakMap,Is=new WeakMap,Ue=new WeakMap,lr=new WeakMap,ao=new WeakMap,Nn=new WeakMap,zt=new WeakMap,Ls=new WeakMap,uo=new WeakMap,co=new WeakMap,ar=new WeakMap,ur=new WeakMap,kn=new WeakMap,fo=new WeakMap,te=new WeakSet,Xo=function(t){G(this,te,Hu).call(this);let n=E(this,X).fetch(this.options,t);return t!=null&&t.throwOnError||(n=n.catch(pt)),n},Fu=function(){G(this,te,Vu).call(this);const t=qr(this.options.staleTime,E(this,X));if(gr||E(this,Ue).isStale||!Au(t))return;const r=Rv(E(this,Ue).dataUpdatedAt,t)+1;D(this,ar,setTimeout(()=>{E(this,Ue).isStale||this.updateResult()},r))},zu=function(){return(typeof this.options.refetchInterval=="function"?this.options.refetchInterval(E(this,X)):this.options.refetchInterval)??!1},$u=function(t){G(this,te,Bu).call(this),D(this,kn,t),!(gr||kt(this.options.enabled,E(this,X))===!1||!Au(E(this,kn))||E(this,kn)===0)&&D(this,ur,setInterval(()=>{(this.options.refetchIntervalInBackground||pd.isFocused())&&G(this,te,Xo).call(this)},E(this,kn)))},Uu=function(){G(this,te,Fu).call(this),G(this,te,$u).call(this,G(this,te,zu).call(this))},Vu=function(){E(this,ar)&&(clearTimeout(E(this,ar)),D(this,ar,void 0))},Bu=function(){E(this,ur)&&(clearInterval(E(this,ur)),D(this,ur,void 0))},Hu=function(){const t=E(this,Qe).getQueryCache().build(E(this,Qe),this.options);if(t===E(this,X))return;const n=E(this,X);D(this,X,t),D(this,Is,t.state),this.hasListeners()&&(n==null||n.removeObserver(this),t.addObserver(this))},zv=function(t){be.batch(()=>{t.listeners&&this.listeners.forEach(n=>{n(E(this,Ue))}),E(this,Qe).getQueryCache().notify({query:E(this,X),type:"observerResultsUpdated"})})},th);function J1(e,t){return kt(t.enabled,e)!==!1&&e.state.data===void 0&&!(e.state.status==="error"&&t.retryOnMount===!1)}function cp(e,t){return J1(e,t)||e.state.data!==void 0&&Wu(e,t,t.refetchOnMount)}function Wu(e,t,n){if(kt(t.enabled,e)!==!1){const r=typeof n=="function"?n(e):n;return r==="always"||r!==!1&&hd(e,t)}return!1}function dp(e,t,n,r){return(e!==t||kt(r.enabled,e)===!1)&&(!n.suspense||e.state.status!=="error")&&hd(e,n)}function hd(e,t){return kt(t.enabled,e)!==!1&&e.isStaleByTime(qr(t.staleTime,e))}function eS(e,t){return!al(e.getCurrentResult(),t)}var Pn,jn,Ge,Xt,tn,_i,Ku,nh,tS=(nh=class extends jo{constructor(n,r){super();V(this,tn);V(this,Pn);V(this,jn);V(this,Ge);V(this,Xt);D(this,Pn,n),this.setOptions(r),this.bindMethods(),G(this,tn,_i).call(this)}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(n){var o;const r=this.options;this.options=E(this,Pn).defaultMutationOptions(n),al(this.options,r)||E(this,Pn).getMutationCache().notify({type:"observerOptionsUpdated",mutation:E(this,Ge),observer:this}),r!=null&&r.mutationKey&&this.options.mutationKey&&yr(r.mutationKey)!==yr(this.options.mutationKey)?this.reset():((o=E(this,Ge))==null?void 0:o.state.status)==="pending"&&E(this,Ge).setOptions(this.options)}onUnsubscribe(){var n;this.hasListeners()||(n=E(this,Ge))==null||n.removeObserver(this)}onMutationUpdate(n){G(this,tn,_i).call(this),G(this,tn,Ku).call(this,n)}getCurrentResult(){return E(this,jn)}reset(){var n;(n=E(this,Ge))==null||n.removeObserver(this),D(this,Ge,void 0),G(this,tn,_i).call(this),G(this,tn,Ku).call(this)}mutate(n,r){var o;return D(this,Xt,r),(o=E(this,Ge))==null||o.removeObserver(this),D(this,Ge,E(this,Pn).getMutationCache().build(E(this,Pn),this.options)),E(this,Ge).addObserver(this),E(this,Ge).execute(n)}},Pn=new WeakMap,jn=new WeakMap,Ge=new WeakMap,Xt=new WeakMap,tn=new WeakSet,_i=function(){var r;const n=((r=E(this,Ge))==null?void 0:r.state)??Fv();D(this,jn,{...n,isPending:n.status==="pending",isSuccess:n.status==="success",isError:n.status==="error",isIdle:n.status==="idle",mutate:this.mutate,reset:this.reset})},Ku=function(n){be.batch(()=>{var r,o,s,i,l,a,c,p;if(E(this,Xt)&&this.hasListeners()){const d=E(this,jn).variables,g=E(this,jn).context;(n==null?void 0:n.type)==="success"?((o=(r=E(this,Xt)).onSuccess)==null||o.call(r,n.data,d,g),(i=(s=E(this,Xt)).onSettled)==null||i.call(s,n.data,null,d,g)):(n==null?void 0:n.type)==="error"&&((a=(l=E(this,Xt)).onError)==null||a.call(l,n.error,d,g),(p=(c=E(this,Xt)).onSettled)==null||p.call(c,void 0,n.error,d,g))}this.listeners.forEach(d=>{d(E(this,jn))})})},nh),$v=f.createContext(void 0),Vs=e=>{const t=f.useContext($v);if(!t)throw new Error("No QueryClient set, use QueryClientProvider to set one");return t},nS=({client:e,children:t})=>(f.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),u.jsx($v.Provider,{value:e,children:t})),Uv=f.createContext(!1),rS=()=>f.useContext(Uv);Uv.Provider;function oS(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}var sS=f.createContext(oS()),iS=()=>f.useContext(sS);function Vv(e,t){return typeof e=="function"?e(...t):!!e}function Qu(){}var lS=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&(t.isReset()||(e.retryOnMount=!1))},aS=e=>{f.useEffect(()=>{e.clearReset()},[e])},uS=({result:e,errorResetBoundary:t,throwOnError:n,query:r})=>e.isError&&!t.isReset()&&!e.isFetching&&r&&Vv(n,[e.error,r]),cS=e=>{e.suspense&&(e.staleTime===void 0&&(e.staleTime=1e3),typeof e.gcTime=="number"&&(e.gcTime=Math.max(e.gcTime,1e3)))},dS=(e,t)=>e.isLoading&&e.isFetching&&!t,fS=(e,t)=>(e==null?void 0:e.suspense)&&t.isPending,fp=(e,t,n)=>t.fetchOptimistic(e).catch(()=>{n.clearReset()});function pS(e,t,n){var p,d,g,S,w;const r=Vs(),o=rS(),s=iS(),i=r.defaultQueryOptions(e);(d=(p=r.getDefaultOptions().queries)==null?void 0:p._experimental_beforeQuery)==null||d.call(p,i),i._optimisticResults=o?"isRestoring":"optimistic",cS(i),lS(i,s),aS(s);const l=!r.getQueryCache().get(i.queryHash),[a]=f.useState(()=>new t(r,i)),c=a.getOptimisticResult(i);if(f.useSyncExternalStore(f.useCallback(m=>{const x=o?Qu:a.subscribe(be.batchCalls(m));return a.updateResult(),x},[a,o]),()=>a.getCurrentResult(),()=>a.getCurrentResult()),f.useEffect(()=>{a.setOptions(i,{listeners:!1})},[i,a]),fS(i,c))throw fp(i,a,s);if(uS({result:c,errorResetBoundary:s,throwOnError:i.throwOnError,query:r.getQueryCache().get(i.queryHash)}))throw c.error;if((S=(g=r.getDefaultOptions().queries)==null?void 0:g._experimental_afterQuery)==null||S.call(g,i,c),i.experimental_prefetchInRender&&!gr&&dS(c,o)){const m=l?fp(i,a,s):(w=r.getQueryCache().get(i.queryHash))==null?void 0:w.promise;m==null||m.catch(Qu).finally(()=>{a.updateResult()})}return i.notifyOnChangeProps?c:a.trackResult(c)}function So(e,t){return pS(e,Z1)}function cl(e,t){const n=Vs(),[r]=f.useState(()=>new tS(n,e));f.useEffect(()=>{r.setOptions(e)},[r,e]);const o=f.useSyncExternalStore(f.useCallback(i=>r.subscribe(be.batchCalls(i)),[r]),()=>r.getCurrentResult(),()=>r.getCurrentResult()),s=f.useCallback((i,l)=>{r.mutate(i,l).catch(Qu)},[r]);if(o.error&&Vv(r.options.throwOnError,[o.error]))throw o.error;return{...o,mutate:s,mutateAsync:o.mutate}}async function Bv(e){if(!e.ok){const t=await e.text()||e.statusText;throw new Error(`${e.status}: ${t}`)}}async function Gu(e,t,n){const r=await fetch(t,{method:e,headers:n?{"Content-Type":"application/json"}:{},body:n?JSON.stringify(n):void 0,credentials:"include"});return await Bv(r),r}const hS=({on401:e})=>async({queryKey:t})=>{const n=await fetch(t[0],{credentials:"include"});return e==="returnNull"&&n.status===401?null:(await Bv(n),await n.json())},mS=new X1({defaultOptions:{queries:{queryFn:hS({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}}),vS=1,gS=1e6;let ja=0;function yS(){return ja=(ja+1)%Number.MAX_SAFE_INTEGER,ja.toString()}const Ta=new Map,pp=e=>{if(Ta.has(e))return;const t=setTimeout(()=>{Ta.delete(e),us({type:"REMOVE_TOAST",toastId:e})},gS);Ta.set(e,t)},xS=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,vS)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(n=>n.id===t.toast.id?{...n,...t.toast}:n)};case"DISMISS_TOAST":{const{toastId:n}=t;return n?pp(n):e.toasts.forEach(r=>{pp(r.id)}),{...e,toasts:e.toasts.map(r=>r.id===n||n===void 0?{...r,open:!1}:r)}}case"REMOVE_TOAST":return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(n=>n.id!==t.toastId)}}},Oi=[];let Mi={toasts:[]};function us(e){Mi=xS(Mi,e),Oi.forEach(t=>{t(Mi)})}function wS({...e}){const t=yS(),n=o=>us({type:"UPDATE_TOAST",toast:{...o,id:t}}),r=()=>us({type:"DISMISS_TOAST",toastId:t});return us({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:o=>{o||r()}}}),{id:t,dismiss:r,update:n}}function md(){const[e,t]=f.useState(Mi);return f.useEffect(()=>(Oi.push(t),()=>{const n=Oi.indexOf(t);n>-1&&Oi.splice(n,1)}),[e]),{...e,toast:wS,dismiss:n=>us({type:"DISMISS_TOAST",toastId:n})}}function K(e,t,{checkForDefaultPrevented:n=!0}={}){return function(o){if(e==null||e(o),n===!1||!o.defaultPrevented)return t==null?void 0:t(o)}}function hp(e,t){if(typeof e=="function")return e(t);e!=null&&(e.current=t)}function Hv(...e){return t=>{let n=!1;const r=e.map(o=>{const s=hp(o,t);return!n&&typeof s=="function"&&(n=!0),s});if(n)return()=>{for(let o=0;o<r.length;o++){const s=r[o];typeof s=="function"?s():hp(e[o],null)}}}}function ie(...e){return f.useCallback(Hv(...e),e)}function kr(e,t=[]){let n=[];function r(s,i){const l=f.createContext(i),a=n.length;n=[...n,i];const c=d=>{var v;const{scope:g,children:S,...w}=d,m=((v=g==null?void 0:g[e])==null?void 0:v[a])||l,x=f.useMemo(()=>w,Object.values(w));return u.jsx(m.Provider,{value:x,children:S})};c.displayName=s+"Provider";function p(d,g){var m;const S=((m=g==null?void 0:g[e])==null?void 0:m[a])||l,w=f.useContext(S);if(w)return w;if(i!==void 0)return i;throw new Error(`\`${d}\` must be used within \`${s}\``)}return[c,p]}const o=()=>{const s=n.map(i=>f.createContext(i));return function(l){const a=(l==null?void 0:l[e])||s;return f.useMemo(()=>({[`__scope${e}`]:{...l,[e]:a}}),[l,a])}};return o.scopeName=e,[r,SS(o,...t)]}function SS(...e){const t=e[0];if(e.length===1)return t;const n=()=>{const r=e.map(o=>({useScope:o(),scopeName:o.scopeName}));return function(s){const i=r.reduce((l,{useScope:a,scopeName:c})=>{const d=a(s)[`__scope${c}`];return{...l,...d}},{});return f.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return n.scopeName=t.scopeName,n}function Rs(e){const t=ES(e),n=f.forwardRef((r,o)=>{const{children:s,...i}=r,l=f.Children.toArray(s),a=l.find(NS);if(a){const c=a.props.children,p=l.map(d=>d===a?f.Children.count(c)>1?f.Children.only(null):f.isValidElement(c)?c.props.children:null:d);return u.jsx(t,{...i,ref:o,children:f.isValidElement(c)?f.cloneElement(c,void 0,p):null})}return u.jsx(t,{...i,ref:o,children:s})});return n.displayName=`${e}.Slot`,n}var CS=Rs("Slot");function ES(e){const t=f.forwardRef((n,r)=>{const{children:o,...s}=n;if(f.isValidElement(o)){const i=PS(o),l=kS(s,o.props);return o.type!==f.Fragment&&(l.ref=r?Hv(r,i):i),f.cloneElement(o,l)}return f.Children.count(o)>1?f.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}var Wv=Symbol("radix.slottable");function bS(e){const t=({children:n})=>u.jsx(u.Fragment,{children:n});return t.displayName=`${e}.Slottable`,t.__radixId=Wv,t}function NS(e){return f.isValidElement(e)&&typeof e.type=="function"&&"__radixId"in e.type&&e.type.__radixId===Wv}function kS(e,t){const n={...t};for(const r in t){const o=e[r],s=t[r];/^on[A-Z]/.test(r)?o&&s?n[r]=(...l)=>{s(...l),o(...l)}:o&&(n[r]=o):r==="style"?n[r]={...o,...s}:r==="className"&&(n[r]=[o,s].filter(Boolean).join(" "))}return{...e,...n}}function PS(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function vd(e){const t=e+"CollectionProvider",[n,r]=kr(t),[o,s]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=m=>{const{scope:x,children:v}=m,h=pn.useRef(null),y=pn.useRef(new Map).current;return u.jsx(o,{scope:x,itemMap:y,collectionRef:h,children:v})};i.displayName=t;const l=e+"CollectionSlot",a=Rs(l),c=pn.forwardRef((m,x)=>{const{scope:v,children:h}=m,y=s(l,v),C=ie(x,y.collectionRef);return u.jsx(a,{ref:C,children:h})});c.displayName=l;const p=e+"CollectionItemSlot",d="data-radix-collection-item",g=Rs(p),S=pn.forwardRef((m,x)=>{const{scope:v,children:h,...y}=m,C=pn.useRef(null),b=ie(x,C),k=s(p,v);return pn.useEffect(()=>(k.itemMap.set(C,{ref:C,...y}),()=>void k.itemMap.delete(C))),u.jsx(g,{[d]:"",ref:b,children:h})});S.displayName=p;function w(m){const x=s(e+"CollectionConsumer",m);return pn.useCallback(()=>{const h=x.collectionRef.current;if(!h)return[];const y=Array.from(h.querySelectorAll(`[${d}]`));return Array.from(x.itemMap.values()).sort((k,N)=>y.indexOf(k.ref.current)-y.indexOf(N.ref.current))},[x.collectionRef,x.itemMap])}return[{Provider:i,Slot:c,ItemSlot:S},w,r]}var jS=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"],ee=jS.reduce((e,t)=>{const n=Rs(`Primitive.${t}`),r=f.forwardRef((o,s)=>{const{asChild:i,...l}=o,a=i?n:t;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),u.jsx(a,{...l,ref:s})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function Kv(e,t){e&&Nr.flushSync(()=>e.dispatchEvent(t))}function ut(e){const t=f.useRef(e);return f.useEffect(()=>{t.current=e}),f.useMemo(()=>(...n)=>{var r;return(r=t.current)==null?void 0:r.call(t,...n)},[])}function TS(e,t=globalThis==null?void 0:globalThis.document){const n=ut(e);f.useEffect(()=>{const r=o=>{o.key==="Escape"&&n(o)};return t.addEventListener("keydown",r,{capture:!0}),()=>t.removeEventListener("keydown",r,{capture:!0})},[n,t])}var RS="DismissableLayer",qu="dismissableLayer.update",_S="dismissableLayer.pointerDownOutside",OS="dismissableLayer.focusOutside",mp,Qv=f.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),Dl=f.forwardRef((e,t)=>{const{disableOutsidePointerEvents:n=!1,onEscapeKeyDown:r,onPointerDownOutside:o,onFocusOutside:s,onInteractOutside:i,onDismiss:l,...a}=e,c=f.useContext(Qv),[p,d]=f.useState(null),g=(p==null?void 0:p.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,S]=f.useState({}),w=ie(t,N=>d(N)),m=Array.from(c.layers),[x]=[...c.layersWithOutsidePointerEventsDisabled].slice(-1),v=m.indexOf(x),h=p?m.indexOf(p):-1,y=c.layersWithOutsidePointerEventsDisabled.size>0,C=h>=v,b=AS(N=>{const P=N.target,_=[...c.branches].some(O=>O.contains(P));!C||_||(o==null||o(N),i==null||i(N),N.defaultPrevented||l==null||l())},g),k=IS(N=>{const P=N.target;[...c.branches].some(O=>O.contains(P))||(s==null||s(N),i==null||i(N),N.defaultPrevented||l==null||l())},g);return TS(N=>{h===c.layers.size-1&&(r==null||r(N),!N.defaultPrevented&&l&&(N.preventDefault(),l()))},g),f.useEffect(()=>{if(p)return n&&(c.layersWithOutsidePointerEventsDisabled.size===0&&(mp=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),c.layersWithOutsidePointerEventsDisabled.add(p)),c.layers.add(p),vp(),()=>{n&&c.layersWithOutsidePointerEventsDisabled.size===1&&(g.body.style.pointerEvents=mp)}},[p,g,n,c]),f.useEffect(()=>()=>{p&&(c.layers.delete(p),c.layersWithOutsidePointerEventsDisabled.delete(p),vp())},[p,c]),f.useEffect(()=>{const N=()=>S({});return document.addEventListener(qu,N),()=>document.removeEventListener(qu,N)},[]),u.jsx(ee.div,{...a,ref:w,style:{pointerEvents:y?C?"auto":"none":void 0,...e.style},onFocusCapture:K(e.onFocusCapture,k.onFocusCapture),onBlurCapture:K(e.onBlurCapture,k.onBlurCapture),onPointerDownCapture:K(e.onPointerDownCapture,b.onPointerDownCapture)})});Dl.displayName=RS;var MS="DismissableLayerBranch",Gv=f.forwardRef((e,t)=>{const n=f.useContext(Qv),r=f.useRef(null),o=ie(t,r);return f.useEffect(()=>{const s=r.current;if(s)return n.branches.add(s),()=>{n.branches.delete(s)}},[n.branches]),u.jsx(ee.div,{...e,ref:o})});Gv.displayName=MS;function AS(e,t=globalThis==null?void 0:globalThis.document){const n=ut(e),r=f.useRef(!1),o=f.useRef(()=>{});return f.useEffect(()=>{const s=l=>{if(l.target&&!r.current){let a=function(){qv(_S,n,c,{discrete:!0})};const c={originalEvent:l};l.pointerType==="touch"?(t.removeEventListener("click",o.current),o.current=a,t.addEventListener("click",o.current,{once:!0})):a()}else t.removeEventListener("click",o.current);r.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",s)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",s),t.removeEventListener("click",o.current)}},[t,n]),{onPointerDownCapture:()=>r.current=!0}}function IS(e,t=globalThis==null?void 0:globalThis.document){const n=ut(e),r=f.useRef(!1);return f.useEffect(()=>{const o=s=>{s.target&&!r.current&&qv(OS,n,{originalEvent:s},{discrete:!1})};return t.addEventListener("focusin",o),()=>t.removeEventListener("focusin",o)},[t,n]),{onFocusCapture:()=>r.current=!0,onBlurCapture:()=>r.current=!1}}function vp(){const e=new CustomEvent(qu);document.dispatchEvent(e)}function qv(e,t,n,{discrete:r}){const o=n.originalEvent.target,s=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Kv(o,s):o.dispatchEvent(s)}var LS=Dl,DS=Gv,De=globalThis!=null&&globalThis.document?f.useLayoutEffect:()=>{},FS="Portal",gd=f.forwardRef((e,t)=>{var l;const{container:n,...r}=e,[o,s]=f.useState(!1);De(()=>s(!0),[]);const i=n||o&&((l=globalThis==null?void 0:globalThis.document)==null?void 0:l.body);return i?l1.createPortal(u.jsx(ee.div,{...r,ref:t}),i):null});gd.displayName=FS;function zS(e,t){return f.useReducer((n,r)=>t[n][r]??n,e)}var yd=e=>{const{present:t,children:n}=e,r=$S(t),o=typeof n=="function"?n({present:r.isPresent}):f.Children.only(n),s=ie(r.ref,US(o));return typeof n=="function"||r.isPresent?f.cloneElement(o,{ref:s}):null};yd.displayName="Presence";function $S(e){const[t,n]=f.useState(),r=f.useRef({}),o=f.useRef(e),s=f.useRef("none"),i=e?"mounted":"unmounted",[l,a]=zS(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return f.useEffect(()=>{const c=fi(r.current);s.current=l==="mounted"?c:"none"},[l]),De(()=>{const c=r.current,p=o.current;if(p!==e){const g=s.current,S=fi(c);e?a("MOUNT"):S==="none"||(c==null?void 0:c.display)==="none"?a("UNMOUNT"):a(p&&g!==S?"ANIMATION_OUT":"UNMOUNT"),o.current=e}},[e,a]),De(()=>{if(t){let c;const p=t.ownerDocument.defaultView??window,d=S=>{const m=fi(r.current).includes(S.animationName);if(S.target===t&&m&&(a("ANIMATION_END"),!o.current)){const x=t.style.animationFillMode;t.style.animationFillMode="forwards",c=p.setTimeout(()=>{t.style.animationFillMode==="forwards"&&(t.style.animationFillMode=x)})}},g=S=>{S.target===t&&(s.current=fi(r.current))};return t.addEventListener("animationstart",g),t.addEventListener("animationcancel",d),t.addEventListener("animationend",d),()=>{p.clearTimeout(c),t.removeEventListener("animationstart",g),t.removeEventListener("animationcancel",d),t.removeEventListener("animationend",d)}}else a("ANIMATION_END")},[t,a]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:f.useCallback(c=>{c&&(r.current=getComputedStyle(c)),n(c)},[])}}function fi(e){return(e==null?void 0:e.animationName)||"none"}function US(e){var r,o;let t=(r=Object.getOwnPropertyDescriptor(e.props,"ref"))==null?void 0:r.get,n=t&&"isReactWarning"in t&&t.isReactWarning;return n?e.ref:(t=(o=Object.getOwnPropertyDescriptor(e,"ref"))==null?void 0:o.get,n=t&&"isReactWarning"in t&&t.isReactWarning,n?e.props.ref:e.props.ref||e.ref)}function dl({prop:e,defaultProp:t,onChange:n=()=>{}}){const[r,o]=VS({defaultProp:t,onChange:n}),s=e!==void 0,i=s?e:r,l=ut(n),a=f.useCallback(c=>{if(s){const d=typeof c=="function"?c(e):c;d!==e&&l(d)}else o(c)},[s,e,o,l]);return[i,a]}function VS({defaultProp:e,onChange:t}){const n=f.useState(e),[r]=n,o=f.useRef(r),s=ut(t);return f.useEffect(()=>{o.current!==r&&(s(r),o.current=r)},[r,o,s]),n}var BS="VisuallyHidden",Bs=f.forwardRef((e,t)=>u.jsx(ee.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));Bs.displayName=BS;var HS=Bs,xd="ToastProvider",[wd,WS,KS]=vd("Toast"),[Yv,ek]=kr("Toast",[KS]),[QS,Fl]=Yv(xd),Xv=e=>{const{__scopeToast:t,label:n="Notification",duration:r=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:i}=e,[l,a]=f.useState(null),[c,p]=f.useState(0),d=f.useRef(!1),g=f.useRef(!1);return n.trim()||console.error(`Invalid prop \`label\` supplied to \`${xd}\`. Expected non-empty \`string\`.`),u.jsx(wd.Provider,{scope:t,children:u.jsx(QS,{scope:t,label:n,duration:r,swipeDirection:o,swipeThreshold:s,toastCount:c,viewport:l,onViewportChange:a,onToastAdd:f.useCallback(()=>p(S=>S+1),[]),onToastRemove:f.useCallback(()=>p(S=>S-1),[]),isFocusedToastEscapeKeyDownRef:d,isClosePausedRef:g,children:i})})};Xv.displayName=xd;var Zv="ToastViewport",GS=["F8"],Yu="toast.viewportPause",Xu="toast.viewportResume",Jv=f.forwardRef((e,t)=>{const{__scopeToast:n,hotkey:r=GS,label:o="Notifications ({hotkey})",...s}=e,i=Fl(Zv,n),l=WS(n),a=f.useRef(null),c=f.useRef(null),p=f.useRef(null),d=f.useRef(null),g=ie(t,d,i.onViewportChange),S=r.join("+").replace(/Key/g,"").replace(/Digit/g,""),w=i.toastCount>0;f.useEffect(()=>{const x=v=>{var y;r.length!==0&&r.every(C=>v[C]||v.code===C)&&((y=d.current)==null||y.focus())};return document.addEventListener("keydown",x),()=>document.removeEventListener("keydown",x)},[r]),f.useEffect(()=>{const x=a.current,v=d.current;if(w&&x&&v){const h=()=>{if(!i.isClosePausedRef.current){const k=new CustomEvent(Yu);v.dispatchEvent(k),i.isClosePausedRef.current=!0}},y=()=>{if(i.isClosePausedRef.current){const k=new CustomEvent(Xu);v.dispatchEvent(k),i.isClosePausedRef.current=!1}},C=k=>{!x.contains(k.relatedTarget)&&y()},b=()=>{x.contains(document.activeElement)||y()};return x.addEventListener("focusin",h),x.addEventListener("focusout",C),x.addEventListener("pointermove",h),x.addEventListener("pointerleave",b),window.addEventListener("blur",h),window.addEventListener("focus",y),()=>{x.removeEventListener("focusin",h),x.removeEventListener("focusout",C),x.removeEventListener("pointermove",h),x.removeEventListener("pointerleave",b),window.removeEventListener("blur",h),window.removeEventListener("focus",y)}}},[w,i.isClosePausedRef]);const m=f.useCallback(({tabbingDirection:x})=>{const h=l().map(y=>{const C=y.ref.current,b=[C,...lC(C)];return x==="forwards"?b:b.reverse()});return(x==="forwards"?h.reverse():h).flat()},[l]);return f.useEffect(()=>{const x=d.current;if(x){const v=h=>{var b,k,N;const y=h.altKey||h.ctrlKey||h.metaKey;if(h.key==="Tab"&&!y){const P=document.activeElement,_=h.shiftKey;if(h.target===x&&_){(b=c.current)==null||b.focus();return}const M=m({tabbingDirection:_?"backwards":"forwards"}),L=M.findIndex(T=>T===P);Ra(M.slice(L+1))?h.preventDefault():_?(k=c.current)==null||k.focus():(N=p.current)==null||N.focus()}};return x.addEventListener("keydown",v),()=>x.removeEventListener("keydown",v)}},[l,m]),u.jsxs(DS,{ref:a,role:"region","aria-label":o.replace("{hotkey}",S),tabIndex:-1,style:{pointerEvents:w?void 0:"none"},children:[w&&u.jsx(Zu,{ref:c,onFocusFromOutsideViewport:()=>{const x=m({tabbingDirection:"forwards"});Ra(x)}}),u.jsx(wd.Slot,{scope:n,children:u.jsx(ee.ol,{tabIndex:-1,...s,ref:g})}),w&&u.jsx(Zu,{ref:p,onFocusFromOutsideViewport:()=>{const x=m({tabbingDirection:"backwards"});Ra(x)}})]})});Jv.displayName=Zv;var eg="ToastFocusProxy",Zu=f.forwardRef((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r,...o}=e,s=Fl(eg,n);return u.jsx(Bs,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:i=>{var c;const l=i.relatedTarget;!((c=s.viewport)!=null&&c.contains(l))&&r()}})});Zu.displayName=eg;var zl="Toast",qS="toast.swipeStart",YS="toast.swipeMove",XS="toast.swipeCancel",ZS="toast.swipeEnd",tg=f.forwardRef((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:s,...i}=e,[l=!0,a]=dl({prop:r,defaultProp:o,onChange:s});return u.jsx(yd,{present:n||l,children:u.jsx(tC,{open:l,...i,ref:t,onClose:()=>a(!1),onPause:ut(e.onPause),onResume:ut(e.onResume),onSwipeStart:K(e.onSwipeStart,c=>{c.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:K(e.onSwipeMove,c=>{const{x:p,y:d}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","move"),c.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${p}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${d}px`)}),onSwipeCancel:K(e.onSwipeCancel,c=>{c.currentTarget.setAttribute("data-swipe","cancel"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:K(e.onSwipeEnd,c=>{const{x:p,y:d}=c.detail.delta;c.currentTarget.setAttribute("data-swipe","end"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),c.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),c.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${p}px`),c.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${d}px`),a(!1)})})})});tg.displayName=zl;var[JS,eC]=Yv(zl,{onClose(){}}),tC=f.forwardRef((e,t)=>{const{__scopeToast:n,type:r="foreground",duration:o,open:s,onClose:i,onEscapeKeyDown:l,onPause:a,onResume:c,onSwipeStart:p,onSwipeMove:d,onSwipeCancel:g,onSwipeEnd:S,...w}=e,m=Fl(zl,n),[x,v]=f.useState(null),h=ie(t,T=>v(T)),y=f.useRef(null),C=f.useRef(null),b=o||m.duration,k=f.useRef(0),N=f.useRef(b),P=f.useRef(0),{onToastAdd:_,onToastRemove:O}=m,I=ut(()=>{var U;(x==null?void 0:x.contains(document.activeElement))&&((U=m.viewport)==null||U.focus()),i()}),M=f.useCallback(T=>{!T||T===1/0||(window.clearTimeout(P.current),k.current=new Date().getTime(),P.current=window.setTimeout(I,T))},[I]);f.useEffect(()=>{const T=m.viewport;if(T){const U=()=>{M(N.current),c==null||c()},z=()=>{const B=new Date().getTime()-k.current;N.current=N.current-B,window.clearTimeout(P.current),a==null||a()};return T.addEventListener(Yu,z),T.addEventListener(Xu,U),()=>{T.removeEventListener(Yu,z),T.removeEventListener(Xu,U)}}},[m.viewport,b,a,c,M]),f.useEffect(()=>{s&&!m.isClosePausedRef.current&&M(b)},[s,b,m.isClosePausedRef,M]),f.useEffect(()=>(_(),()=>O()),[_,O]);const L=f.useMemo(()=>x?ag(x):null,[x]);return m.viewport?u.jsxs(u.Fragment,{children:[L&&u.jsx(nC,{__scopeToast:n,role:"status","aria-live":r==="foreground"?"assertive":"polite","aria-atomic":!0,children:L}),u.jsx(JS,{scope:n,onClose:I,children:Nr.createPortal(u.jsx(wd.ItemSlot,{scope:n,children:u.jsx(LS,{asChild:!0,onEscapeKeyDown:K(l,()=>{m.isFocusedToastEscapeKeyDownRef.current||I(),m.isFocusedToastEscapeKeyDownRef.current=!1}),children:u.jsx(ee.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":m.swipeDirection,...w,ref:h,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:K(e.onKeyDown,T=>{T.key==="Escape"&&(l==null||l(T.nativeEvent),T.nativeEvent.defaultPrevented||(m.isFocusedToastEscapeKeyDownRef.current=!0,I()))}),onPointerDown:K(e.onPointerDown,T=>{T.button===0&&(y.current={x:T.clientX,y:T.clientY})}),onPointerMove:K(e.onPointerMove,T=>{if(!y.current)return;const U=T.clientX-y.current.x,z=T.clientY-y.current.y,B=!!C.current,j=["left","right"].includes(m.swipeDirection),A=["left","up"].includes(m.swipeDirection)?Math.min:Math.max,$=j?A(0,U):0,W=j?0:A(0,z),re=T.pointerType==="touch"?10:2,Fe={x:$,y:W},Ce={originalEvent:T,delta:Fe};B?(C.current=Fe,pi(YS,d,Ce,{discrete:!1})):gp(Fe,m.swipeDirection,re)?(C.current=Fe,pi(qS,p,Ce,{discrete:!1}),T.target.setPointerCapture(T.pointerId)):(Math.abs(U)>re||Math.abs(z)>re)&&(y.current=null)}),onPointerUp:K(e.onPointerUp,T=>{const U=C.current,z=T.target;if(z.hasPointerCapture(T.pointerId)&&z.releasePointerCapture(T.pointerId),C.current=null,y.current=null,U){const B=T.currentTarget,j={originalEvent:T,delta:U};gp(U,m.swipeDirection,m.swipeThreshold)?pi(ZS,S,j,{discrete:!0}):pi(XS,g,j,{discrete:!0}),B.addEventListener("click",A=>A.preventDefault(),{once:!0})}})})})}),m.viewport)})]}):null}),nC=e=>{const{__scopeToast:t,children:n,...r}=e,o=Fl(zl,t),[s,i]=f.useState(!1),[l,a]=f.useState(!1);return sC(()=>i(!0)),f.useEffect(()=>{const c=window.setTimeout(()=>a(!0),1e3);return()=>window.clearTimeout(c)},[]),l?null:u.jsx(gd,{asChild:!0,children:u.jsx(Bs,{...r,children:s&&u.jsxs(u.Fragment,{children:[o.label," ",n]})})})},rC="ToastTitle",ng=f.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return u.jsx(ee.div,{...r,ref:t})});ng.displayName=rC;var oC="ToastDescription",rg=f.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e;return u.jsx(ee.div,{...r,ref:t})});rg.displayName=oC;var og="ToastAction",sg=f.forwardRef((e,t)=>{const{altText:n,...r}=e;return n.trim()?u.jsx(lg,{altText:n,asChild:!0,children:u.jsx(Sd,{...r,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${og}\`. Expected non-empty \`string\`.`),null)});sg.displayName=og;var ig="ToastClose",Sd=f.forwardRef((e,t)=>{const{__scopeToast:n,...r}=e,o=eC(ig,n);return u.jsx(lg,{asChild:!0,children:u.jsx(ee.button,{type:"button",...r,ref:t,onClick:K(e.onClick,o.onClose)})})});Sd.displayName=ig;var lg=f.forwardRef((e,t)=>{const{__scopeToast:n,altText:r,...o}=e;return u.jsx(ee.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0,...o,ref:t})});function ag(e){const t=[];return Array.from(e.childNodes).forEach(r=>{if(r.nodeType===r.TEXT_NODE&&r.textContent&&t.push(r.textContent),iC(r)){const o=r.ariaHidden||r.hidden||r.style.display==="none",s=r.dataset.radixToastAnnounceExclude==="";if(!o)if(s){const i=r.dataset.radixToastAnnounceAlt;i&&t.push(i)}else t.push(...ag(r))}}),t}function pi(e,t,n,{discrete:r}){const o=n.originalEvent.currentTarget,s=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&o.addEventListener(e,t,{once:!0}),r?Kv(o,s):o.dispatchEvent(s)}var gp=(e,t,n=0)=>{const r=Math.abs(e.x),o=Math.abs(e.y),s=r>o;return t==="left"||t==="right"?s&&r>n:!s&&o>n};function sC(e=()=>{}){const t=ut(e);De(()=>{let n=0,r=0;return n=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(n),window.cancelAnimationFrame(r)}},[t])}function iC(e){return e.nodeType===e.ELEMENT_NODE}function lC(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Ra(e){const t=document.activeElement;return e.some(n=>n===t?!0:(n.focus(),document.activeElement!==t))}var aC=Xv,ug=Jv,cg=tg,dg=ng,fg=rg,pg=sg,hg=Sd;function mg(e){var t,n,r="";if(typeof e=="string"||typeof e=="number")r+=e;else if(typeof e=="object")if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=mg(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function vg(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=mg(e))&&(r&&(r+=" "),r+=t);return r}const yp=e=>typeof e=="boolean"?`${e}`:e===0?"0":e,xp=vg,Cd=(e,t)=>n=>{var r;if((t==null?void 0:t.variants)==null)return xp(e,n==null?void 0:n.class,n==null?void 0:n.className);const{variants:o,defaultVariants:s}=t,i=Object.keys(o).map(c=>{const p=n==null?void 0:n[c],d=s==null?void 0:s[c];if(p===null)return null;const g=yp(p)||yp(d);return o[c][g]}),l=n&&Object.entries(n).reduce((c,p)=>{let[d,g]=p;return g===void 0||(c[d]=g),c},{}),a=t==null||(r=t.compoundVariants)===null||r===void 0?void 0:r.reduce((c,p)=>{let{class:d,className:g,...S}=p;return Object.entries(S).every(w=>{let[m,x]=w;return Array.isArray(x)?x.includes({...s,...l}[m]):{...s,...l}[m]===x})?[...c,d,g]:c},[]);return xp(e,i,a,n==null?void 0:n.class,n==null?void 0:n.className)};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const uC=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),gg=(...e)=>e.filter((t,n,r)=>!!t&&r.indexOf(t)===n).join(" ");/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var cC={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const dC=f.forwardRef(({color:e="currentColor",size:t=24,strokeWidth:n=2,absoluteStrokeWidth:r,className:o="",children:s,iconNode:i,...l},a)=>f.createElement("svg",{ref:a,...cC,width:t,height:t,stroke:e,strokeWidth:r?Number(n)*24/Number(t):n,className:gg("lucide",o),...l},[...i.map(([c,p])=>f.createElement(c,p)),...Array.isArray(s)?s:[s]]));/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Z=(e,t)=>{const n=f.forwardRef(({className:r,...o},s)=>f.createElement(dC,{ref:s,iconNode:t,className:gg(`lucide-${uC(e)}`,r),...o}));return n.displayName=`${e}`,n};/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wp=Z("Activity",[["path",{d:"M22 12h-2.48a2 2 0 0 0-1.93 1.46l-2.35 8.36a.25.25 0 0 1-.48 0L9.24 2.18a.25.25 0 0 0-.48 0l-2.35 8.36A2 2 0 0 1 4.49 12H2",key:"169zse"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sp=Z("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fC=Z("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _a=Z("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pC=Z("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yg=Z("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const hC=Z("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mC=Z("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vC=Z("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xg=Z("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const gC=Z("Code",[["polyline",{points:"16 18 22 12 16 6",key:"z7tu5w"}],["polyline",{points:"8 6 2 12 8 18",key:"1eg1df"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const $l=Z("Eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yC=Z("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xC=Z("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wC=Z("Maximize",[["path",{d:"M8 3H5a2 2 0 0 0-2 2v3",key:"1dcmit"}],["path",{d:"M21 8V5a2 2 0 0 0-2-2h-3",key:"1e4gt3"}],["path",{d:"M3 16v3a2 2 0 0 0 2 2h3",key:"wsl5sc"}],["path",{d:"M16 21h3a2 2 0 0 0 2-2v-3",key:"18trek"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const SC=Z("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Co=Z("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cp=Z("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ep=Z("Radio",[["path",{d:"M4.9 19.1C1 15.2 1 8.8 4.9 4.9",key:"1vaf9d"}],["path",{d:"M7.8 16.2c-2.3-2.3-2.3-6.1 0-8.5",key:"u1ii0m"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}],["path",{d:"M16.2 7.8c2.3 2.3 2.3 6.1 0 8.5",key:"1j5fej"}],["path",{d:"M19.1 4.9C23 8.8 23 15.1 19.1 19",key:"10b0cb"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const CC=Z("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const EC=Z("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const bC=Z("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ed=Z("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const NC=Z("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const kC=Z("SquarePen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wg=Z("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const PC=Z("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fl=Z("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const cs=Z("Video",[["path",{d:"m16 13 5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5",key:"ftymec"}],["rect",{x:"2",y:"6",width:"14",height:"12",rx:"2",key:"158x01"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const jC=Z("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const TC=Z("VolumeX",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["line",{x1:"22",x2:"16",y1:"9",y2:"15",key:"1ewh16"}],["line",{x1:"16",x2:"22",y1:"9",y2:"15",key:"5ykzw1"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sg=Z("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const RC=Z("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]);/**
 * @license lucide-react v0.453.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _C=Z("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),bd="-",OC=e=>{const t=AC(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:i=>{const l=i.split(bd);return l[0]===""&&l.length!==1&&l.shift(),Cg(l,t)||MC(i)},getConflictingClassGroupIds:(i,l)=>{const a=n[i]||[];return l&&r[i]?[...a,...r[i]]:a}}},Cg=(e,t)=>{var i;if(e.length===0)return t.classGroupId;const n=e[0],r=t.nextPart.get(n),o=r?Cg(e.slice(1),r):void 0;if(o)return o;if(t.validators.length===0)return;const s=e.join(bd);return(i=t.validators.find(({validator:l})=>l(s)))==null?void 0:i.classGroupId},bp=/^\[(.+)\]$/,MC=e=>{if(bp.test(e)){const t=bp.exec(e)[1],n=t==null?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},AC=e=>{const{theme:t,prefix:n}=e,r={nextPart:new Map,validators:[]};return LC(Object.entries(e.classGroups),n).forEach(([s,i])=>{Ju(i,r,s,t)}),r},Ju=(e,t,n,r)=>{e.forEach(o=>{if(typeof o=="string"){const s=o===""?t:Np(t,o);s.classGroupId=n;return}if(typeof o=="function"){if(IC(o)){Ju(o(r),t,n,r);return}t.validators.push({validator:o,classGroupId:n});return}Object.entries(o).forEach(([s,i])=>{Ju(i,Np(t,s),n,r)})})},Np=(e,t)=>{let n=e;return t.split(bd).forEach(r=>{n.nextPart.has(r)||n.nextPart.set(r,{nextPart:new Map,validators:[]}),n=n.nextPart.get(r)}),n},IC=e=>e.isThemeGetter,LC=(e,t)=>t?e.map(([n,r])=>{const o=r.map(s=>typeof s=="string"?t+s:typeof s=="object"?Object.fromEntries(Object.entries(s).map(([i,l])=>[t+i,l])):s);return[n,o]}):e,DC=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(s,i)=>{n.set(s,i),t++,t>e&&(t=0,r=n,n=new Map)};return{get(s){let i=n.get(s);if(i!==void 0)return i;if((i=r.get(s))!==void 0)return o(s,i),i},set(s,i){n.has(s)?n.set(s,i):o(s,i)}}},Eg="!",FC=e=>{const{separator:t,experimentalParseClassName:n}=e,r=t.length===1,o=t[0],s=t.length,i=l=>{const a=[];let c=0,p=0,d;for(let x=0;x<l.length;x++){let v=l[x];if(c===0){if(v===o&&(r||l.slice(x,x+s)===t)){a.push(l.slice(p,x)),p=x+s;continue}if(v==="/"){d=x;continue}}v==="["?c++:v==="]"&&c--}const g=a.length===0?l:l.substring(p),S=g.startsWith(Eg),w=S?g.substring(1):g,m=d&&d>p?d-p:void 0;return{modifiers:a,hasImportantModifier:S,baseClassName:w,maybePostfixModifierPosition:m}};return n?l=>n({className:l,parseClassName:i}):i},zC=e=>{if(e.length<=1)return e;const t=[];let n=[];return e.forEach(r=>{r[0]==="["?(t.push(...n.sort(),r),n=[]):n.push(r)}),t.push(...n.sort()),t},$C=e=>({cache:DC(e.cacheSize),parseClassName:FC(e),...OC(e)}),UC=/\s+/,VC=(e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o}=t,s=[],i=e.trim().split(UC);let l="";for(let a=i.length-1;a>=0;a-=1){const c=i[a],{modifiers:p,hasImportantModifier:d,baseClassName:g,maybePostfixModifierPosition:S}=n(c);let w=!!S,m=r(w?g.substring(0,S):g);if(!m){if(!w){l=c+(l.length>0?" "+l:l);continue}if(m=r(g),!m){l=c+(l.length>0?" "+l:l);continue}w=!1}const x=zC(p).join(":"),v=d?x+Eg:x,h=v+m;if(s.includes(h))continue;s.push(h);const y=o(m,w);for(let C=0;C<y.length;++C){const b=y[C];s.push(v+b)}l=c+(l.length>0?" "+l:l)}return l};function BC(){let e=0,t,n,r="";for(;e<arguments.length;)(t=arguments[e++])&&(n=bg(t))&&(r&&(r+=" "),r+=n);return r}const bg=e=>{if(typeof e=="string")return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=bg(e[r]))&&(n&&(n+=" "),n+=t);return n};function HC(e,...t){let n,r,o,s=i;function i(a){const c=t.reduce((p,d)=>d(p),e());return n=$C(c),r=n.cache.get,o=n.cache.set,s=l,l(a)}function l(a){const c=r(a);if(c)return c;const p=VC(a,n);return o(a,p),p}return function(){return s(BC.apply(null,arguments))}}const ce=e=>{const t=n=>n[e]||[];return t.isThemeGetter=!0,t},Ng=/^\[(?:([a-z-]+):)?(.+)\]$/i,WC=/^\d+\/\d+$/,KC=new Set(["px","full","screen"]),QC=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,GC=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,qC=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,YC=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,XC=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Qt=e=>Yr(e)||KC.has(e)||WC.test(e),dn=e=>To(e,"length",sE),Yr=e=>!!e&&!Number.isNaN(Number(e)),Oa=e=>To(e,"number",Yr),Wo=e=>!!e&&Number.isInteger(Number(e)),ZC=e=>e.endsWith("%")&&Yr(e.slice(0,-1)),Q=e=>Ng.test(e),fn=e=>QC.test(e),JC=new Set(["length","size","percentage"]),eE=e=>To(e,JC,kg),tE=e=>To(e,"position",kg),nE=new Set(["image","url"]),rE=e=>To(e,nE,lE),oE=e=>To(e,"",iE),Ko=()=>!0,To=(e,t,n)=>{const r=Ng.exec(e);return r?r[1]?typeof t=="string"?r[1]===t:t.has(r[1]):n(r[2]):!1},sE=e=>GC.test(e)&&!qC.test(e),kg=()=>!1,iE=e=>YC.test(e),lE=e=>XC.test(e),aE=()=>{const e=ce("colors"),t=ce("spacing"),n=ce("blur"),r=ce("brightness"),o=ce("borderColor"),s=ce("borderRadius"),i=ce("borderSpacing"),l=ce("borderWidth"),a=ce("contrast"),c=ce("grayscale"),p=ce("hueRotate"),d=ce("invert"),g=ce("gap"),S=ce("gradientColorStops"),w=ce("gradientColorStopPositions"),m=ce("inset"),x=ce("margin"),v=ce("opacity"),h=ce("padding"),y=ce("saturate"),C=ce("scale"),b=ce("sepia"),k=ce("skew"),N=ce("space"),P=ce("translate"),_=()=>["auto","contain","none"],O=()=>["auto","hidden","clip","visible","scroll"],I=()=>["auto",Q,t],M=()=>[Q,t],L=()=>["",Qt,dn],T=()=>["auto",Yr,Q],U=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],z=()=>["solid","dashed","dotted","double","none"],B=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],j=()=>["start","end","center","between","around","evenly","stretch"],A=()=>["","0",Q],$=()=>["auto","avoid","all","avoid-page","page","left","right","column"],W=()=>[Yr,Q];return{cacheSize:500,separator:":",theme:{colors:[Ko],spacing:[Qt,dn],blur:["none","",fn,Q],brightness:W(),borderColor:[e],borderRadius:["none","","full",fn,Q],borderSpacing:M(),borderWidth:L(),contrast:W(),grayscale:A(),hueRotate:W(),invert:A(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[ZC,dn],inset:I(),margin:I(),opacity:W(),padding:M(),saturate:W(),scale:W(),sepia:A(),skew:W(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",Q]}],container:["container"],columns:[{columns:[fn]}],"break-after":[{"break-after":$()}],"break-before":[{"break-before":$()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...U(),Q]}],overflow:[{overflow:O()}],"overflow-x":[{"overflow-x":O()}],"overflow-y":[{"overflow-y":O()}],overscroll:[{overscroll:_()}],"overscroll-x":[{"overscroll-x":_()}],"overscroll-y":[{"overscroll-y":_()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Wo,Q]}],basis:[{basis:I()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",Q]}],grow:[{grow:A()}],shrink:[{shrink:A()}],order:[{order:["first","last","none",Wo,Q]}],"grid-cols":[{"grid-cols":[Ko]}],"col-start-end":[{col:["auto",{span:["full",Wo,Q]},Q]}],"col-start":[{"col-start":T()}],"col-end":[{"col-end":T()}],"grid-rows":[{"grid-rows":[Ko]}],"row-start-end":[{row:["auto",{span:[Wo,Q]},Q]}],"row-start":[{"row-start":T()}],"row-end":[{"row-end":T()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",Q]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",Q]}],gap:[{gap:[g]}],"gap-x":[{"gap-x":[g]}],"gap-y":[{"gap-y":[g]}],"justify-content":[{justify:["normal",...j()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...j(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...j(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[h]}],px:[{px:[h]}],py:[{py:[h]}],ps:[{ps:[h]}],pe:[{pe:[h]}],pt:[{pt:[h]}],pr:[{pr:[h]}],pb:[{pb:[h]}],pl:[{pl:[h]}],m:[{m:[x]}],mx:[{mx:[x]}],my:[{my:[x]}],ms:[{ms:[x]}],me:[{me:[x]}],mt:[{mt:[x]}],mr:[{mr:[x]}],mb:[{mb:[x]}],ml:[{ml:[x]}],"space-x":[{"space-x":[N]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[N]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",Q,t]}],"min-w":[{"min-w":[Q,t,"min","max","fit"]}],"max-w":[{"max-w":[Q,t,"none","full","min","max","fit","prose",{screen:[fn]},fn]}],h:[{h:[Q,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[Q,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[Q,t,"auto","min","max","fit"]}],"font-size":[{text:["base",fn,dn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",Oa]}],"font-family":[{font:[Ko]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",Q]}],"line-clamp":[{"line-clamp":["none",Yr,Oa]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",Qt,Q]}],"list-image":[{"list-image":["none",Q]}],"list-style-type":[{list:["none","disc","decimal",Q]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...z(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",Qt,dn]}],"underline-offset":[{"underline-offset":["auto",Qt,Q]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",Q]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",Q]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...U(),tE]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",eE]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},rE]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[w]}],"gradient-via-pos":[{via:[w]}],"gradient-to-pos":[{to:[w]}],"gradient-from":[{from:[S]}],"gradient-via":[{via:[S]}],"gradient-to":[{to:[S]}],rounded:[{rounded:[s]}],"rounded-s":[{"rounded-s":[s]}],"rounded-e":[{"rounded-e":[s]}],"rounded-t":[{"rounded-t":[s]}],"rounded-r":[{"rounded-r":[s]}],"rounded-b":[{"rounded-b":[s]}],"rounded-l":[{"rounded-l":[s]}],"rounded-ss":[{"rounded-ss":[s]}],"rounded-se":[{"rounded-se":[s]}],"rounded-ee":[{"rounded-ee":[s]}],"rounded-es":[{"rounded-es":[s]}],"rounded-tl":[{"rounded-tl":[s]}],"rounded-tr":[{"rounded-tr":[s]}],"rounded-br":[{"rounded-br":[s]}],"rounded-bl":[{"rounded-bl":[s]}],"border-w":[{border:[l]}],"border-w-x":[{"border-x":[l]}],"border-w-y":[{"border-y":[l]}],"border-w-s":[{"border-s":[l]}],"border-w-e":[{"border-e":[l]}],"border-w-t":[{"border-t":[l]}],"border-w-r":[{"border-r":[l]}],"border-w-b":[{"border-b":[l]}],"border-w-l":[{"border-l":[l]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...z(),"hidden"]}],"divide-x":[{"divide-x":[l]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[l]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:z()}],"border-color":[{border:[o]}],"border-color-x":[{"border-x":[o]}],"border-color-y":[{"border-y":[o]}],"border-color-s":[{"border-s":[o]}],"border-color-e":[{"border-e":[o]}],"border-color-t":[{"border-t":[o]}],"border-color-r":[{"border-r":[o]}],"border-color-b":[{"border-b":[o]}],"border-color-l":[{"border-l":[o]}],"divide-color":[{divide:[o]}],"outline-style":[{outline:["",...z()]}],"outline-offset":[{"outline-offset":[Qt,Q]}],"outline-w":[{outline:[Qt,dn]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:L()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[Qt,dn]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",fn,oE]}],"shadow-color":[{shadow:[Ko]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...B(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":B()}],filter:[{filter:["","none"]}],blur:[{blur:[n]}],brightness:[{brightness:[r]}],contrast:[{contrast:[a]}],"drop-shadow":[{"drop-shadow":["","none",fn,Q]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[p]}],invert:[{invert:[d]}],saturate:[{saturate:[y]}],sepia:[{sepia:[b]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[n]}],"backdrop-brightness":[{"backdrop-brightness":[r]}],"backdrop-contrast":[{"backdrop-contrast":[a]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[p]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[y]}],"backdrop-sepia":[{"backdrop-sepia":[b]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[i]}],"border-spacing-x":[{"border-spacing-x":[i]}],"border-spacing-y":[{"border-spacing-y":[i]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",Q]}],duration:[{duration:W()}],ease:[{ease:["linear","in","out","in-out",Q]}],delay:[{delay:W()}],animate:[{animate:["none","spin","ping","pulse","bounce",Q]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[C]}],"scale-x":[{"scale-x":[C]}],"scale-y":[{"scale-y":[C]}],rotate:[{rotate:[Wo,Q]}],"translate-x":[{"translate-x":[P]}],"translate-y":[{"translate-y":[P]}],"skew-x":[{"skew-x":[k]}],"skew-y":[{"skew-y":[k]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",Q]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",Q]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",Q]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[Qt,dn,Oa]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}},uE=HC(aE);function q(...e){return uE(vg(e))}const cE=aC,Pg=f.forwardRef(({className:e,...t},n)=>u.jsx(ug,{ref:n,className:q("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));Pg.displayName=ug.displayName;const dE=Cd("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),jg=f.forwardRef(({className:e,variant:t,...n},r)=>u.jsx(cg,{ref:r,className:q(dE({variant:t}),e),...n}));jg.displayName=cg.displayName;const fE=f.forwardRef(({className:e,...t},n)=>u.jsx(pg,{ref:n,className:q("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t}));fE.displayName=pg.displayName;const Tg=f.forwardRef(({className:e,...t},n)=>u.jsx(hg,{ref:n,className:q("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:u.jsx(RC,{className:"h-4 w-4"})}));Tg.displayName=hg.displayName;const Rg=f.forwardRef(({className:e,...t},n)=>u.jsx(dg,{ref:n,className:q("text-sm font-semibold",e),...t}));Rg.displayName=dg.displayName;const _g=f.forwardRef(({className:e,...t},n)=>u.jsx(fg,{ref:n,className:q("text-sm opacity-90",e),...t}));_g.displayName=fg.displayName;function pE(){const{toasts:e}=md();return u.jsxs(cE,{children:[e.map(function({id:t,title:n,description:r,action:o,...s}){return u.jsxs(jg,{...s,children:[u.jsxs("div",{className:"grid gap-1",children:[n&&u.jsx(Rg,{children:n}),r&&u.jsx(_g,{children:r})]}),o,u.jsx(Tg,{})]},t)}),u.jsx(Pg,{})]})}var hE=hh[" useId ".trim().toString()]||(()=>{}),mE=0;function Nd(e){const[t,n]=f.useState(hE());return De(()=>{n(r=>r??String(mE++))},[e]),t?`radix-${t}`:""}const vE=["top","right","bottom","left"],Un=Math.min,ot=Math.max,pl=Math.round,hi=Math.floor,Ht=e=>({x:e,y:e}),gE={left:"right",right:"left",bottom:"top",top:"bottom"},yE={start:"end",end:"start"};function ec(e,t,n){return ot(e,Un(t,n))}function ln(e,t){return typeof e=="function"?e(t):e}function an(e){return e.split("-")[0]}function Ro(e){return e.split("-")[1]}function kd(e){return e==="x"?"y":"x"}function Pd(e){return e==="y"?"height":"width"}function Vn(e){return["top","bottom"].includes(an(e))?"y":"x"}function jd(e){return kd(Vn(e))}function xE(e,t,n){n===void 0&&(n=!1);const r=Ro(e),o=jd(e),s=Pd(o);let i=o==="x"?r===(n?"end":"start")?"right":"left":r==="start"?"bottom":"top";return t.reference[s]>t.floating[s]&&(i=hl(i)),[i,hl(i)]}function wE(e){const t=hl(e);return[tc(e),t,tc(t)]}function tc(e){return e.replace(/start|end/g,t=>yE[t])}function SE(e,t,n){const r=["left","right"],o=["right","left"],s=["top","bottom"],i=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?s:i;default:return[]}}function CE(e,t,n,r){const o=Ro(e);let s=SE(an(e),n==="start",r);return o&&(s=s.map(i=>i+"-"+o),t&&(s=s.concat(s.map(tc)))),s}function hl(e){return e.replace(/left|right|bottom|top/g,t=>gE[t])}function EE(e){return{top:0,right:0,bottom:0,left:0,...e}}function Og(e){return typeof e!="number"?EE(e):{top:e,right:e,bottom:e,left:e}}function ml(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function kp(e,t,n){let{reference:r,floating:o}=e;const s=Vn(t),i=jd(t),l=Pd(i),a=an(t),c=s==="y",p=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,g=r[l]/2-o[l]/2;let S;switch(a){case"top":S={x:p,y:r.y-o.height};break;case"bottom":S={x:p,y:r.y+r.height};break;case"right":S={x:r.x+r.width,y:d};break;case"left":S={x:r.x-o.width,y:d};break;default:S={x:r.x,y:r.y}}switch(Ro(t)){case"start":S[i]-=g*(n&&c?-1:1);break;case"end":S[i]+=g*(n&&c?-1:1);break}return S}const bE=async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:s=[],platform:i}=n,l=s.filter(Boolean),a=await(i.isRTL==null?void 0:i.isRTL(t));let c=await i.getElementRects({reference:e,floating:t,strategy:o}),{x:p,y:d}=kp(c,r,a),g=r,S={},w=0;for(let m=0;m<l.length;m++){const{name:x,fn:v}=l[m],{x:h,y,data:C,reset:b}=await v({x:p,y:d,initialPlacement:r,placement:g,strategy:o,middlewareData:S,rects:c,platform:i,elements:{reference:e,floating:t}});p=h??p,d=y??d,S={...S,[x]:{...S[x],...C}},b&&w<=50&&(w++,typeof b=="object"&&(b.placement&&(g=b.placement),b.rects&&(c=b.rects===!0?await i.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:p,y:d}=kp(c,g,a)),m=-1)}return{x:p,y:d,placement:g,strategy:o,middlewareData:S}};async function _s(e,t){var n;t===void 0&&(t={});const{x:r,y:o,platform:s,rects:i,elements:l,strategy:a}=e,{boundary:c="clippingAncestors",rootBoundary:p="viewport",elementContext:d="floating",altBoundary:g=!1,padding:S=0}=ln(t,e),w=Og(S),x=l[g?d==="floating"?"reference":"floating":d],v=ml(await s.getClippingRect({element:(n=await(s.isElement==null?void 0:s.isElement(x)))==null||n?x:x.contextElement||await(s.getDocumentElement==null?void 0:s.getDocumentElement(l.floating)),boundary:c,rootBoundary:p,strategy:a})),h=d==="floating"?{x:r,y:o,width:i.floating.width,height:i.floating.height}:i.reference,y=await(s.getOffsetParent==null?void 0:s.getOffsetParent(l.floating)),C=await(s.isElement==null?void 0:s.isElement(y))?await(s.getScale==null?void 0:s.getScale(y))||{x:1,y:1}:{x:1,y:1},b=ml(s.convertOffsetParentRelativeRectToViewportRelativeRect?await s.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:h,offsetParent:y,strategy:a}):h);return{top:(v.top-b.top+w.top)/C.y,bottom:(b.bottom-v.bottom+w.bottom)/C.y,left:(v.left-b.left+w.left)/C.x,right:(b.right-v.right+w.right)/C.x}}const NE=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:s,platform:i,elements:l,middlewareData:a}=t,{element:c,padding:p=0}=ln(e,t)||{};if(c==null)return{};const d=Og(p),g={x:n,y:r},S=jd(o),w=Pd(S),m=await i.getDimensions(c),x=S==="y",v=x?"top":"left",h=x?"bottom":"right",y=x?"clientHeight":"clientWidth",C=s.reference[w]+s.reference[S]-g[S]-s.floating[w],b=g[S]-s.reference[S],k=await(i.getOffsetParent==null?void 0:i.getOffsetParent(c));let N=k?k[y]:0;(!N||!await(i.isElement==null?void 0:i.isElement(k)))&&(N=l.floating[y]||s.floating[w]);const P=C/2-b/2,_=N/2-m[w]/2-1,O=Un(d[v],_),I=Un(d[h],_),M=O,L=N-m[w]-I,T=N/2-m[w]/2+P,U=ec(M,T,L),z=!a.arrow&&Ro(o)!=null&&T!==U&&s.reference[w]/2-(T<M?O:I)-m[w]/2<0,B=z?T<M?T-M:T-L:0;return{[S]:g[S]+B,data:{[S]:U,centerOffset:T-U-B,...z&&{alignmentOffset:B}},reset:z}}}),kE=function(e){return e===void 0&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:s,rects:i,initialPlacement:l,platform:a,elements:c}=t,{mainAxis:p=!0,crossAxis:d=!0,fallbackPlacements:g,fallbackStrategy:S="bestFit",fallbackAxisSideDirection:w="none",flipAlignment:m=!0,...x}=ln(e,t);if((n=s.arrow)!=null&&n.alignmentOffset)return{};const v=an(o),h=Vn(l),y=an(l)===l,C=await(a.isRTL==null?void 0:a.isRTL(c.floating)),b=g||(y||!m?[hl(l)]:wE(l)),k=w!=="none";!g&&k&&b.push(...CE(l,m,w,C));const N=[l,...b],P=await _s(t,x),_=[];let O=((r=s.flip)==null?void 0:r.overflows)||[];if(p&&_.push(P[v]),d){const T=xE(o,i,C);_.push(P[T[0]],P[T[1]])}if(O=[...O,{placement:o,overflows:_}],!_.every(T=>T<=0)){var I,M;const T=(((I=s.flip)==null?void 0:I.index)||0)+1,U=N[T];if(U)return{data:{index:T,overflows:O},reset:{placement:U}};let z=(M=O.filter(B=>B.overflows[0]<=0).sort((B,j)=>B.overflows[1]-j.overflows[1])[0])==null?void 0:M.placement;if(!z)switch(S){case"bestFit":{var L;const B=(L=O.filter(j=>{if(k){const A=Vn(j.placement);return A===h||A==="y"}return!0}).map(j=>[j.placement,j.overflows.filter(A=>A>0).reduce((A,$)=>A+$,0)]).sort((j,A)=>j[1]-A[1])[0])==null?void 0:L[0];B&&(z=B);break}case"initialPlacement":z=l;break}if(o!==z)return{reset:{placement:z}}}return{}}}};function Pp(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function jp(e){return vE.some(t=>e[t]>=0)}const PE=function(e){return e===void 0&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,{strategy:r="referenceHidden",...o}=ln(e,t);switch(r){case"referenceHidden":{const s=await _s(t,{...o,elementContext:"reference"}),i=Pp(s,n.reference);return{data:{referenceHiddenOffsets:i,referenceHidden:jp(i)}}}case"escaped":{const s=await _s(t,{...o,altBoundary:!0}),i=Pp(s,n.floating);return{data:{escapedOffsets:i,escaped:jp(i)}}}default:return{}}}}};async function jE(e,t){const{placement:n,platform:r,elements:o}=e,s=await(r.isRTL==null?void 0:r.isRTL(o.floating)),i=an(n),l=Ro(n),a=Vn(n)==="y",c=["left","top"].includes(i)?-1:1,p=s&&a?-1:1,d=ln(t,e);let{mainAxis:g,crossAxis:S,alignmentAxis:w}=typeof d=="number"?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return l&&typeof w=="number"&&(S=l==="end"?w*-1:w),a?{x:S*p,y:g*c}:{x:g*c,y:S*p}}const TE=function(e){return e===void 0&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:s,placement:i,middlewareData:l}=t,a=await jE(t,e);return i===((n=l.offset)==null?void 0:n.placement)&&(r=l.arrow)!=null&&r.alignmentOffset?{}:{x:o+a.x,y:s+a.y,data:{...a,placement:i}}}}},RE=function(e){return e===void 0&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,{mainAxis:s=!0,crossAxis:i=!1,limiter:l={fn:x=>{let{x:v,y:h}=x;return{x:v,y:h}}},...a}=ln(e,t),c={x:n,y:r},p=await _s(t,a),d=Vn(an(o)),g=kd(d);let S=c[g],w=c[d];if(s){const x=g==="y"?"top":"left",v=g==="y"?"bottom":"right",h=S+p[x],y=S-p[v];S=ec(h,S,y)}if(i){const x=d==="y"?"top":"left",v=d==="y"?"bottom":"right",h=w+p[x],y=w-p[v];w=ec(h,w,y)}const m=l.fn({...t,[g]:S,[d]:w});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[g]:s,[d]:i}}}}}},_E=function(e){return e===void 0&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:s,middlewareData:i}=t,{offset:l=0,mainAxis:a=!0,crossAxis:c=!0}=ln(e,t),p={x:n,y:r},d=Vn(o),g=kd(d);let S=p[g],w=p[d];const m=ln(l,t),x=typeof m=="number"?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(a){const y=g==="y"?"height":"width",C=s.reference[g]-s.floating[y]+x.mainAxis,b=s.reference[g]+s.reference[y]-x.mainAxis;S<C?S=C:S>b&&(S=b)}if(c){var v,h;const y=g==="y"?"width":"height",C=["top","left"].includes(an(o)),b=s.reference[d]-s.floating[y]+(C&&((v=i.offset)==null?void 0:v[d])||0)+(C?0:x.crossAxis),k=s.reference[d]+s.reference[y]+(C?0:((h=i.offset)==null?void 0:h[d])||0)-(C?x.crossAxis:0);w<b?w=b:w>k&&(w=k)}return{[g]:S,[d]:w}}}},OE=function(e){return e===void 0&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:s,platform:i,elements:l}=t,{apply:a=()=>{},...c}=ln(e,t),p=await _s(t,c),d=an(o),g=Ro(o),S=Vn(o)==="y",{width:w,height:m}=s.floating;let x,v;d==="top"||d==="bottom"?(x=d,v=g===(await(i.isRTL==null?void 0:i.isRTL(l.floating))?"start":"end")?"left":"right"):(v=d,x=g==="end"?"top":"bottom");const h=m-p.top-p.bottom,y=w-p.left-p.right,C=Un(m-p[x],h),b=Un(w-p[v],y),k=!t.middlewareData.shift;let N=C,P=b;if((n=t.middlewareData.shift)!=null&&n.enabled.x&&(P=y),(r=t.middlewareData.shift)!=null&&r.enabled.y&&(N=h),k&&!g){const O=ot(p.left,0),I=ot(p.right,0),M=ot(p.top,0),L=ot(p.bottom,0);S?P=w-2*(O!==0||I!==0?O+I:ot(p.left,p.right)):N=m-2*(M!==0||L!==0?M+L:ot(p.top,p.bottom))}await a({...t,availableWidth:P,availableHeight:N});const _=await i.getDimensions(l.floating);return w!==_.width||m!==_.height?{reset:{rects:!0}}:{}}}};function Ul(){return typeof window<"u"}function _o(e){return Mg(e)?(e.nodeName||"").toLowerCase():"#document"}function lt(e){var t;return(e==null||(t=e.ownerDocument)==null?void 0:t.defaultView)||window}function Kt(e){var t;return(t=(Mg(e)?e.ownerDocument:e.document)||window.document)==null?void 0:t.documentElement}function Mg(e){return Ul()?e instanceof Node||e instanceof lt(e).Node:!1}function Rt(e){return Ul()?e instanceof Element||e instanceof lt(e).Element:!1}function Wt(e){return Ul()?e instanceof HTMLElement||e instanceof lt(e).HTMLElement:!1}function Tp(e){return!Ul()||typeof ShadowRoot>"u"?!1:e instanceof ShadowRoot||e instanceof lt(e).ShadowRoot}function Hs(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=_t(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ME(e){return["table","td","th"].includes(_o(e))}function Vl(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch{return!1}})}function Td(e){const t=Rd(),n=Rt(e)?_t(e):e;return["transform","translate","scale","rotate","perspective"].some(r=>n[r]?n[r]!=="none":!1)||(n.containerType?n.containerType!=="normal":!1)||!t&&(n.backdropFilter?n.backdropFilter!=="none":!1)||!t&&(n.filter?n.filter!=="none":!1)||["transform","translate","scale","rotate","perspective","filter"].some(r=>(n.willChange||"").includes(r))||["paint","layout","strict","content"].some(r=>(n.contain||"").includes(r))}function AE(e){let t=Bn(e);for(;Wt(t)&&!Eo(t);){if(Td(t))return t;if(Vl(t))return null;t=Bn(t)}return null}function Rd(){return typeof CSS>"u"||!CSS.supports?!1:CSS.supports("-webkit-backdrop-filter","none")}function Eo(e){return["html","body","#document"].includes(_o(e))}function _t(e){return lt(e).getComputedStyle(e)}function Bl(e){return Rt(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Bn(e){if(_o(e)==="html")return e;const t=e.assignedSlot||e.parentNode||Tp(e)&&e.host||Kt(e);return Tp(t)?t.host:t}function Ag(e){const t=Bn(e);return Eo(t)?e.ownerDocument?e.ownerDocument.body:e.body:Wt(t)&&Hs(t)?t:Ag(t)}function Os(e,t,n){var r;t===void 0&&(t=[]),n===void 0&&(n=!0);const o=Ag(e),s=o===((r=e.ownerDocument)==null?void 0:r.body),i=lt(o);if(s){const l=nc(i);return t.concat(i,i.visualViewport||[],Hs(o)?o:[],l&&n?Os(l):[])}return t.concat(o,Os(o,[],n))}function nc(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function Ig(e){const t=_t(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Wt(e),s=o?e.offsetWidth:n,i=o?e.offsetHeight:r,l=pl(n)!==s||pl(r)!==i;return l&&(n=s,r=i),{width:n,height:r,$:l}}function _d(e){return Rt(e)?e:e.contextElement}function Xr(e){const t=_d(e);if(!Wt(t))return Ht(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:s}=Ig(t);let i=(s?pl(n.width):n.width)/r,l=(s?pl(n.height):n.height)/o;return(!i||!Number.isFinite(i))&&(i=1),(!l||!Number.isFinite(l))&&(l=1),{x:i,y:l}}const IE=Ht(0);function Lg(e){const t=lt(e);return!Rd()||!t.visualViewport?IE:{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}}function LE(e,t,n){return t===void 0&&(t=!1),!n||t&&n!==lt(e)?!1:t}function xr(e,t,n,r){t===void 0&&(t=!1),n===void 0&&(n=!1);const o=e.getBoundingClientRect(),s=_d(e);let i=Ht(1);t&&(r?Rt(r)&&(i=Xr(r)):i=Xr(e));const l=LE(s,n,r)?Lg(s):Ht(0);let a=(o.left+l.x)/i.x,c=(o.top+l.y)/i.y,p=o.width/i.x,d=o.height/i.y;if(s){const g=lt(s),S=r&&Rt(r)?lt(r):r;let w=g,m=nc(w);for(;m&&r&&S!==w;){const x=Xr(m),v=m.getBoundingClientRect(),h=_t(m),y=v.left+(m.clientLeft+parseFloat(h.paddingLeft))*x.x,C=v.top+(m.clientTop+parseFloat(h.paddingTop))*x.y;a*=x.x,c*=x.y,p*=x.x,d*=x.y,a+=y,c+=C,w=lt(m),m=nc(w)}}return ml({width:p,height:d,x:a,y:c})}function Od(e,t){const n=Bl(e).scrollLeft;return t?t.left+n:xr(Kt(e)).left+n}function Dg(e,t,n){n===void 0&&(n=!1);const r=e.getBoundingClientRect(),o=r.left+t.scrollLeft-(n?0:Od(e,r)),s=r.top+t.scrollTop;return{x:o,y:s}}function DE(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const s=o==="fixed",i=Kt(r),l=t?Vl(t.floating):!1;if(r===i||l&&s)return n;let a={scrollLeft:0,scrollTop:0},c=Ht(1);const p=Ht(0),d=Wt(r);if((d||!d&&!s)&&((_o(r)!=="body"||Hs(i))&&(a=Bl(r)),Wt(r))){const S=xr(r);c=Xr(r),p.x=S.x+r.clientLeft,p.y=S.y+r.clientTop}const g=i&&!d&&!s?Dg(i,a,!0):Ht(0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-a.scrollLeft*c.x+p.x+g.x,y:n.y*c.y-a.scrollTop*c.y+p.y+g.y}}function FE(e){return Array.from(e.getClientRects())}function zE(e){const t=Kt(e),n=Bl(e),r=e.ownerDocument.body,o=ot(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),s=ot(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let i=-n.scrollLeft+Od(e);const l=-n.scrollTop;return _t(r).direction==="rtl"&&(i+=ot(t.clientWidth,r.clientWidth)-o),{width:o,height:s,x:i,y:l}}function $E(e,t){const n=lt(e),r=Kt(e),o=n.visualViewport;let s=r.clientWidth,i=r.clientHeight,l=0,a=0;if(o){s=o.width,i=o.height;const c=Rd();(!c||c&&t==="fixed")&&(l=o.offsetLeft,a=o.offsetTop)}return{width:s,height:i,x:l,y:a}}function UE(e,t){const n=xr(e,!0,t==="fixed"),r=n.top+e.clientTop,o=n.left+e.clientLeft,s=Wt(e)?Xr(e):Ht(1),i=e.clientWidth*s.x,l=e.clientHeight*s.y,a=o*s.x,c=r*s.y;return{width:i,height:l,x:a,y:c}}function Rp(e,t,n){let r;if(t==="viewport")r=$E(e,n);else if(t==="document")r=zE(Kt(e));else if(Rt(t))r=UE(t,n);else{const o=Lg(e);r={x:t.x-o.x,y:t.y-o.y,width:t.width,height:t.height}}return ml(r)}function Fg(e,t){const n=Bn(e);return n===t||!Rt(n)||Eo(n)?!1:_t(n).position==="fixed"||Fg(n,t)}function VE(e,t){const n=t.get(e);if(n)return n;let r=Os(e,[],!1).filter(l=>Rt(l)&&_o(l)!=="body"),o=null;const s=_t(e).position==="fixed";let i=s?Bn(e):e;for(;Rt(i)&&!Eo(i);){const l=_t(i),a=Td(i);!a&&l.position==="fixed"&&(o=null),(s?!a&&!o:!a&&l.position==="static"&&!!o&&["absolute","fixed"].includes(o.position)||Hs(i)&&!a&&Fg(e,i))?r=r.filter(p=>p!==i):o=l,i=Bn(i)}return t.set(e,r),r}function BE(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const i=[...n==="clippingAncestors"?Vl(t)?[]:VE(t,this._c):[].concat(n),r],l=i[0],a=i.reduce((c,p)=>{const d=Rp(t,p,o);return c.top=ot(d.top,c.top),c.right=Un(d.right,c.right),c.bottom=Un(d.bottom,c.bottom),c.left=ot(d.left,c.left),c},Rp(t,l,o));return{width:a.right-a.left,height:a.bottom-a.top,x:a.left,y:a.top}}function HE(e){const{width:t,height:n}=Ig(e);return{width:t,height:n}}function WE(e,t,n){const r=Wt(t),o=Kt(t),s=n==="fixed",i=xr(e,!0,s,t);let l={scrollLeft:0,scrollTop:0};const a=Ht(0);if(r||!r&&!s)if((_o(t)!=="body"||Hs(o))&&(l=Bl(t)),r){const g=xr(t,!0,s,t);a.x=g.x+t.clientLeft,a.y=g.y+t.clientTop}else o&&(a.x=Od(o));const c=o&&!r&&!s?Dg(o,l):Ht(0),p=i.left+l.scrollLeft-a.x-c.x,d=i.top+l.scrollTop-a.y-c.y;return{x:p,y:d,width:i.width,height:i.height}}function Ma(e){return _t(e).position==="static"}function _p(e,t){if(!Wt(e)||_t(e).position==="fixed")return null;if(t)return t(e);let n=e.offsetParent;return Kt(e)===n&&(n=n.ownerDocument.body),n}function zg(e,t){const n=lt(e);if(Vl(e))return n;if(!Wt(e)){let o=Bn(e);for(;o&&!Eo(o);){if(Rt(o)&&!Ma(o))return o;o=Bn(o)}return n}let r=_p(e,t);for(;r&&ME(r)&&Ma(r);)r=_p(r,t);return r&&Eo(r)&&Ma(r)&&!Td(r)?n:r||AE(e)||n}const KE=async function(e){const t=this.getOffsetParent||zg,n=this.getDimensions,r=await n(e.floating);return{reference:WE(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}};function QE(e){return _t(e).direction==="rtl"}const GE={convertOffsetParentRelativeRectToViewportRelativeRect:DE,getDocumentElement:Kt,getClippingRect:BE,getOffsetParent:zg,getElementRects:KE,getClientRects:FE,getDimensions:HE,getScale:Xr,isElement:Rt,isRTL:QE};function $g(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function qE(e,t){let n=null,r;const o=Kt(e);function s(){var l;clearTimeout(r),(l=n)==null||l.disconnect(),n=null}function i(l,a){l===void 0&&(l=!1),a===void 0&&(a=1),s();const c=e.getBoundingClientRect(),{left:p,top:d,width:g,height:S}=c;if(l||t(),!g||!S)return;const w=hi(d),m=hi(o.clientWidth-(p+g)),x=hi(o.clientHeight-(d+S)),v=hi(p),y={rootMargin:-w+"px "+-m+"px "+-x+"px "+-v+"px",threshold:ot(0,Un(1,a))||1};let C=!0;function b(k){const N=k[0].intersectionRatio;if(N!==a){if(!C)return i();N?i(!1,N):r=setTimeout(()=>{i(!1,1e-7)},1e3)}N===1&&!$g(c,e.getBoundingClientRect())&&i(),C=!1}try{n=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch{n=new IntersectionObserver(b,y)}n.observe(e)}return i(!0),s}function YE(e,t,n,r){r===void 0&&(r={});const{ancestorScroll:o=!0,ancestorResize:s=!0,elementResize:i=typeof ResizeObserver=="function",layoutShift:l=typeof IntersectionObserver=="function",animationFrame:a=!1}=r,c=_d(e),p=o||s?[...c?Os(c):[],...Os(t)]:[];p.forEach(v=>{o&&v.addEventListener("scroll",n,{passive:!0}),s&&v.addEventListener("resize",n)});const d=c&&l?qE(c,n):null;let g=-1,S=null;i&&(S=new ResizeObserver(v=>{let[h]=v;h&&h.target===c&&S&&(S.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var y;(y=S)==null||y.observe(t)})),n()}),c&&!a&&S.observe(c),S.observe(t));let w,m=a?xr(e):null;a&&x();function x(){const v=xr(e);m&&!$g(m,v)&&n(),m=v,w=requestAnimationFrame(x)}return n(),()=>{var v;p.forEach(h=>{o&&h.removeEventListener("scroll",n),s&&h.removeEventListener("resize",n)}),d==null||d(),(v=S)==null||v.disconnect(),S=null,a&&cancelAnimationFrame(w)}}const XE=TE,ZE=RE,JE=kE,e2=OE,t2=PE,Op=NE,n2=_E,r2=(e,t,n)=>{const r=new Map,o={platform:GE,...n},s={...o.platform,_c:r};return bE(e,t,{...o,platform:s})};var Ai=typeof document<"u"?f.useLayoutEffect:f.useEffect;function vl(e,t){if(e===t)return!0;if(typeof e!=typeof t)return!1;if(typeof e=="function"&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&typeof e=="object"){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;r--!==0;)if(!vl(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;r--!==0;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;r--!==0;){const s=o[r];if(!(s==="_owner"&&e.$$typeof)&&!vl(e[s],t[s]))return!1}return!0}return e!==e&&t!==t}function Ug(e){return typeof window>"u"?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Mp(e,t){const n=Ug(e);return Math.round(t*n)/n}function Aa(e){const t=f.useRef(e);return Ai(()=>{t.current=e}),t}function o2(e){e===void 0&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:o,elements:{reference:s,floating:i}={},transform:l=!0,whileElementsMounted:a,open:c}=e,[p,d]=f.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[g,S]=f.useState(r);vl(g,r)||S(r);const[w,m]=f.useState(null),[x,v]=f.useState(null),h=f.useCallback(j=>{j!==k.current&&(k.current=j,m(j))},[]),y=f.useCallback(j=>{j!==N.current&&(N.current=j,v(j))},[]),C=s||w,b=i||x,k=f.useRef(null),N=f.useRef(null),P=f.useRef(p),_=a!=null,O=Aa(a),I=Aa(o),M=Aa(c),L=f.useCallback(()=>{if(!k.current||!N.current)return;const j={placement:t,strategy:n,middleware:g};I.current&&(j.platform=I.current),r2(k.current,N.current,j).then(A=>{const $={...A,isPositioned:M.current!==!1};T.current&&!vl(P.current,$)&&(P.current=$,Nr.flushSync(()=>{d($)}))})},[g,t,n,I,M]);Ai(()=>{c===!1&&P.current.isPositioned&&(P.current.isPositioned=!1,d(j=>({...j,isPositioned:!1})))},[c]);const T=f.useRef(!1);Ai(()=>(T.current=!0,()=>{T.current=!1}),[]),Ai(()=>{if(C&&(k.current=C),b&&(N.current=b),C&&b){if(O.current)return O.current(C,b,L);L()}},[C,b,L,O,_]);const U=f.useMemo(()=>({reference:k,floating:N,setReference:h,setFloating:y}),[h,y]),z=f.useMemo(()=>({reference:C,floating:b}),[C,b]),B=f.useMemo(()=>{const j={position:n,left:0,top:0};if(!z.floating)return j;const A=Mp(z.floating,p.x),$=Mp(z.floating,p.y);return l?{...j,transform:"translate("+A+"px, "+$+"px)",...Ug(z.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:A,top:$}},[n,l,z.floating,p.x,p.y]);return f.useMemo(()=>({...p,update:L,refs:U,elements:z,floatingStyles:B}),[p,L,U,z,B])}const s2=e=>{function t(n){return{}.hasOwnProperty.call(n,"current")}return{name:"arrow",options:e,fn(n){const{element:r,padding:o}=typeof e=="function"?e(n):e;return r&&t(r)?r.current!=null?Op({element:r.current,padding:o}).fn(n):{}:r?Op({element:r,padding:o}).fn(n):{}}}},i2=(e,t)=>({...XE(e),options:[e,t]}),l2=(e,t)=>({...ZE(e),options:[e,t]}),a2=(e,t)=>({...n2(e),options:[e,t]}),u2=(e,t)=>({...JE(e),options:[e,t]}),c2=(e,t)=>({...e2(e),options:[e,t]}),d2=(e,t)=>({...t2(e),options:[e,t]}),f2=(e,t)=>({...s2(e),options:[e,t]});var p2="Arrow",Vg=f.forwardRef((e,t)=>{const{children:n,width:r=10,height:o=5,...s}=e;return u.jsx(ee.svg,{...s,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:u.jsx("polygon",{points:"0,0 30,0 15,10"})})});Vg.displayName=p2;var h2=Vg;function Bg(e){const[t,n]=f.useState(void 0);return De(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const r=new ResizeObserver(o=>{if(!Array.isArray(o)||!o.length)return;const s=o[0];let i,l;if("borderBoxSize"in s){const a=s.borderBoxSize,c=Array.isArray(a)?a[0]:a;i=c.inlineSize,l=c.blockSize}else i=e.offsetWidth,l=e.offsetHeight;n({width:i,height:l})});return r.observe(e,{box:"border-box"}),()=>r.unobserve(e)}else n(void 0)},[e]),t}var Md="Popper",[Hg,Hl]=kr(Md),[m2,Wg]=Hg(Md),Kg=e=>{const{__scopePopper:t,children:n}=e,[r,o]=f.useState(null);return u.jsx(m2,{scope:t,anchor:r,onAnchorChange:o,children:n})};Kg.displayName=Md;var Qg="PopperAnchor",Gg=f.forwardRef((e,t)=>{const{__scopePopper:n,virtualRef:r,...o}=e,s=Wg(Qg,n),i=f.useRef(null),l=ie(t,i);return f.useEffect(()=>{s.onAnchorChange((r==null?void 0:r.current)||i.current)}),r?null:u.jsx(ee.div,{...o,ref:l})});Gg.displayName=Qg;var Ad="PopperContent",[v2,g2]=Hg(Ad),qg=f.forwardRef((e,t)=>{var H,ue,_e,le,oe,se;const{__scopePopper:n,side:r="bottom",sideOffset:o=0,align:s="center",alignOffset:i=0,arrowPadding:l=0,avoidCollisions:a=!0,collisionBoundary:c=[],collisionPadding:p=0,sticky:d="partial",hideWhenDetached:g=!1,updatePositionStrategy:S="optimized",onPlaced:w,...m}=e,x=Wg(Ad,n),[v,h]=f.useState(null),y=ie(t,tt=>h(tt)),[C,b]=f.useState(null),k=Bg(C),N=(k==null?void 0:k.width)??0,P=(k==null?void 0:k.height)??0,_=r+(s!=="center"?"-"+s:""),O=typeof p=="number"?p:{top:0,right:0,bottom:0,left:0,...p},I=Array.isArray(c)?c:[c],M=I.length>0,L={padding:O,boundary:I.filter(x2),altBoundary:M},{refs:T,floatingStyles:U,placement:z,isPositioned:B,middlewareData:j}=o2({strategy:"fixed",placement:_,whileElementsMounted:(...tt)=>YE(...tt,{animationFrame:S==="always"}),elements:{reference:x.anchor},middleware:[i2({mainAxis:o+P,alignmentAxis:i}),a&&l2({mainAxis:!0,crossAxis:!1,limiter:d==="partial"?a2():void 0,...L}),a&&u2({...L}),c2({...L,apply:({elements:tt,rects:Mt,availableWidth:Ao,availableHeight:Io})=>{const{width:Lo,height:A0}=Mt.reference,Ks=tt.floating.style;Ks.setProperty("--radix-popper-available-width",`${Ao}px`),Ks.setProperty("--radix-popper-available-height",`${Io}px`),Ks.setProperty("--radix-popper-anchor-width",`${Lo}px`),Ks.setProperty("--radix-popper-anchor-height",`${A0}px`)}}),C&&f2({element:C,padding:l}),w2({arrowWidth:N,arrowHeight:P}),g&&d2({strategy:"referenceHidden",...L})]}),[A,$]=Zg(z),W=ut(w);De(()=>{B&&(W==null||W())},[B,W]);const re=(H=j.arrow)==null?void 0:H.x,Fe=(ue=j.arrow)==null?void 0:ue.y,Ce=((_e=j.arrow)==null?void 0:_e.centerOffset)!==0,[Ot,ze]=f.useState();return De(()=>{v&&ze(window.getComputedStyle(v).zIndex)},[v]),u.jsx("div",{ref:T.setFloating,"data-radix-popper-content-wrapper":"",style:{...U,transform:B?U.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:Ot,"--radix-popper-transform-origin":[(le=j.transformOrigin)==null?void 0:le.x,(oe=j.transformOrigin)==null?void 0:oe.y].join(" "),...((se=j.hide)==null?void 0:se.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:u.jsx(v2,{scope:n,placedSide:A,onArrowChange:b,arrowX:re,arrowY:Fe,shouldHideArrow:Ce,children:u.jsx(ee.div,{"data-side":A,"data-align":$,...m,ref:y,style:{...m.style,animation:B?void 0:"none"}})})})});qg.displayName=Ad;var Yg="PopperArrow",y2={top:"bottom",right:"left",bottom:"top",left:"right"},Xg=f.forwardRef(function(t,n){const{__scopePopper:r,...o}=t,s=g2(Yg,r),i=y2[s.placedSide];return u.jsx("span",{ref:s.onArrowChange,style:{position:"absolute",left:s.arrowX,top:s.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[s.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[s.placedSide],visibility:s.shouldHideArrow?"hidden":void 0},children:u.jsx(h2,{...o,ref:n,style:{...o.style,display:"block"}})})});Xg.displayName=Yg;function x2(e){return e!==null}var w2=e=>({name:"transformOrigin",options:e,fn(t){var x,v,h;const{placement:n,rects:r,middlewareData:o}=t,i=((x=o.arrow)==null?void 0:x.centerOffset)!==0,l=i?0:e.arrowWidth,a=i?0:e.arrowHeight,[c,p]=Zg(n),d={start:"0%",center:"50%",end:"100%"}[p],g=(((v=o.arrow)==null?void 0:v.x)??0)+l/2,S=(((h=o.arrow)==null?void 0:h.y)??0)+a/2;let w="",m="";return c==="bottom"?(w=i?d:`${g}px`,m=`${-a}px`):c==="top"?(w=i?d:`${g}px`,m=`${r.floating.height+a}px`):c==="right"?(w=`${-a}px`,m=i?d:`${S}px`):c==="left"&&(w=`${r.floating.width+a}px`,m=i?d:`${S}px`),{data:{x:w,y:m}}}});function Zg(e){const[t,n="center"]=e.split("-");return[t,n]}var S2=Kg,Jg=Gg,ey=qg,ty=Xg,[Wl,tk]=kr("Tooltip",[Hl]),Id=Hl(),ny="TooltipProvider",C2=700,Ap="tooltip.open",[E2,ry]=Wl(ny),oy=e=>{const{__scopeTooltip:t,delayDuration:n=C2,skipDelayDuration:r=300,disableHoverableContent:o=!1,children:s}=e,i=f.useRef(!0),l=f.useRef(!1),a=f.useRef(0);return f.useEffect(()=>{const c=a.current;return()=>window.clearTimeout(c)},[]),u.jsx(E2,{scope:t,isOpenDelayedRef:i,delayDuration:n,onOpen:f.useCallback(()=>{window.clearTimeout(a.current),i.current=!1},[]),onClose:f.useCallback(()=>{window.clearTimeout(a.current),a.current=window.setTimeout(()=>i.current=!0,r)},[r]),isPointerInTransitRef:l,onPointerInTransitChange:f.useCallback(c=>{l.current=c},[]),disableHoverableContent:o,children:s})};oy.displayName=ny;var sy="Tooltip",[nk,Kl]=Wl(sy),rc="TooltipTrigger",b2=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Kl(rc,n),s=ry(rc,n),i=Id(n),l=f.useRef(null),a=ie(t,l,o.onTriggerChange),c=f.useRef(!1),p=f.useRef(!1),d=f.useCallback(()=>c.current=!1,[]);return f.useEffect(()=>()=>document.removeEventListener("pointerup",d),[d]),u.jsx(Jg,{asChild:!0,...i,children:u.jsx(ee.button,{"aria-describedby":o.open?o.contentId:void 0,"data-state":o.stateAttribute,...r,ref:a,onPointerMove:K(e.onPointerMove,g=>{g.pointerType!=="touch"&&!p.current&&!s.isPointerInTransitRef.current&&(o.onTriggerEnter(),p.current=!0)}),onPointerLeave:K(e.onPointerLeave,()=>{o.onTriggerLeave(),p.current=!1}),onPointerDown:K(e.onPointerDown,()=>{o.open&&o.onClose(),c.current=!0,document.addEventListener("pointerup",d,{once:!0})}),onFocus:K(e.onFocus,()=>{c.current||o.onOpen()}),onBlur:K(e.onBlur,o.onClose),onClick:K(e.onClick,o.onClose)})})});b2.displayName=rc;var N2="TooltipPortal",[rk,k2]=Wl(N2,{forceMount:void 0}),bo="TooltipContent",iy=f.forwardRef((e,t)=>{const n=k2(bo,e.__scopeTooltip),{forceMount:r=n.forceMount,side:o="top",...s}=e,i=Kl(bo,e.__scopeTooltip);return u.jsx(yd,{present:r||i.open,children:i.disableHoverableContent?u.jsx(ly,{side:o,...s,ref:t}):u.jsx(P2,{side:o,...s,ref:t})})}),P2=f.forwardRef((e,t)=>{const n=Kl(bo,e.__scopeTooltip),r=ry(bo,e.__scopeTooltip),o=f.useRef(null),s=ie(t,o),[i,l]=f.useState(null),{trigger:a,onClose:c}=n,p=o.current,{onPointerInTransitChange:d}=r,g=f.useCallback(()=>{l(null),d(!1)},[d]),S=f.useCallback((w,m)=>{const x=w.currentTarget,v={x:w.clientX,y:w.clientY},h=O2(v,x.getBoundingClientRect()),y=M2(v,h),C=A2(m.getBoundingClientRect()),b=L2([...y,...C]);l(b),d(!0)},[d]);return f.useEffect(()=>()=>g(),[g]),f.useEffect(()=>{if(a&&p){const w=x=>S(x,p),m=x=>S(x,a);return a.addEventListener("pointerleave",w),p.addEventListener("pointerleave",m),()=>{a.removeEventListener("pointerleave",w),p.removeEventListener("pointerleave",m)}}},[a,p,S,g]),f.useEffect(()=>{if(i){const w=m=>{const x=m.target,v={x:m.clientX,y:m.clientY},h=(a==null?void 0:a.contains(x))||(p==null?void 0:p.contains(x)),y=!I2(v,i);h?g():y&&(g(),c())};return document.addEventListener("pointermove",w),()=>document.removeEventListener("pointermove",w)}},[a,p,i,c,g]),u.jsx(ly,{...e,ref:s})}),[j2,T2]=Wl(sy,{isInside:!1}),R2=bS("TooltipContent"),ly=f.forwardRef((e,t)=>{const{__scopeTooltip:n,children:r,"aria-label":o,onEscapeKeyDown:s,onPointerDownOutside:i,...l}=e,a=Kl(bo,n),c=Id(n),{onClose:p}=a;return f.useEffect(()=>(document.addEventListener(Ap,p),()=>document.removeEventListener(Ap,p)),[p]),f.useEffect(()=>{if(a.trigger){const d=g=>{const S=g.target;S!=null&&S.contains(a.trigger)&&p()};return window.addEventListener("scroll",d,{capture:!0}),()=>window.removeEventListener("scroll",d,{capture:!0})}},[a.trigger,p]),u.jsx(Dl,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:d=>d.preventDefault(),onDismiss:p,children:u.jsxs(ey,{"data-state":a.stateAttribute,...c,...l,ref:t,style:{...l.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[u.jsx(R2,{children:r}),u.jsx(j2,{scope:n,isInside:!0,children:u.jsx(HS,{id:a.contentId,role:"tooltip",children:o||r})})]})})});iy.displayName=bo;var ay="TooltipArrow",_2=f.forwardRef((e,t)=>{const{__scopeTooltip:n,...r}=e,o=Id(n);return T2(ay,n).isInside?null:u.jsx(ty,{...o,...r,ref:t})});_2.displayName=ay;function O2(e,t){const n=Math.abs(t.top-e.y),r=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),s=Math.abs(t.left-e.x);switch(Math.min(n,r,o,s)){case s:return"left";case o:return"right";case n:return"top";case r:return"bottom";default:throw new Error("unreachable")}}function M2(e,t,n=5){const r=[];switch(t){case"top":r.push({x:e.x-n,y:e.y+n},{x:e.x+n,y:e.y+n});break;case"bottom":r.push({x:e.x-n,y:e.y-n},{x:e.x+n,y:e.y-n});break;case"left":r.push({x:e.x+n,y:e.y-n},{x:e.x+n,y:e.y+n});break;case"right":r.push({x:e.x-n,y:e.y-n},{x:e.x-n,y:e.y+n});break}return r}function A2(e){const{top:t,right:n,bottom:r,left:o}=e;return[{x:o,y:t},{x:n,y:t},{x:n,y:r},{x:o,y:r}]}function I2(e,t){const{x:n,y:r}=e;let o=!1;for(let s=0,i=t.length-1;s<t.length;i=s++){const l=t[s].x,a=t[s].y,c=t[i].x,p=t[i].y;a>r!=p>r&&n<(c-l)*(r-a)/(p-a)+l&&(o=!o)}return o}function L2(e){const t=e.slice();return t.sort((n,r)=>n.x<r.x?-1:n.x>r.x?1:n.y<r.y?-1:n.y>r.y?1:0),D2(t)}function D2(e){if(e.length<=1)return e.slice();const t=[];for(let r=0;r<e.length;r++){const o=e[r];for(;t.length>=2;){const s=t[t.length-1],i=t[t.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))t.pop();else break}t.push(o)}t.pop();const n=[];for(let r=e.length-1;r>=0;r--){const o=e[r];for(;n.length>=2;){const s=n[n.length-1],i=n[n.length-2];if((s.x-i.x)*(o.y-i.y)>=(s.y-i.y)*(o.x-i.x))n.pop();else break}n.push(o)}return n.pop(),t.length===1&&n.length===1&&t[0].x===n[0].x&&t[0].y===n[0].y?t:t.concat(n)}var F2=oy,uy=iy;const z2=F2,$2=f.forwardRef(({className:e,sideOffset:t=4,...n},r)=>u.jsx(uy,{ref:r,sideOffset:t,className:q("z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-tooltip-content-transform-origin]",e),...n}));$2.displayName=uy.displayName;const Be=f.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:q("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));Be.displayName="Card";const wr=f.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:q("flex flex-col space-y-1.5 p-6",e),...t}));wr.displayName="CardHeader";const Sr=f.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:q("text-2xl font-semibold leading-none tracking-tight",e),...t}));Sr.displayName="CardTitle";const U2=f.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:q("text-sm text-muted-foreground",e),...t}));U2.displayName="CardDescription";const Ye=f.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:q("p-6 pt-0",e),...t}));Ye.displayName="CardContent";const V2=f.forwardRef(({className:e,...t},n)=>u.jsx("div",{ref:n,className:q("flex items-center p-6 pt-0",e),...t}));V2.displayName="CardFooter";const B2=Cd("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),we=f.forwardRef(({className:e,variant:t,size:n,asChild:r=!1,...o},s)=>{const i=r?CS:"button";return u.jsx(i,{className:q(B2({variant:t,size:n,className:e})),ref:s,...o})});we.displayName="Button";const H2=Cd("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Hn({className:e,variant:t,...n}){return u.jsx("div",{className:q(H2({variant:t}),e),...n})}function gl(e,[t,n]){return Math.min(n,Math.max(t,e))}var W2=f.createContext(void 0);function cy(e){const t=f.useContext(W2);return e||t||"ltr"}var Ia=0;function K2(){f.useEffect(()=>{const e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??Ip()),document.body.insertAdjacentElement("beforeend",e[1]??Ip()),Ia++,()=>{Ia===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(t=>t.remove()),Ia--}},[])}function Ip(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var La="focusScope.autoFocusOnMount",Da="focusScope.autoFocusOnUnmount",Lp={bubbles:!1,cancelable:!0},Q2="FocusScope",dy=f.forwardRef((e,t)=>{const{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:s,...i}=e,[l,a]=f.useState(null),c=ut(o),p=ut(s),d=f.useRef(null),g=ie(t,m=>a(m)),S=f.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;f.useEffect(()=>{if(r){let m=function(y){if(S.paused||!l)return;const C=y.target;l.contains(C)?d.current=C:mn(d.current,{select:!0})},x=function(y){if(S.paused||!l)return;const C=y.relatedTarget;C!==null&&(l.contains(C)||mn(d.current,{select:!0}))},v=function(y){if(document.activeElement===document.body)for(const b of y)b.removedNodes.length>0&&mn(l)};document.addEventListener("focusin",m),document.addEventListener("focusout",x);const h=new MutationObserver(v);return l&&h.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",m),document.removeEventListener("focusout",x),h.disconnect()}}},[r,l,S.paused]),f.useEffect(()=>{if(l){Fp.add(S);const m=document.activeElement;if(!l.contains(m)){const v=new CustomEvent(La,Lp);l.addEventListener(La,c),l.dispatchEvent(v),v.defaultPrevented||(G2(J2(fy(l)),{select:!0}),document.activeElement===m&&mn(l))}return()=>{l.removeEventListener(La,c),setTimeout(()=>{const v=new CustomEvent(Da,Lp);l.addEventListener(Da,p),l.dispatchEvent(v),v.defaultPrevented||mn(m??document.body,{select:!0}),l.removeEventListener(Da,p),Fp.remove(S)},0)}}},[l,c,p,S]);const w=f.useCallback(m=>{if(!n&&!r||S.paused)return;const x=m.key==="Tab"&&!m.altKey&&!m.ctrlKey&&!m.metaKey,v=document.activeElement;if(x&&v){const h=m.currentTarget,[y,C]=q2(h);y&&C?!m.shiftKey&&v===C?(m.preventDefault(),n&&mn(y,{select:!0})):m.shiftKey&&v===y&&(m.preventDefault(),n&&mn(C,{select:!0})):v===h&&m.preventDefault()}},[n,r,S.paused]);return u.jsx(ee.div,{tabIndex:-1,...i,ref:g,onKeyDown:w})});dy.displayName=Q2;function G2(e,{select:t=!1}={}){const n=document.activeElement;for(const r of e)if(mn(r,{select:t}),document.activeElement!==n)return}function q2(e){const t=fy(e),n=Dp(t,e),r=Dp(t.reverse(),e);return[n,r]}function fy(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:r=>{const o=r.tagName==="INPUT"&&r.type==="hidden";return r.disabled||r.hidden||o?NodeFilter.FILTER_SKIP:r.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Dp(e,t){for(const n of e)if(!Y2(n,{upTo:t}))return n}function Y2(e,{upTo:t}){if(getComputedStyle(e).visibility==="hidden")return!0;for(;e;){if(t!==void 0&&e===t)return!1;if(getComputedStyle(e).display==="none")return!0;e=e.parentElement}return!1}function X2(e){return e instanceof HTMLInputElement&&"select"in e}function mn(e,{select:t=!1}={}){if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&X2(e)&&t&&e.select()}}var Fp=Z2();function Z2(){let e=[];return{add(t){const n=e[0];t!==n&&(n==null||n.pause()),e=zp(e,t),e.unshift(t)},remove(t){var n;e=zp(e,t),(n=e[0])==null||n.resume()}}}function zp(e,t){const n=[...e],r=n.indexOf(t);return r!==-1&&n.splice(r,1),n}function J2(e){return e.filter(t=>t.tagName!=="A")}function py(e){const t=f.useRef({value:e,previous:e});return f.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}var eb=function(e){if(typeof document>"u")return null;var t=Array.isArray(e)?e[0]:e;return t.ownerDocument.body},jr=new WeakMap,mi=new WeakMap,vi={},Fa=0,hy=function(e){return e&&(e.host||hy(e.parentNode))},tb=function(e,t){return t.map(function(n){if(e.contains(n))return n;var r=hy(n);return r&&e.contains(r)?r:(console.error("aria-hidden",n,"in not contained inside",e,". Doing nothing"),null)}).filter(function(n){return!!n})},nb=function(e,t,n,r){var o=tb(t,Array.isArray(e)?e:[e]);vi[n]||(vi[n]=new WeakMap);var s=vi[n],i=[],l=new Set,a=new Set(o),c=function(d){!d||l.has(d)||(l.add(d),c(d.parentNode))};o.forEach(c);var p=function(d){!d||a.has(d)||Array.prototype.forEach.call(d.children,function(g){if(l.has(g))p(g);else try{var S=g.getAttribute(r),w=S!==null&&S!=="false",m=(jr.get(g)||0)+1,x=(s.get(g)||0)+1;jr.set(g,m),s.set(g,x),i.push(g),m===1&&w&&mi.set(g,!0),x===1&&g.setAttribute(n,"true"),w||g.setAttribute(r,"true")}catch(v){console.error("aria-hidden: cannot operate on ",g,v)}})};return p(t),l.clear(),Fa++,function(){i.forEach(function(d){var g=jr.get(d)-1,S=s.get(d)-1;jr.set(d,g),s.set(d,S),g||(mi.has(d)||d.removeAttribute(r),mi.delete(d)),S||d.removeAttribute(n)}),Fa--,Fa||(jr=new WeakMap,jr=new WeakMap,mi=new WeakMap,vi={})}},rb=function(e,t,n){n===void 0&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=eb(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),nb(r,o,n,"aria-hidden")):function(){return null}},Ut=function(){return Ut=Object.assign||function(t){for(var n,r=1,o=arguments.length;r<o;r++){n=arguments[r];for(var s in n)Object.prototype.hasOwnProperty.call(n,s)&&(t[s]=n[s])}return t},Ut.apply(this,arguments)};function my(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}function ob(e,t,n){if(n||arguments.length===2)for(var r=0,o=t.length,s;r<o;r++)(s||!(r in t))&&(s||(s=Array.prototype.slice.call(t,0,r)),s[r]=t[r]);return e.concat(s||Array.prototype.slice.call(t))}var Ii="right-scroll-bar-position",Li="width-before-scroll-bar",sb="with-scroll-bars-hidden",ib="--removed-body-scroll-bar-size";function za(e,t){return typeof e=="function"?e(t):e&&(e.current=t),e}function lb(e,t){var n=f.useState(function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(r){var o=n.value;o!==r&&(n.value=r,n.callback(r,o))}}}})[0];return n.callback=t,n.facade}var ab=typeof window<"u"?f.useLayoutEffect:f.useEffect,$p=new WeakMap;function ub(e,t){var n=lb(null,function(r){return e.forEach(function(o){return za(o,r)})});return ab(function(){var r=$p.get(n);if(r){var o=new Set(r),s=new Set(e),i=n.current;o.forEach(function(l){s.has(l)||za(l,null)}),s.forEach(function(l){o.has(l)||za(l,i)})}$p.set(n,e)},[e]),n}function cb(e){return e}function db(e,t){t===void 0&&(t=cb);var n=[],r=!1,o={read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(s){var i=t(s,r);return n.push(i),function(){n=n.filter(function(l){return l!==i})}},assignSyncMedium:function(s){for(r=!0;n.length;){var i=n;n=[],i.forEach(s)}n={push:function(l){return s(l)},filter:function(){return n}}},assignMedium:function(s){r=!0;var i=[];if(n.length){var l=n;n=[],l.forEach(s),i=n}var a=function(){var p=i;i=[],p.forEach(s)},c=function(){return Promise.resolve().then(a)};c(),n={push:function(p){i.push(p),c()},filter:function(p){return i=i.filter(p),n}}}};return o}function fb(e){e===void 0&&(e={});var t=db(null);return t.options=Ut({async:!0,ssr:!1},e),t}var vy=function(e){var t=e.sideCar,n=my(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw new Error("Sidecar medium not found");return f.createElement(r,Ut({},n))};vy.isSideCarExport=!0;function pb(e,t){return e.useMedium(t),vy}var gy=fb(),$a=function(){},Ql=f.forwardRef(function(e,t){var n=f.useRef(null),r=f.useState({onScrollCapture:$a,onWheelCapture:$a,onTouchMoveCapture:$a}),o=r[0],s=r[1],i=e.forwardProps,l=e.children,a=e.className,c=e.removeScrollBar,p=e.enabled,d=e.shards,g=e.sideCar,S=e.noIsolation,w=e.inert,m=e.allowPinchZoom,x=e.as,v=x===void 0?"div":x,h=e.gapMode,y=my(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),C=g,b=ub([n,t]),k=Ut(Ut({},y),o);return f.createElement(f.Fragment,null,p&&f.createElement(C,{sideCar:gy,removeScrollBar:c,shards:d,noIsolation:S,inert:w,setCallbacks:s,allowPinchZoom:!!m,lockRef:n,gapMode:h}),i?f.cloneElement(f.Children.only(l),Ut(Ut({},k),{ref:b})):f.createElement(v,Ut({},k,{className:a,ref:b}),l))});Ql.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};Ql.classNames={fullWidth:Li,zeroRight:Ii};var hb=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function mb(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=hb();return t&&e.setAttribute("nonce",t),e}function vb(e,t){e.styleSheet?e.styleSheet.cssText=t:e.appendChild(document.createTextNode(t))}function gb(e){var t=document.head||document.getElementsByTagName("head")[0];t.appendChild(e)}var yb=function(){var e=0,t=null;return{add:function(n){e==0&&(t=mb())&&(vb(t,n),gb(t)),e++},remove:function(){e--,!e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},xb=function(){var e=yb();return function(t,n){f.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},yy=function(){var e=xb(),t=function(n){var r=n.styles,o=n.dynamic;return e(r,o),null};return t},wb={left:0,top:0,right:0,gap:0},Ua=function(e){return parseInt(e||"",10)||0},Sb=function(e){var t=window.getComputedStyle(document.body),n=t[e==="padding"?"paddingLeft":"marginLeft"],r=t[e==="padding"?"paddingTop":"marginTop"],o=t[e==="padding"?"paddingRight":"marginRight"];return[Ua(n),Ua(r),Ua(o)]},Cb=function(e){if(e===void 0&&(e="margin"),typeof window>"u")return wb;var t=Sb(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},Eb=yy(),Zr="data-scroll-locked",bb=function(e,t,n,r){var o=e.left,s=e.top,i=e.right,l=e.gap;return n===void 0&&(n="margin"),`
  .`.concat(sb,` {
   overflow: hidden `).concat(r,`;
   padding-right: `).concat(l,"px ").concat(r,`;
  }
  body[`).concat(Zr,`] {
    overflow: hidden `).concat(r,`;
    overscroll-behavior: contain;
    `).concat([t&&"position: relative ".concat(r,";"),n==="margin"&&`
    padding-left: `.concat(o,`px;
    padding-top: `).concat(s,`px;
    padding-right: `).concat(i,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(l,"px ").concat(r,`;
    `),n==="padding"&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ii,` {
    right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Li,` {
    margin-right: `).concat(l,"px ").concat(r,`;
  }
  
  .`).concat(Ii," .").concat(Ii,` {
    right: 0 `).concat(r,`;
  }
  
  .`).concat(Li," .").concat(Li,` {
    margin-right: 0 `).concat(r,`;
  }
  
  body[`).concat(Zr,`] {
    `).concat(ib,": ").concat(l,`px;
  }
`)},Up=function(){var e=parseInt(document.body.getAttribute(Zr)||"0",10);return isFinite(e)?e:0},Nb=function(){f.useEffect(function(){return document.body.setAttribute(Zr,(Up()+1).toString()),function(){var e=Up()-1;e<=0?document.body.removeAttribute(Zr):document.body.setAttribute(Zr,e.toString())}},[])},kb=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=r===void 0?"margin":r;Nb();var s=f.useMemo(function(){return Cb(o)},[o]);return f.createElement(Eb,{styles:bb(s,!t,o,n?"":"!important")})},oc=!1;if(typeof window<"u")try{var gi=Object.defineProperty({},"passive",{get:function(){return oc=!0,!0}});window.addEventListener("test",gi,gi),window.removeEventListener("test",gi,gi)}catch{oc=!1}var Tr=oc?{passive:!1}:!1,Pb=function(e){return e.tagName==="TEXTAREA"},xy=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return n[t]!=="hidden"&&!(n.overflowY===n.overflowX&&!Pb(e)&&n[t]==="visible")},jb=function(e){return xy(e,"overflowY")},Tb=function(e){return xy(e,"overflowX")},Vp=function(e,t){var n=t.ownerDocument,r=t;do{typeof ShadowRoot<"u"&&r instanceof ShadowRoot&&(r=r.host);var o=wy(e,r);if(o){var s=Sy(e,r),i=s[1],l=s[2];if(i>l)return!0}r=r.parentNode}while(r&&r!==n.body);return!1},Rb=function(e){var t=e.scrollTop,n=e.scrollHeight,r=e.clientHeight;return[t,n,r]},_b=function(e){var t=e.scrollLeft,n=e.scrollWidth,r=e.clientWidth;return[t,n,r]},wy=function(e,t){return e==="v"?jb(t):Tb(t)},Sy=function(e,t){return e==="v"?Rb(t):_b(t)},Ob=function(e,t){return e==="h"&&t==="rtl"?-1:1},Mb=function(e,t,n,r,o){var s=Ob(e,window.getComputedStyle(t).direction),i=s*r,l=n.target,a=t.contains(l),c=!1,p=i>0,d=0,g=0;do{var S=Sy(e,l),w=S[0],m=S[1],x=S[2],v=m-x-s*w;(w||v)&&wy(e,l)&&(d+=v,g+=w),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!a&&l!==document.body||a&&(t.contains(l)||t===l));return(p&&(Math.abs(d)<1||!o)||!p&&(Math.abs(g)<1||!o))&&(c=!0),c},yi=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Bp=function(e){return[e.deltaX,e.deltaY]},Hp=function(e){return e&&"current"in e?e.current:e},Ab=function(e,t){return e[0]===t[0]&&e[1]===t[1]},Ib=function(e){return`
  .block-interactivity-`.concat(e,` {pointer-events: none;}
  .allow-interactivity-`).concat(e,` {pointer-events: all;}
`)},Lb=0,Rr=[];function Db(e){var t=f.useRef([]),n=f.useRef([0,0]),r=f.useRef(),o=f.useState(Lb++)[0],s=f.useState(yy)[0],i=f.useRef(e);f.useEffect(function(){i.current=e},[e]),f.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var m=ob([e.lockRef.current],(e.shards||[]).map(Hp),!0).filter(Boolean);return m.forEach(function(x){return x.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),m.forEach(function(x){return x.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=f.useCallback(function(m,x){if("touches"in m&&m.touches.length===2||m.type==="wheel"&&m.ctrlKey)return!i.current.allowPinchZoom;var v=yi(m),h=n.current,y="deltaX"in m?m.deltaX:h[0]-v[0],C="deltaY"in m?m.deltaY:h[1]-v[1],b,k=m.target,N=Math.abs(y)>Math.abs(C)?"h":"v";if("touches"in m&&N==="h"&&k.type==="range")return!1;var P=Vp(N,k);if(!P)return!0;if(P?b=N:(b=N==="v"?"h":"v",P=Vp(N,k)),!P)return!1;if(!r.current&&"changedTouches"in m&&(y||C)&&(r.current=b),!b)return!0;var _=r.current||b;return Mb(_,x,m,_==="h"?y:C,!0)},[]),a=f.useCallback(function(m){var x=m;if(!(!Rr.length||Rr[Rr.length-1]!==s)){var v="deltaY"in x?Bp(x):yi(x),h=t.current.filter(function(b){return b.name===x.type&&(b.target===x.target||x.target===b.shadowParent)&&Ab(b.delta,v)})[0];if(h&&h.should){x.cancelable&&x.preventDefault();return}if(!h){var y=(i.current.shards||[]).map(Hp).filter(Boolean).filter(function(b){return b.contains(x.target)}),C=y.length>0?l(x,y[0]):!i.current.noIsolation;C&&x.cancelable&&x.preventDefault()}}},[]),c=f.useCallback(function(m,x,v,h){var y={name:m,delta:x,target:v,should:h,shadowParent:Fb(v)};t.current.push(y),setTimeout(function(){t.current=t.current.filter(function(C){return C!==y})},1)},[]),p=f.useCallback(function(m){n.current=yi(m),r.current=void 0},[]),d=f.useCallback(function(m){c(m.type,Bp(m),m.target,l(m,e.lockRef.current))},[]),g=f.useCallback(function(m){c(m.type,yi(m),m.target,l(m,e.lockRef.current))},[]);f.useEffect(function(){return Rr.push(s),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:g}),document.addEventListener("wheel",a,Tr),document.addEventListener("touchmove",a,Tr),document.addEventListener("touchstart",p,Tr),function(){Rr=Rr.filter(function(m){return m!==s}),document.removeEventListener("wheel",a,Tr),document.removeEventListener("touchmove",a,Tr),document.removeEventListener("touchstart",p,Tr)}},[]);var S=e.removeScrollBar,w=e.inert;return f.createElement(f.Fragment,null,w?f.createElement(s,{styles:Ib(o)}):null,S?f.createElement(kb,{gapMode:e.gapMode}):null)}function Fb(e){for(var t=null;e!==null;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const zb=pb(gy,Db);var Cy=f.forwardRef(function(e,t){return f.createElement(Ql,Ut({},e,{ref:t,sideCar:zb}))});Cy.classNames=Ql.classNames;var $b=[" ","Enter","ArrowUp","ArrowDown"],Ub=[" ","Enter"],Ws="Select",[Gl,ql,Vb]=vd(Ws),[Oo,ok]=kr(Ws,[Vb,Hl]),Yl=Hl(),[Bb,Gn]=Oo(Ws),[Hb,Wb]=Oo(Ws),Ey=e=>{const{__scopeSelect:t,children:n,open:r,defaultOpen:o,onOpenChange:s,value:i,defaultValue:l,onValueChange:a,dir:c,name:p,autoComplete:d,disabled:g,required:S,form:w}=e,m=Yl(t),[x,v]=f.useState(null),[h,y]=f.useState(null),[C,b]=f.useState(!1),k=cy(c),[N=!1,P]=dl({prop:r,defaultProp:o,onChange:s}),[_,O]=dl({prop:i,defaultProp:l,onChange:a}),I=f.useRef(null),M=x?w||!!x.closest("form"):!0,[L,T]=f.useState(new Set),U=Array.from(L).map(z=>z.props.value).join(";");return u.jsx(S2,{...m,children:u.jsxs(Bb,{required:S,scope:t,trigger:x,onTriggerChange:v,valueNode:h,onValueNodeChange:y,valueNodeHasChildren:C,onValueNodeHasChildrenChange:b,contentId:Nd(),value:_,onValueChange:O,open:N,onOpenChange:P,dir:k,triggerPointerDownPosRef:I,disabled:g,children:[u.jsx(Gl.Provider,{scope:t,children:u.jsx(Hb,{scope:e.__scopeSelect,onNativeOptionAdd:f.useCallback(z=>{T(B=>new Set(B).add(z))},[]),onNativeOptionRemove:f.useCallback(z=>{T(B=>{const j=new Set(B);return j.delete(z),j})},[]),children:n})}),M?u.jsxs(Gy,{"aria-hidden":!0,required:S,tabIndex:-1,name:p,autoComplete:d,value:_,onChange:z=>O(z.target.value),disabled:g,form:w,children:[_===void 0?u.jsx("option",{value:""}):null,Array.from(L)]},U):null]})})};Ey.displayName=Ws;var by="SelectTrigger",Ny=f.forwardRef((e,t)=>{const{__scopeSelect:n,disabled:r=!1,...o}=e,s=Yl(n),i=Gn(by,n),l=i.disabled||r,a=ie(t,i.onTriggerChange),c=ql(n),p=f.useRef("touch"),[d,g,S]=qy(m=>{const x=c().filter(y=>!y.disabled),v=x.find(y=>y.value===i.value),h=Yy(x,m,v);h!==void 0&&i.onValueChange(h.value)}),w=m=>{l||(i.onOpenChange(!0),S()),m&&(i.triggerPointerDownPosRef.current={x:Math.round(m.pageX),y:Math.round(m.pageY)})};return u.jsx(Jg,{asChild:!0,...s,children:u.jsx(ee.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:l,"data-disabled":l?"":void 0,"data-placeholder":Qy(i.value)?"":void 0,...o,ref:a,onClick:K(o.onClick,m=>{m.currentTarget.focus(),p.current!=="mouse"&&w(m)}),onPointerDown:K(o.onPointerDown,m=>{p.current=m.pointerType;const x=m.target;x.hasPointerCapture(m.pointerId)&&x.releasePointerCapture(m.pointerId),m.button===0&&m.ctrlKey===!1&&m.pointerType==="mouse"&&(w(m),m.preventDefault())}),onKeyDown:K(o.onKeyDown,m=>{const x=d.current!=="";!(m.ctrlKey||m.altKey||m.metaKey)&&m.key.length===1&&g(m.key),!(x&&m.key===" ")&&$b.includes(m.key)&&(w(),m.preventDefault())})})})});Ny.displayName=by;var ky="SelectValue",Py=f.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:s,placeholder:i="",...l}=e,a=Gn(ky,n),{onValueNodeHasChildrenChange:c}=a,p=s!==void 0,d=ie(t,a.onValueNodeChange);return De(()=>{c(p)},[c,p]),u.jsx(ee.span,{...l,ref:d,style:{pointerEvents:"none"},children:Qy(a.value)?u.jsx(u.Fragment,{children:i}):s})});Py.displayName=ky;var Kb="SelectIcon",jy=f.forwardRef((e,t)=>{const{__scopeSelect:n,children:r,...o}=e;return u.jsx(ee.span,{"aria-hidden":!0,...o,ref:t,children:r||"▼"})});jy.displayName=Kb;var Qb="SelectPortal",Ty=e=>u.jsx(gd,{asChild:!0,...e});Ty.displayName=Qb;var Cr="SelectContent",Ry=f.forwardRef((e,t)=>{const n=Gn(Cr,e.__scopeSelect),[r,o]=f.useState();if(De(()=>{o(new DocumentFragment)},[]),!n.open){const s=r;return s?Nr.createPortal(u.jsx(_y,{scope:e.__scopeSelect,children:u.jsx(Gl.Slot,{scope:e.__scopeSelect,children:u.jsx("div",{children:e.children})})}),s):null}return u.jsx(Oy,{...e,ref:t})});Ry.displayName=Cr;var St=10,[_y,qn]=Oo(Cr),Gb="SelectContentImpl",qb=Rs("SelectContent.RemoveScroll"),Oy=f.forwardRef((e,t)=>{const{__scopeSelect:n,position:r="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:s,onPointerDownOutside:i,side:l,sideOffset:a,align:c,alignOffset:p,arrowPadding:d,collisionBoundary:g,collisionPadding:S,sticky:w,hideWhenDetached:m,avoidCollisions:x,...v}=e,h=Gn(Cr,n),[y,C]=f.useState(null),[b,k]=f.useState(null),N=ie(t,H=>C(H)),[P,_]=f.useState(null),[O,I]=f.useState(null),M=ql(n),[L,T]=f.useState(!1),U=f.useRef(!1);f.useEffect(()=>{if(y)return rb(y)},[y]),K2();const z=f.useCallback(H=>{const[ue,..._e]=M().map(se=>se.ref.current),[le]=_e.slice(-1),oe=document.activeElement;for(const se of H)if(se===oe||(se==null||se.scrollIntoView({block:"nearest"}),se===ue&&b&&(b.scrollTop=0),se===le&&b&&(b.scrollTop=b.scrollHeight),se==null||se.focus(),document.activeElement!==oe))return},[M,b]),B=f.useCallback(()=>z([P,y]),[z,P,y]);f.useEffect(()=>{L&&B()},[L,B]);const{onOpenChange:j,triggerPointerDownPosRef:A}=h;f.useEffect(()=>{if(y){let H={x:0,y:0};const ue=le=>{var oe,se;H={x:Math.abs(Math.round(le.pageX)-(((oe=A.current)==null?void 0:oe.x)??0)),y:Math.abs(Math.round(le.pageY)-(((se=A.current)==null?void 0:se.y)??0))}},_e=le=>{H.x<=10&&H.y<=10?le.preventDefault():y.contains(le.target)||j(!1),document.removeEventListener("pointermove",ue),A.current=null};return A.current!==null&&(document.addEventListener("pointermove",ue),document.addEventListener("pointerup",_e,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",ue),document.removeEventListener("pointerup",_e,{capture:!0})}}},[y,j,A]),f.useEffect(()=>{const H=()=>j(!1);return window.addEventListener("blur",H),window.addEventListener("resize",H),()=>{window.removeEventListener("blur",H),window.removeEventListener("resize",H)}},[j]);const[$,W]=qy(H=>{const ue=M().filter(oe=>!oe.disabled),_e=ue.find(oe=>oe.ref.current===document.activeElement),le=Yy(ue,H,_e);le&&setTimeout(()=>le.ref.current.focus())}),re=f.useCallback((H,ue,_e)=>{const le=!U.current&&!_e;(h.value!==void 0&&h.value===ue||le)&&(_(H),le&&(U.current=!0))},[h.value]),Fe=f.useCallback(()=>y==null?void 0:y.focus(),[y]),Ce=f.useCallback((H,ue,_e)=>{const le=!U.current&&!_e;(h.value!==void 0&&h.value===ue||le)&&I(H)},[h.value]),Ot=r==="popper"?sc:My,ze=Ot===sc?{side:l,sideOffset:a,align:c,alignOffset:p,arrowPadding:d,collisionBoundary:g,collisionPadding:S,sticky:w,hideWhenDetached:m,avoidCollisions:x}:{};return u.jsx(_y,{scope:n,content:y,viewport:b,onViewportChange:k,itemRefCallback:re,selectedItem:P,onItemLeave:Fe,itemTextRefCallback:Ce,focusSelectedItem:B,selectedItemText:O,position:r,isPositioned:L,searchRef:$,children:u.jsx(Cy,{as:qb,allowPinchZoom:!0,children:u.jsx(dy,{asChild:!0,trapped:h.open,onMountAutoFocus:H=>{H.preventDefault()},onUnmountAutoFocus:K(o,H=>{var ue;(ue=h.trigger)==null||ue.focus({preventScroll:!0}),H.preventDefault()}),children:u.jsx(Dl,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:s,onPointerDownOutside:i,onFocusOutside:H=>H.preventDefault(),onDismiss:()=>h.onOpenChange(!1),children:u.jsx(Ot,{role:"listbox",id:h.contentId,"data-state":h.open?"open":"closed",dir:h.dir,onContextMenu:H=>H.preventDefault(),...v,...ze,onPlaced:()=>T(!0),ref:N,style:{display:"flex",flexDirection:"column",outline:"none",...v.style},onKeyDown:K(v.onKeyDown,H=>{const ue=H.ctrlKey||H.altKey||H.metaKey;if(H.key==="Tab"&&H.preventDefault(),!ue&&H.key.length===1&&W(H.key),["ArrowUp","ArrowDown","Home","End"].includes(H.key)){let le=M().filter(oe=>!oe.disabled).map(oe=>oe.ref.current);if(["ArrowUp","End"].includes(H.key)&&(le=le.slice().reverse()),["ArrowUp","ArrowDown"].includes(H.key)){const oe=H.target,se=le.indexOf(oe);le=le.slice(se+1)}setTimeout(()=>z(le)),H.preventDefault()}})})})})})})});Oy.displayName=Gb;var Yb="SelectItemAlignedPosition",My=f.forwardRef((e,t)=>{const{__scopeSelect:n,onPlaced:r,...o}=e,s=Gn(Cr,n),i=qn(Cr,n),[l,a]=f.useState(null),[c,p]=f.useState(null),d=ie(t,N=>p(N)),g=ql(n),S=f.useRef(!1),w=f.useRef(!0),{viewport:m,selectedItem:x,selectedItemText:v,focusSelectedItem:h}=i,y=f.useCallback(()=>{if(s.trigger&&s.valueNode&&l&&c&&m&&x&&v){const N=s.trigger.getBoundingClientRect(),P=c.getBoundingClientRect(),_=s.valueNode.getBoundingClientRect(),O=v.getBoundingClientRect();if(s.dir!=="rtl"){const oe=O.left-P.left,se=_.left-oe,tt=N.left-se,Mt=N.width+tt,Ao=Math.max(Mt,P.width),Io=window.innerWidth-St,Lo=gl(se,[St,Math.max(St,Io-Ao)]);l.style.minWidth=Mt+"px",l.style.left=Lo+"px"}else{const oe=P.right-O.right,se=window.innerWidth-_.right-oe,tt=window.innerWidth-N.right-se,Mt=N.width+tt,Ao=Math.max(Mt,P.width),Io=window.innerWidth-St,Lo=gl(se,[St,Math.max(St,Io-Ao)]);l.style.minWidth=Mt+"px",l.style.right=Lo+"px"}const I=g(),M=window.innerHeight-St*2,L=m.scrollHeight,T=window.getComputedStyle(c),U=parseInt(T.borderTopWidth,10),z=parseInt(T.paddingTop,10),B=parseInt(T.borderBottomWidth,10),j=parseInt(T.paddingBottom,10),A=U+z+L+j+B,$=Math.min(x.offsetHeight*5,A),W=window.getComputedStyle(m),re=parseInt(W.paddingTop,10),Fe=parseInt(W.paddingBottom,10),Ce=N.top+N.height/2-St,Ot=M-Ce,ze=x.offsetHeight/2,H=x.offsetTop+ze,ue=U+z+H,_e=A-ue;if(ue<=Ce){const oe=I.length>0&&x===I[I.length-1].ref.current;l.style.bottom="0px";const se=c.clientHeight-m.offsetTop-m.offsetHeight,tt=Math.max(Ot,ze+(oe?Fe:0)+se+B),Mt=ue+tt;l.style.height=Mt+"px"}else{const oe=I.length>0&&x===I[0].ref.current;l.style.top="0px";const tt=Math.max(Ce,U+m.offsetTop+(oe?re:0)+ze)+_e;l.style.height=tt+"px",m.scrollTop=ue-Ce+m.offsetTop}l.style.margin=`${St}px 0`,l.style.minHeight=$+"px",l.style.maxHeight=M+"px",r==null||r(),requestAnimationFrame(()=>S.current=!0)}},[g,s.trigger,s.valueNode,l,c,m,x,v,s.dir,r]);De(()=>y(),[y]);const[C,b]=f.useState();De(()=>{c&&b(window.getComputedStyle(c).zIndex)},[c]);const k=f.useCallback(N=>{N&&w.current===!0&&(y(),h==null||h(),w.current=!1)},[y,h]);return u.jsx(Zb,{scope:n,contentWrapper:l,shouldExpandOnScrollRef:S,onScrollButtonChange:k,children:u.jsx("div",{ref:a,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:C},children:u.jsx(ee.div,{...o,ref:d,style:{boxSizing:"border-box",maxHeight:"100%",...o.style}})})})});My.displayName=Yb;var Xb="SelectPopperPosition",sc=f.forwardRef((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=St,...s}=e,i=Yl(n);return u.jsx(ey,{...i,...s,ref:t,align:r,collisionPadding:o,style:{boxSizing:"border-box",...s.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});sc.displayName=Xb;var[Zb,Ld]=Oo(Cr,{}),ic="SelectViewport",Ay=f.forwardRef((e,t)=>{const{__scopeSelect:n,nonce:r,...o}=e,s=qn(ic,n),i=Ld(ic,n),l=ie(t,s.onViewportChange),a=f.useRef(0);return u.jsxs(u.Fragment,{children:[u.jsx("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:r}),u.jsx(Gl.Slot,{scope:n,children:u.jsx(ee.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:l,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:K(o.onScroll,c=>{const p=c.currentTarget,{contentWrapper:d,shouldExpandOnScrollRef:g}=i;if(g!=null&&g.current&&d){const S=Math.abs(a.current-p.scrollTop);if(S>0){const w=window.innerHeight-St*2,m=parseFloat(d.style.minHeight),x=parseFloat(d.style.height),v=Math.max(m,x);if(v<w){const h=v+S,y=Math.min(w,h),C=h-y;d.style.height=y+"px",d.style.bottom==="0px"&&(p.scrollTop=C>0?C:0,d.style.justifyContent="flex-end")}}}a.current=p.scrollTop})})})]})});Ay.displayName=ic;var Iy="SelectGroup",[Jb,eN]=Oo(Iy),tN=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Nd();return u.jsx(Jb,{scope:n,id:o,children:u.jsx(ee.div,{role:"group","aria-labelledby":o,...r,ref:t})})});tN.displayName=Iy;var Ly="SelectLabel",Dy=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=eN(Ly,n);return u.jsx(ee.div,{id:o.id,...r,ref:t})});Dy.displayName=Ly;var yl="SelectItem",[nN,Fy]=Oo(yl),zy=f.forwardRef((e,t)=>{const{__scopeSelect:n,value:r,disabled:o=!1,textValue:s,...i}=e,l=Gn(yl,n),a=qn(yl,n),c=l.value===r,[p,d]=f.useState(s??""),[g,S]=f.useState(!1),w=ie(t,h=>{var y;return(y=a.itemRefCallback)==null?void 0:y.call(a,h,r,o)}),m=Nd(),x=f.useRef("touch"),v=()=>{o||(l.onValueChange(r),l.onOpenChange(!1))};if(r==="")throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return u.jsx(nN,{scope:n,value:r,disabled:o,textId:m,isSelected:c,onItemTextChange:f.useCallback(h=>{d(y=>y||((h==null?void 0:h.textContent)??"").trim())},[]),children:u.jsx(Gl.ItemSlot,{scope:n,value:r,disabled:o,textValue:p,children:u.jsx(ee.div,{role:"option","aria-labelledby":m,"data-highlighted":g?"":void 0,"aria-selected":c&&g,"data-state":c?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...i,ref:w,onFocus:K(i.onFocus,()=>S(!0)),onBlur:K(i.onBlur,()=>S(!1)),onClick:K(i.onClick,()=>{x.current!=="mouse"&&v()}),onPointerUp:K(i.onPointerUp,()=>{x.current==="mouse"&&v()}),onPointerDown:K(i.onPointerDown,h=>{x.current=h.pointerType}),onPointerMove:K(i.onPointerMove,h=>{var y;x.current=h.pointerType,o?(y=a.onItemLeave)==null||y.call(a):x.current==="mouse"&&h.currentTarget.focus({preventScroll:!0})}),onPointerLeave:K(i.onPointerLeave,h=>{var y;h.currentTarget===document.activeElement&&((y=a.onItemLeave)==null||y.call(a))}),onKeyDown:K(i.onKeyDown,h=>{var C;((C=a.searchRef)==null?void 0:C.current)!==""&&h.key===" "||(Ub.includes(h.key)&&v(),h.key===" "&&h.preventDefault())})})})})});zy.displayName=yl;var Zo="SelectItemText",$y=f.forwardRef((e,t)=>{const{__scopeSelect:n,className:r,style:o,...s}=e,i=Gn(Zo,n),l=qn(Zo,n),a=Fy(Zo,n),c=Wb(Zo,n),[p,d]=f.useState(null),g=ie(t,v=>d(v),a.onItemTextChange,v=>{var h;return(h=l.itemTextRefCallback)==null?void 0:h.call(l,v,a.value,a.disabled)}),S=p==null?void 0:p.textContent,w=f.useMemo(()=>u.jsx("option",{value:a.value,disabled:a.disabled,children:S},a.value),[a.disabled,a.value,S]),{onNativeOptionAdd:m,onNativeOptionRemove:x}=c;return De(()=>(m(w),()=>x(w)),[m,x,w]),u.jsxs(u.Fragment,{children:[u.jsx(ee.span,{id:a.textId,...s,ref:g}),a.isSelected&&i.valueNode&&!i.valueNodeHasChildren?Nr.createPortal(s.children,i.valueNode):null]})});$y.displayName=Zo;var Uy="SelectItemIndicator",Vy=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return Fy(Uy,n).isSelected?u.jsx(ee.span,{"aria-hidden":!0,...r,ref:t}):null});Vy.displayName=Uy;var lc="SelectScrollUpButton",By=f.forwardRef((e,t)=>{const n=qn(lc,e.__scopeSelect),r=Ld(lc,e.__scopeSelect),[o,s]=f.useState(!1),i=ie(t,r.onScrollButtonChange);return De(()=>{if(n.viewport&&n.isPositioned){let l=function(){const c=a.scrollTop>0;s(c)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?u.jsx(Wy,{...e,ref:i,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop-a.offsetHeight)}}):null});By.displayName=lc;var ac="SelectScrollDownButton",Hy=f.forwardRef((e,t)=>{const n=qn(ac,e.__scopeSelect),r=Ld(ac,e.__scopeSelect),[o,s]=f.useState(!1),i=ie(t,r.onScrollButtonChange);return De(()=>{if(n.viewport&&n.isPositioned){let l=function(){const c=a.scrollHeight-a.clientHeight,p=Math.ceil(a.scrollTop)<c;s(p)};const a=n.viewport;return l(),a.addEventListener("scroll",l),()=>a.removeEventListener("scroll",l)}},[n.viewport,n.isPositioned]),o?u.jsx(Wy,{...e,ref:i,onAutoScroll:()=>{const{viewport:l,selectedItem:a}=n;l&&a&&(l.scrollTop=l.scrollTop+a.offsetHeight)}}):null});Hy.displayName=ac;var Wy=f.forwardRef((e,t)=>{const{__scopeSelect:n,onAutoScroll:r,...o}=e,s=qn("SelectScrollButton",n),i=f.useRef(null),l=ql(n),a=f.useCallback(()=>{i.current!==null&&(window.clearInterval(i.current),i.current=null)},[]);return f.useEffect(()=>()=>a(),[a]),De(()=>{var p;const c=l().find(d=>d.ref.current===document.activeElement);(p=c==null?void 0:c.ref.current)==null||p.scrollIntoView({block:"nearest"})},[l]),u.jsx(ee.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:K(o.onPointerDown,()=>{i.current===null&&(i.current=window.setInterval(r,50))}),onPointerMove:K(o.onPointerMove,()=>{var c;(c=s.onItemLeave)==null||c.call(s),i.current===null&&(i.current=window.setInterval(r,50))}),onPointerLeave:K(o.onPointerLeave,()=>{a()})})}),rN="SelectSeparator",Ky=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e;return u.jsx(ee.div,{"aria-hidden":!0,...r,ref:t})});Ky.displayName=rN;var uc="SelectArrow",oN=f.forwardRef((e,t)=>{const{__scopeSelect:n,...r}=e,o=Yl(n),s=Gn(uc,n),i=qn(uc,n);return s.open&&i.position==="popper"?u.jsx(ty,{...o,...r,ref:t}):null});oN.displayName=uc;function Qy(e){return e===""||e===void 0}var Gy=f.forwardRef((e,t)=>{const{value:n,...r}=e,o=f.useRef(null),s=ie(t,o),i=py(n);return f.useEffect(()=>{const l=o.current,a=window.HTMLSelectElement.prototype,p=Object.getOwnPropertyDescriptor(a,"value").set;if(i!==n&&p){const d=new Event("change",{bubbles:!0});p.call(l,n),l.dispatchEvent(d)}},[i,n]),u.jsx(Bs,{asChild:!0,children:u.jsx("select",{...r,ref:s,defaultValue:n})})});Gy.displayName="BubbleSelect";function qy(e){const t=ut(e),n=f.useRef(""),r=f.useRef(0),o=f.useCallback(i=>{const l=n.current+i;t(l),function a(c){n.current=c,window.clearTimeout(r.current),c!==""&&(r.current=window.setTimeout(()=>a(""),1e3))}(l)},[t]),s=f.useCallback(()=>{n.current="",window.clearTimeout(r.current)},[]);return f.useEffect(()=>()=>window.clearTimeout(r.current),[]),[n,o,s]}function Yy(e,t,n){const o=t.length>1&&Array.from(t).every(c=>c===t[0])?t[0]:t,s=n?e.indexOf(n):-1;let i=sN(e,Math.max(s,0));o.length===1&&(i=i.filter(c=>c!==n));const a=i.find(c=>c.textValue.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}function sN(e,t){return e.map((n,r)=>e[(t+r)%e.length])}var iN=Ey,Xy=Ny,lN=Py,aN=jy,uN=Ty,Zy=Ry,cN=Ay,Jy=Dy,e0=zy,dN=$y,fN=Vy,t0=By,n0=Hy,r0=Ky;const pN=iN,hN=lN,o0=f.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(Xy,{ref:r,className:q("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...n,children:[t,u.jsx(aN,{asChild:!0,children:u.jsx(yg,{className:"h-4 w-4 opacity-50"})})]}));o0.displayName=Xy.displayName;const s0=f.forwardRef(({className:e,...t},n)=>u.jsx(t0,{ref:n,className:q("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(hC,{className:"h-4 w-4"})}));s0.displayName=t0.displayName;const i0=f.forwardRef(({className:e,...t},n)=>u.jsx(n0,{ref:n,className:q("flex cursor-default items-center justify-center py-1",e),...t,children:u.jsx(yg,{className:"h-4 w-4"})}));i0.displayName=n0.displayName;const l0=f.forwardRef(({className:e,children:t,position:n="popper",...r},o)=>u.jsx(uN,{children:u.jsxs(Zy,{ref:o,className:q("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]",n==="popper"&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:n,...r,children:[u.jsx(s0,{}),u.jsx(cN,{className:q("p-1",n==="popper"&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),u.jsx(i0,{})]})}));l0.displayName=Zy.displayName;const mN=f.forwardRef(({className:e,...t},n)=>u.jsx(Jy,{ref:n,className:q("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t}));mN.displayName=Jy.displayName;const Jo=f.forwardRef(({className:e,children:t,...n},r)=>u.jsxs(e0,{ref:r,className:q("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...n,children:[u.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:u.jsx(fN,{children:u.jsx(pC,{className:"h-4 w-4"})})}),u.jsx(dN,{children:t})]}));Jo.displayName=e0.displayName;const vN=f.forwardRef(({className:e,...t},n)=>u.jsx(r0,{ref:n,className:q("-mx-1 my-1 h-px bg-muted",e),...t}));vN.displayName=r0.displayName;var a0=["PageUp","PageDown"],u0=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],c0={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},Mo="Slider",[cc,gN,yN]=vd(Mo),[d0,sk]=kr(Mo,[yN]),[xN,Xl]=d0(Mo),f0=f.forwardRef((e,t)=>{const{name:n,min:r=0,max:o=100,step:s=1,orientation:i="horizontal",disabled:l=!1,minStepsBetweenThumbs:a=0,defaultValue:c=[r],value:p,onValueChange:d=()=>{},onValueCommit:g=()=>{},inverted:S=!1,form:w,...m}=e,x=f.useRef(new Set),v=f.useRef(0),y=i==="horizontal"?wN:SN,[C=[],b]=dl({prop:p,defaultProp:c,onChange:I=>{var L;(L=[...x.current][v.current])==null||L.focus(),d(I)}}),k=f.useRef(C);function N(I){const M=kN(C,I);O(I,M)}function P(I){O(I,v.current)}function _(){const I=k.current[v.current];C[v.current]!==I&&g(C)}function O(I,M,{commit:L}={commit:!1}){const T=RN(s),U=_N(Math.round((I-r)/s)*s+r,T),z=gl(U,[r,o]);b((B=[])=>{const j=bN(B,z,M);if(TN(j,a*s)){v.current=j.indexOf(z);const A=String(j)!==String(B);return A&&L&&g(j),A?j:B}else return B})}return u.jsx(xN,{scope:e.__scopeSlider,name:n,disabled:l,min:r,max:o,valueIndexToChangeRef:v,thumbs:x.current,values:C,orientation:i,form:w,children:u.jsx(cc.Provider,{scope:e.__scopeSlider,children:u.jsx(cc.Slot,{scope:e.__scopeSlider,children:u.jsx(y,{"aria-disabled":l,"data-disabled":l?"":void 0,...m,ref:t,onPointerDown:K(m.onPointerDown,()=>{l||(k.current=C)}),min:r,max:o,inverted:S,onSlideStart:l?void 0:N,onSlideMove:l?void 0:P,onSlideEnd:l?void 0:_,onHomeKeyDown:()=>!l&&O(r,0,{commit:!0}),onEndKeyDown:()=>!l&&O(o,C.length-1,{commit:!0}),onStepKeyDown:({event:I,direction:M})=>{if(!l){const U=a0.includes(I.key)||I.shiftKey&&u0.includes(I.key)?10:1,z=v.current,B=C[z],j=s*U*M;O(B+j,z,{commit:!0})}}})})})})});f0.displayName=Mo;var[p0,h0]=d0(Mo,{startEdge:"left",endEdge:"right",size:"width",direction:1}),wN=f.forwardRef((e,t)=>{const{min:n,max:r,dir:o,inverted:s,onSlideStart:i,onSlideMove:l,onSlideEnd:a,onStepKeyDown:c,...p}=e,[d,g]=f.useState(null),S=ie(t,y=>g(y)),w=f.useRef(void 0),m=cy(o),x=m==="ltr",v=x&&!s||!x&&s;function h(y){const C=w.current||d.getBoundingClientRect(),b=[0,C.width],N=Dd(b,v?[n,r]:[r,n]);return w.current=C,N(y-C.left)}return u.jsx(p0,{scope:e.__scopeSlider,startEdge:v?"left":"right",endEdge:v?"right":"left",direction:v?1:-1,size:"width",children:u.jsx(m0,{dir:m,"data-orientation":"horizontal",...p,ref:S,style:{...p.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:y=>{const C=h(y.clientX);i==null||i(C)},onSlideMove:y=>{const C=h(y.clientX);l==null||l(C)},onSlideEnd:()=>{w.current=void 0,a==null||a()},onStepKeyDown:y=>{const b=c0[v?"from-left":"from-right"].includes(y.key);c==null||c({event:y,direction:b?-1:1})}})})}),SN=f.forwardRef((e,t)=>{const{min:n,max:r,inverted:o,onSlideStart:s,onSlideMove:i,onSlideEnd:l,onStepKeyDown:a,...c}=e,p=f.useRef(null),d=ie(t,p),g=f.useRef(void 0),S=!o;function w(m){const x=g.current||p.current.getBoundingClientRect(),v=[0,x.height],y=Dd(v,S?[r,n]:[n,r]);return g.current=x,y(m-x.top)}return u.jsx(p0,{scope:e.__scopeSlider,startEdge:S?"bottom":"top",endEdge:S?"top":"bottom",size:"height",direction:S?1:-1,children:u.jsx(m0,{"data-orientation":"vertical",...c,ref:d,style:{...c.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:m=>{const x=w(m.clientY);s==null||s(x)},onSlideMove:m=>{const x=w(m.clientY);i==null||i(x)},onSlideEnd:()=>{g.current=void 0,l==null||l()},onStepKeyDown:m=>{const v=c0[S?"from-bottom":"from-top"].includes(m.key);a==null||a({event:m,direction:v?-1:1})}})})}),m0=f.forwardRef((e,t)=>{const{__scopeSlider:n,onSlideStart:r,onSlideMove:o,onSlideEnd:s,onHomeKeyDown:i,onEndKeyDown:l,onStepKeyDown:a,...c}=e,p=Xl(Mo,n);return u.jsx(ee.span,{...c,ref:t,onKeyDown:K(e.onKeyDown,d=>{d.key==="Home"?(i(d),d.preventDefault()):d.key==="End"?(l(d),d.preventDefault()):a0.concat(u0).includes(d.key)&&(a(d),d.preventDefault())}),onPointerDown:K(e.onPointerDown,d=>{const g=d.target;g.setPointerCapture(d.pointerId),d.preventDefault(),p.thumbs.has(g)?g.focus():r(d)}),onPointerMove:K(e.onPointerMove,d=>{d.target.hasPointerCapture(d.pointerId)&&o(d)}),onPointerUp:K(e.onPointerUp,d=>{const g=d.target;g.hasPointerCapture(d.pointerId)&&(g.releasePointerCapture(d.pointerId),s(d))})})}),v0="SliderTrack",g0=f.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Xl(v0,n);return u.jsx(ee.span,{"data-disabled":o.disabled?"":void 0,"data-orientation":o.orientation,...r,ref:t})});g0.displayName=v0;var dc="SliderRange",y0=f.forwardRef((e,t)=>{const{__scopeSlider:n,...r}=e,o=Xl(dc,n),s=h0(dc,n),i=f.useRef(null),l=ie(t,i),a=o.values.length,c=o.values.map(g=>w0(g,o.min,o.max)),p=a>1?Math.min(...c):0,d=100-Math.max(...c);return u.jsx(ee.span,{"data-orientation":o.orientation,"data-disabled":o.disabled?"":void 0,...r,ref:l,style:{...e.style,[s.startEdge]:p+"%",[s.endEdge]:d+"%"}})});y0.displayName=dc;var fc="SliderThumb",x0=f.forwardRef((e,t)=>{const n=gN(e.__scopeSlider),[r,o]=f.useState(null),s=ie(t,l=>o(l)),i=f.useMemo(()=>r?n().findIndex(l=>l.ref.current===r):-1,[n,r]);return u.jsx(CN,{...e,ref:s,index:i})}),CN=f.forwardRef((e,t)=>{const{__scopeSlider:n,index:r,name:o,...s}=e,i=Xl(fc,n),l=h0(fc,n),[a,c]=f.useState(null),p=ie(t,h=>c(h)),d=a?i.form||!!a.closest("form"):!0,g=Bg(a),S=i.values[r],w=S===void 0?0:w0(S,i.min,i.max),m=NN(r,i.values.length),x=g==null?void 0:g[l.size],v=x?PN(x,w,l.direction):0;return f.useEffect(()=>{if(a)return i.thumbs.add(a),()=>{i.thumbs.delete(a)}},[a,i.thumbs]),u.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[l.startEdge]:`calc(${w}% + ${v}px)`},children:[u.jsx(cc.ItemSlot,{scope:e.__scopeSlider,children:u.jsx(ee.span,{role:"slider","aria-label":e["aria-label"]||m,"aria-valuemin":i.min,"aria-valuenow":S,"aria-valuemax":i.max,"aria-orientation":i.orientation,"data-orientation":i.orientation,"data-disabled":i.disabled?"":void 0,tabIndex:i.disabled?void 0:0,...s,ref:p,style:S===void 0?{display:"none"}:e.style,onFocus:K(e.onFocus,()=>{i.valueIndexToChangeRef.current=r})})}),d&&u.jsx(EN,{name:o??(i.name?i.name+(i.values.length>1?"[]":""):void 0),form:i.form,value:S},r)]})});x0.displayName=fc;var EN=e=>{const{value:t,...n}=e,r=f.useRef(null),o=py(t);return f.useEffect(()=>{const s=r.current,i=window.HTMLInputElement.prototype,a=Object.getOwnPropertyDescriptor(i,"value").set;if(o!==t&&a){const c=new Event("input",{bubbles:!0});a.call(s,t),s.dispatchEvent(c)}},[o,t]),u.jsx("input",{style:{display:"none"},...n,ref:r,defaultValue:t})};function bN(e=[],t,n){const r=[...e];return r[n]=t,r.sort((o,s)=>o-s)}function w0(e,t,n){const s=100/(n-t)*(e-t);return gl(s,[0,100])}function NN(e,t){return t>2?`Value ${e+1} of ${t}`:t===2?["Minimum","Maximum"][e]:void 0}function kN(e,t){if(e.length===1)return 0;const n=e.map(o=>Math.abs(o-t)),r=Math.min(...n);return n.indexOf(r)}function PN(e,t,n){const r=e/2,s=Dd([0,50],[0,r]);return(r-s(t)*n)*n}function jN(e){return e.slice(0,-1).map((t,n)=>e[n+1]-t)}function TN(e,t){if(t>0){const n=jN(e);return Math.min(...n)>=t}return!0}function Dd(e,t){return n=>{if(e[0]===e[1]||t[0]===t[1])return t[0];const r=(t[1]-t[0])/(e[1]-e[0]);return t[0]+r*(n-e[0])}}function RN(e){return(String(e).split(".")[1]||"").length}function _N(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var S0=f0,ON=g0,MN=y0,AN=x0;const C0=f.forwardRef(({className:e,...t},n)=>u.jsxs(S0,{ref:n,className:q("relative flex w-full touch-none select-none items-center",e),...t,children:[u.jsx(ON,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:u.jsx(MN,{className:"absolute h-full bg-primary"})}),u.jsx(AN,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]}));C0.displayName=S0.displayName;function Fd(e,t={}){const[n,r]=f.useState(!1),[o,s]=f.useState(null),i=f.useRef(null),l=f.useRef(null),a=f.useRef(0),c=t.maxReconnectAttempts||5,p=t.reconnectInterval||3e3,d=f.useCallback(()=>{try{const m=`${window.location.protocol==="https:"?"wss:":"ws:"}//${window.location.host}${e}`;i.current=new WebSocket(m),i.current.onopen=()=>{var x;r(!0),s(null),a.current=0,(x=t.onConnect)==null||x.call(t)},i.current.onmessage=x=>{var v;try{const h=JSON.parse(x.data);(v=t.onMessage)==null||v.call(t,h)}catch(h){console.error("Failed to parse WebSocket message:",h)}},i.current.onclose=()=>{var x;r(!1),(x=t.onDisconnect)==null||x.call(t),a.current<c?(a.current++,l.current=setTimeout(()=>{d()},p)):s("Max reconnection attempts reached")},i.current.onerror=x=>{var v;s("WebSocket connection error"),(v=t.onError)==null||v.call(t,x)}}catch(w){s("Failed to create WebSocket connection"),console.error("WebSocket connection error:",w)}},[e,t,c,p]),g=f.useCallback(()=>{l.current&&clearTimeout(l.current),i.current&&(i.current.close(),i.current=null),r(!1)},[]),S=f.useCallback(w=>{i.current&&n&&i.current.send(JSON.stringify(w))},[n]);return f.useEffect(()=>(d(),()=>{g()}),[d,g]),{socket:i.current,isConnected:n,error:o,sendMessage:S,connect:d,disconnect:g}}function IN(e,t={}){const[n,r]=f.useState(null),[o,s]=f.useState(null),[i,l]=f.useState(!1),[a,c]=f.useState(!1),[p,d]=f.useState(null),g=f.useRef(null),S=f.useRef(null),w=f.useRef(null),m=f.useRef(null),{socket:x,isConnected:v,sendMessage:h}=Fd("/ws",{onMessage:b}),y={iceServers:[{urls:"stun:stun.l.google.com:19302"},{urls:"stun:stun1.l.google.com:19302"}]},C=f.useCallback(()=>{if(g.current)return g.current;const L=new RTCPeerConnection(y);return g.current=L,L.onicecandidate=T=>{T.candidate&&x&&h({type:"webrtc_ice_candidate",candidate:T.candidate,streamId:e,targetConnectionId:m.current})},L.ontrack=T=>{const[U]=T.streams;s(U),w.current&&(w.current.srcObject=U)},L.onconnectionstatechange=()=>{l(L.connectionState==="connected"),L.connectionState==="failed"&&d("WebRTC connection failed")},L},[x,h,e]);function b(L){if(L.streamId===e)switch(L.type){case"webrtc_offer":k(L.offer);break;case"webrtc_answer":N(L.answer);break;case"webrtc_ice_candidate":P(L.candidate);break}}const k=async L=>{try{const T=C();await T.setRemoteDescription(L);const U=await T.createAnswer();await T.setLocalDescription(U),h({type:"webrtc_answer",answer:U,streamId:e,targetConnectionId:m.current})}catch(T){d("Failed to handle offer"),console.error("Handle offer error:",T)}},N=async L=>{try{const T=g.current;T&&await T.setRemoteDescription(L)}catch(T){d("Failed to handle answer"),console.error("Handle answer error:",T)}},P=async L=>{try{const T=g.current;T&&await T.addIceCandidate(L)}catch(T){console.error("Handle ICE candidate error:",T)}},_=f.useCallback(async()=>{try{const L=await navigator.mediaDevices.getUserMedia({video:!0,audio:!0});r(L),c(!0),S.current&&(S.current.srcObject=L);const T=C();L.getTracks().forEach(z=>{T.addTrack(z,L)});const U=await T.createOffer();await T.setLocalDescription(U),h({type:"webrtc_offer",offer:U,streamId:e,connectionType:"publisher"})}catch(L){d("Failed to start streaming"),console.error("Start streaming error:",L)}},[C,h,e]),O=f.useCallback(()=>{n&&(n.getTracks().forEach(L=>L.stop()),r(null)),g.current&&(g.current.close(),g.current=null),c(!1),l(!1)},[n]),I=f.useCallback(()=>{v&&(m.current=Math.random().toString(36).substring(7),h({type:"join_stream",streamId:e,connectionId:m.current,connectionType:"subscriber"}),C())},[v,h,e,C]),M=f.useCallback(()=>{g.current&&(g.current.close(),g.current=null),s(null),l(!1),h({type:"leave_stream",streamId:e,connectionId:m.current})},[h,e]);return f.useEffect(()=>()=>{O(),M()},[O,M]),{localStream:n,remoteStream:o,isConnected:i,isPublishing:a,error:p,startStreaming:_,stopStreaming:O,joinStream:I,leaveStream:M,localVideoRef:S,remoteVideoRef:w}}function E0({stream:e,className:t}){const[n,r]=f.useState(!1),[o,s]=f.useState(!1),[i,l]=f.useState([50]),[a,c]=f.useState("1080p"),[p,d]=f.useState(!1),[g,S]=f.useState(!0),w=f.useRef(null),m=f.useRef(null),{localStream:x,remoteStream:v,isConnected:h,startStreaming:y,stopStreaming:C,joinStream:b}=IN(e.id);f.useEffect(()=>{e.status==="live"&&b()},[e.id,e.status,b]),f.useEffect(()=>{w.current&&v&&(w.current.srcObject=v)},[v]);const k=()=>{w.current&&(n?w.current.pause():w.current.play(),r(!n))},N=()=>{w.current&&(w.current.muted=!o,s(!o))},P=I=>{l(I),w.current&&(w.current.volume=I[0]/100)},_=()=>{var I;p?document.exitFullscreen&&document.exitFullscreen():(I=m.current)!=null&&I.requestFullscreen&&m.current.requestFullscreen(),d(!p)},O=()=>{S(!0),setTimeout(()=>S(!1),3e3)};return u.jsxs(Be,{className:q("overflow-hidden",t),children:[u.jsx("div",{className:"p-6 border-b border-border",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("h3",{className:"text-lg font-semibold",children:e.title}),u.jsx("p",{className:"text-muted-foreground text-sm",children:e.description})]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(Hn,{variant:e.status==="live"?"default":"secondary",children:[e.status==="live"&&u.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"}),e.status.toUpperCase()]}),u.jsxs("span",{className:"text-sm text-muted-foreground flex items-center",children:[u.jsx($l,{className:"w-4 h-4 mr-1"}),e.viewerCount||0," viewers"]})]})]})}),u.jsxs("div",{ref:m,className:"relative bg-black aspect-video",onMouseMove:O,onMouseLeave:()=>S(!1),children:[e.status==="live"?u.jsx("video",{ref:w,className:"w-full h-full object-cover",autoPlay:!0,playsInline:!0,onPlay:()=>r(!0),onPause:()=>r(!1)}):u.jsx("div",{className:"w-full h-full flex items-center justify-center",children:u.jsxs("div",{className:"text-center text-white",children:[u.jsx("div",{className:"w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4",children:u.jsx(Co,{className:"w-8 h-8"})}),u.jsx("h4",{className:"text-lg font-semibold mb-2",children:e.status==="scheduled"?"Stream Scheduled":"Stream Offline"}),u.jsx("p",{className:"text-white/70",children:e.status==="scheduled"?`Starts ${new Date(e.scheduledAt).toLocaleString()}`:"This stream is currently offline"})]})}),e.status==="live"&&u.jsx("div",{className:q("absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300",g?"opacity-100":"opacity-0"),children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx(we,{variant:"ghost",size:"sm",className:"w-10 h-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-0",onClick:k,children:n?u.jsx(SC,{className:"w-4 h-4"}):u.jsx(Co,{className:"w-4 h-4"})}),u.jsx(we,{variant:"ghost",size:"sm",className:"w-10 h-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-0",onClick:N,children:o?u.jsx(TC,{className:"w-4 h-4"}):u.jsx(jC,{className:"w-4 h-4"})}),u.jsx("div",{className:"flex items-center space-x-2",children:u.jsx(C0,{value:i,onValueChange:P,max:100,step:1,className:"w-20"})}),u.jsx("div",{className:"text-white text-sm font-medium",children:u.jsx("span",{children:"Live"})})]}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(pN,{value:a,onValueChange:c,children:[u.jsx(o0,{className:"w-20 bg-black/50 border-white/30 text-white",children:u.jsx(hN,{})}),u.jsxs(l0,{children:[u.jsx(Jo,{value:"1080p",children:"1080p"}),u.jsx(Jo,{value:"720p",children:"720p"}),u.jsx(Jo,{value:"480p",children:"480p"}),u.jsx(Jo,{value:"auto",children:"Auto"})]})]}),u.jsx(we,{variant:"ghost",size:"sm",className:"w-8 h-8 bg-white/20 hover:bg-white/30 text-white rounded p-0",onClick:_,children:u.jsx(wC,{className:"w-4 h-4"})})]})]})}),e.status==="live"&&u.jsx("div",{className:"absolute top-4 right-4",children:u.jsx(Hn,{variant:h?"default":"destructive",children:h?"Connected":"Connecting..."})})]})]})}const zd=f.forwardRef(({className:e,type:t,...n},r)=>u.jsx("input",{type:t,className:q("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:r,...n}));zd.displayName="Input";function b0({streamId:e}){const[t,n]=f.useState(""),[r,o]=f.useState(0),s=f.useRef(null),i=Vs(),{data:l=[],isLoading:a}=So({queryKey:[`/api/streams/${e}/chat`],enabled:!!e}),{socket:c,isConnected:p}=Fd("/ws",{onMessage:x=>{x.type==="chat_message"?(i.setQueryData([`/api/streams/${e}/chat`],v=>[...v||[],x.message]),g()):x.type==="viewer_count_update"&&o(x.count)}}),d=cl({mutationFn:async x=>{c&&p&&c.send(JSON.stringify({type:"chat_message",streamId:e,userId:1,message:x,messageType:"message"}))},onSuccess:()=>{n("")}}),g=()=>{var x;(x=s.current)==null||x.scrollIntoView({behavior:"smooth"})};f.useEffect(()=>{g()},[l]),f.useEffect(()=>(c&&p&&c.send(JSON.stringify({type:"join_stream",streamId:e,userId:1})),()=>{c&&p&&c.send(JSON.stringify({type:"leave_stream",streamId:e}))}),[c,p,e]);const S=x=>{x.preventDefault(),t.trim()&&d.mutate(t.trim())},w=x=>x.split(" ").map(v=>v[0]).join("").toUpperCase(),m=x=>{const v=["bg-red-500","bg-blue-500","bg-green-500","bg-yellow-500","bg-purple-500","bg-pink-500","bg-indigo-500","bg-teal-500"];return v[x%v.length]};return u.jsxs(Be,{className:"h-96 flex flex-col",children:[u.jsx(wr,{className:"pb-3",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(Sr,{className:"text-lg",children:"Live Chat"}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(Hn,{variant:"secondary",className:"text-xs",children:[u.jsx(fl,{className:"w-3 h-3 mr-1"}),r," participants"]}),u.jsx("div",{className:q("w-2 h-2 rounded-full",p?"bg-green-500":"bg-red-500")})]})]})}),u.jsxs(Ye,{className:"flex-1 flex flex-col p-0",children:[u.jsxs("div",{className:"flex-1 overflow-y-auto p-4 space-y-3",children:[a?u.jsx("div",{className:"flex items-center justify-center h-full",children:u.jsx("div",{className:"animate-spin rounded-full h-6 w-6 border-b-2 border-primary"})}):l.length===0?u.jsx("div",{className:"flex items-center justify-center h-full text-center",children:u.jsxs("div",{children:[u.jsx(fl,{className:"w-12 h-12 mx-auto mb-2 text-muted-foreground"}),u.jsx("p",{className:"text-sm text-muted-foreground",children:"No messages yet"}),u.jsx("p",{className:"text-xs text-muted-foreground",children:"Be the first to say hello!"})]})}):l.map(x=>{var v,h;return u.jsxs("div",{className:"flex items-start space-x-3",children:[u.jsx("div",{className:q("w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",m(x.userId)),children:u.jsx("span",{className:"text-white text-xs font-medium",children:(v=x.user)!=null&&v.username?w(x.user.username):"U"})}),u.jsxs("div",{className:"flex-1 min-w-0",children:[u.jsxs("div",{className:"flex items-center space-x-2 mb-1",children:[u.jsx("span",{className:"text-sm font-medium",children:((h=x.user)==null?void 0:h.username)||`User ${x.userId}`}),u.jsx("span",{className:"text-xs text-muted-foreground",children:new Date(x.timestamp).toLocaleTimeString()}),x.type==="moderator"&&u.jsx(Hn,{variant:"secondary",className:"text-xs",children:"MOD"})]}),u.jsx("p",{className:"text-sm text-foreground break-words",children:x.message})]})]},x.id)}),u.jsx("div",{ref:s})]}),u.jsx("div",{className:"p-4 border-t border-border",children:u.jsxs("form",{onSubmit:S,className:"flex items-center space-x-2",children:[u.jsx(zd,{value:t,onChange:x=>n(x.target.value),placeholder:"Type your message...",className:"flex-1",disabled:!p||d.isPending}),u.jsx(we,{type:"submit",size:"sm",disabled:!t.trim()||!p||d.isPending,children:u.jsx(EC,{className:"w-4 h-4"})})]})})]})]})}var $d="Progress",Ud=100,[LN,ik]=kr($d),[DN,FN]=LN($d),N0=f.forwardRef((e,t)=>{const{__scopeProgress:n,value:r=null,max:o,getValueLabel:s=zN,...i}=e;(o||o===0)&&!Wp(o)&&console.error($N(`${o}`,"Progress"));const l=Wp(o)?o:Ud;r!==null&&!Kp(r,l)&&console.error(UN(`${r}`,"Progress"));const a=Kp(r,l)?r:null,c=xl(a)?s(a,l):void 0;return u.jsx(DN,{scope:n,value:a,max:l,children:u.jsx(ee.div,{"aria-valuemax":l,"aria-valuemin":0,"aria-valuenow":xl(a)?a:void 0,"aria-valuetext":c,role:"progressbar","data-state":j0(a,l),"data-value":a??void 0,"data-max":l,...i,ref:t})})});N0.displayName=$d;var k0="ProgressIndicator",P0=f.forwardRef((e,t)=>{const{__scopeProgress:n,...r}=e,o=FN(k0,n);return u.jsx(ee.div,{"data-state":j0(o.value,o.max),"data-value":o.value??void 0,"data-max":o.max,...r,ref:t})});P0.displayName=k0;function zN(e,t){return`${Math.round(e/t*100)}%`}function j0(e,t){return e==null?"indeterminate":e===t?"complete":"loading"}function xl(e){return typeof e=="number"}function Wp(e){return xl(e)&&!isNaN(e)&&e>0}function Kp(e,t){return xl(e)&&!isNaN(e)&&e<=t&&e>=0}function $N(e,t){return`Invalid prop \`max\` of value \`${e}\` supplied to \`${t}\`. Only numbers greater than 0 are valid max values. Defaulting to \`${Ud}\`.`}function UN(e,t){return`Invalid prop \`value\` of value \`${e}\` supplied to \`${t}\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or ${Ud} if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`}var T0=N0,VN=P0;const ds=f.forwardRef(({className:e,value:t,...n},r)=>u.jsx(T0,{ref:r,className:q("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...n,children:u.jsx(VN,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(t||0)}%)`}})}));ds.displayName=T0.displayName;function R0({streamId:e}){var c;const[t,n]=f.useState(!1),{toast:r}=md(),o=Vs(),{data:s}=So({queryKey:[`/api/streams/${e}`],enabled:!!e}),i=cl({mutationFn:async p=>await Gu("PUT",`/api/streams/${e}`,p),onSuccess:()=>{o.invalidateQueries({queryKey:[`/api/streams/${e}`]}),o.invalidateQueries({queryKey:["/api/streams"]})},onError:p=>{r({title:"Error",description:p.message,variant:"destructive"})}}),l=()=>{(s==null?void 0:s.status)==="live"?(i.mutate({status:"inactive",endedAt:new Date().toISOString()}),r({title:"Stream Stopped",description:"Your stream has been stopped successfully."})):(i.mutate({status:"live",startedAt:new Date().toISOString()}),r({title:"Stream Started",description:"Your stream is now live!"}))},a=()=>{n(!t),i.mutate({isRecording:!t}),r({title:t?"Recording Stopped":"Recording Started",description:t?"Stream recording has been stopped.":"Stream recording has been started."})};return s?u.jsxs(Be,{children:[u.jsx(wr,{children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(Sr,{children:"Stream Controls"}),u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsxs(we,{variant:t?"destructive":"default",size:"sm",onClick:a,disabled:i.isPending,children:[u.jsx(vC,{className:"w-4 h-4 mr-1"}),t?"Stop Recording":"Record"]}),u.jsx(we,{variant:s.status==="live"?"destructive":"default",size:"sm",onClick:l,disabled:i.isPending,children:s.status==="live"?u.jsxs(u.Fragment,{children:[u.jsx(wg,{className:"w-4 h-4 mr-1"}),"Stop Stream"]}):u.jsxs(u.Fragment,{children:[u.jsx(Co,{className:"w-4 h-4 mr-1"}),"Start Stream"]})})]})]})}),u.jsxs(Ye,{children:[u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6",children:[u.jsxs("div",{className:"bg-muted/50 p-4 rounded-lg border",children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm text-muted-foreground",children:"Bitrate"}),u.jsx(_C,{className:"w-4 h-4 text-muted-foreground"})]}),u.jsxs("div",{className:"text-lg font-semibold",children:[s.bitrate||2500," kbps"]}),u.jsx("div",{className:"text-xs text-green-600",children:"Adaptive"})]}),u.jsxs("div",{className:"bg-muted/50 p-4 rounded-lg border",children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm text-muted-foreground",children:"Quality"}),u.jsx(Ed,{className:"w-4 h-4 text-muted-foreground"})]}),u.jsx("div",{className:"text-lg font-semibold",children:s.quality||"720p"}),u.jsx("div",{className:"text-xs text-muted-foreground",children:"60fps"})]}),u.jsxs("div",{className:"bg-muted/50 p-4 rounded-lg border",children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm text-muted-foreground",children:"Protocol"}),u.jsx(Sg,{className:"w-4 h-4 text-muted-foreground"})]}),u.jsx("div",{className:"text-lg font-semibold",children:((c=s.protocol)==null?void 0:c.toUpperCase())||"WEBRTC"}),u.jsx("div",{className:"text-xs text-green-600",children:"P2P Enabled"})]})]}),u.jsxs("div",{className:"space-y-4",children:[u.jsxs("div",{children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm text-muted-foreground",children:"CPU Usage"}),u.jsx("span",{className:"text-sm font-medium",children:"34%"})]}),u.jsx(ds,{value:34,className:"h-2"})]}),u.jsxs("div",{children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm text-muted-foreground",children:"Memory Usage"}),u.jsx("span",{className:"text-sm font-medium",children:"52%"})]}),u.jsx(ds,{value:52,className:"h-2"})]}),u.jsxs("div",{children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm text-muted-foreground",children:"Network Usage"}),u.jsx("span",{className:"text-sm font-medium",children:"145 Mbps"})]}),u.jsx(ds,{value:72,className:"h-2"})]})]}),u.jsxs("div",{className:"mt-6 p-4 bg-muted/30 rounded-lg border",children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsx("span",{className:"text-sm font-medium",children:"Stream Key"}),u.jsx(Hn,{variant:"outline",children:s.streamKey})]}),u.jsx("div",{className:"text-xs text-muted-foreground",children:"Keep this key private and secure"})]})]})]}):null}const _0=f.forwardRef(({className:e,...t},n)=>u.jsx("div",{className:"relative w-full overflow-auto",children:u.jsx("table",{ref:n,className:q("w-full caption-bottom text-sm",e),...t})}));_0.displayName="Table";const O0=f.forwardRef(({className:e,...t},n)=>u.jsx("thead",{ref:n,className:q("[&_tr]:border-b",e),...t}));O0.displayName="TableHeader";const M0=f.forwardRef(({className:e,...t},n)=>u.jsx("tbody",{ref:n,className:q("[&_tr:last-child]:border-0",e),...t}));M0.displayName="TableBody";const BN=f.forwardRef(({className:e,...t},n)=>u.jsx("tfoot",{ref:n,className:q("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t}));BN.displayName="TableFooter";const pc=f.forwardRef(({className:e,...t},n)=>u.jsx("tr",{ref:n,className:q("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));pc.displayName="TableRow";const Zn=f.forwardRef(({className:e,...t},n)=>u.jsx("th",{ref:n,className:q("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));Zn.displayName="TableHead";const Jn=f.forwardRef(({className:e,...t},n)=>u.jsx("td",{ref:n,className:q("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));Jn.displayName="TableCell";const HN=f.forwardRef(({className:e,...t},n)=>u.jsx("caption",{ref:n,className:q("mt-4 text-sm text-muted-foreground",e),...t}));HN.displayName="TableCaption";function WN({streams:e,isLoading:t,onStreamSelect:n}){const[r,o]=f.useState(""),{toast:s}=md(),i=Vs(),l=cl({mutationFn:async w=>await Gu("DELETE",`/api/streams/${w}`),onSuccess:()=>{i.invalidateQueries({queryKey:["/api/streams"]}),s({title:"Stream Deleted",description:"The stream has been deleted successfully."})},onError:w=>{s({title:"Error",description:w.message,variant:"destructive"})}}),a=cl({mutationFn:async({streamId:w,updates:m})=>await Gu("PUT",`/api/streams/${w}`,m),onSuccess:()=>{i.invalidateQueries({queryKey:["/api/streams"]})},onError:w=>{s({title:"Error",description:w.message,variant:"destructive"})}}),c=e.filter(w=>w.title.toLowerCase().includes(r.toLowerCase())||w.streamKey.toLowerCase().includes(r.toLowerCase())),p=w=>{const m=w.status==="live"?"inactive":"live";a.mutate({streamId:w.id,updates:{status:m,[m==="live"?"startedAt":"endedAt"]:new Date().toISOString()}})},d=w=>{window.confirm("Are you sure you want to delete this stream?")&&l.mutate(w)},g=w=>{const m={live:"default",inactive:"secondary",scheduled:"outline",ended:"secondary"};return u.jsxs(Hn,{variant:m[w]||"secondary",children:[w==="live"&&u.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"}),w.charAt(0).toUpperCase()+w.slice(1)]})},S=(w,m)=>{if(!w)return"-";const x=new Date(w),v=m?new Date(m):new Date,h=Math.floor((v.getTime()-x.getTime())/1e3),y=Math.floor(h/3600),C=Math.floor(h%3600/60);return y>0?`${y}h ${C}m`:`${C}m`};return u.jsxs(Be,{children:[u.jsx(wr,{children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx(Sr,{children:"Active Streams"}),u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsxs("div",{className:"relative",children:[u.jsx(CC,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4"}),u.jsx(zd,{placeholder:"Search streams...",value:r,onChange:w=>o(w.target.value),className:"pl-10 w-64"})]}),u.jsxs(we,{variant:"outline",size:"sm",children:[u.jsx(yC,{className:"w-4 h-4 mr-2"}),"Filter"]})]})]})}),u.jsx(Ye,{children:t?u.jsx("div",{className:"flex items-center justify-center h-32",children:u.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):c.length===0?u.jsxs("div",{className:"text-center py-8",children:[u.jsx(cs,{className:"w-16 h-16 mx-auto mb-4 text-muted-foreground"}),u.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No Streams Found"}),u.jsx("p",{className:"text-muted-foreground",children:r?"No streams match your search criteria.":"Create your first stream to get started."})]}):u.jsxs(_0,{children:[u.jsx(O0,{children:u.jsxs(pc,{children:[u.jsx(Zn,{children:"Stream"}),u.jsx(Zn,{children:"Status"}),u.jsx(Zn,{children:"Viewers"}),u.jsx(Zn,{children:"Quality"}),u.jsx(Zn,{children:"Duration"}),u.jsx(Zn,{children:"Actions"})]})}),u.jsx(M0,{children:c.map(w=>u.jsxs(pc,{className:"hover:bg-muted/50",children:[u.jsx(Jn,{children:u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsx("div",{className:"w-12 h-8 bg-muted rounded flex items-center justify-center",children:u.jsx(cs,{className:"w-4 h-4 text-muted-foreground"})}),u.jsxs("div",{children:[u.jsx("div",{className:"font-medium",children:w.title}),u.jsx("div",{className:"text-sm text-muted-foreground",children:w.streamKey})]})]})}),u.jsx(Jn,{children:g(w.status)}),u.jsx(Jn,{className:"text-sm",children:w.viewerCount||0}),u.jsx(Jn,{className:"text-sm",children:w.quality||"720p"}),u.jsx(Jn,{className:"text-sm",children:w.status==="live"?S(w.startedAt):w.status==="scheduled"?`Starts ${new Date(w.scheduledAt).toLocaleDateString()}`:S(w.startedAt,w.endedAt)}),u.jsx(Jn,{children:u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx(we,{variant:"ghost",size:"sm",onClick:()=>n==null?void 0:n(w.id),children:u.jsx($l,{className:"w-4 h-4"})}),u.jsx(ll,{href:`/stream/${w.id}`,children:u.jsx(we,{variant:"ghost",size:"sm",children:u.jsx(kC,{className:"w-4 h-4"})})}),u.jsx(we,{variant:"ghost",size:"sm",onClick:()=>p(w),disabled:a.isPending,children:w.status==="live"?u.jsx(wg,{className:"w-4 h-4"}):u.jsx(Co,{className:"w-4 h-4"})}),u.jsx(we,{variant:"ghost",size:"sm",onClick:()=>d(w.id),disabled:l.isPending,children:u.jsx(PC,{className:"w-4 h-4"})})]})})]},w.id))})]})})]})}function KN(){So({queryKey:["/api/stats"],refetchInterval:3e4});const e=[{label:"Average Latency",value:"2.3s",progress:23,color:"bg-green-500"},{label:"Bandwidth Usage",value:"145 Mbps",progress:72,color:"bg-yellow-500"},{label:"CPU Usage",value:"34%",progress:34,color:"bg-blue-500"}],t=[{name:"REST API",status:"Active",endpoint:"https://api.streamengine.com/v1/",icon:xC,statusColor:"bg-green-500"},{name:"WebSocket",status:"Connected",endpoint:"wss://ws.streamengine.com/v1/",icon:Sg,statusColor:"bg-green-500"},{name:"WebRTC",status:"P2P Ready",endpoint:"12 peer connections active",icon:wp,statusColor:"bg-green-500"}];return u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[u.jsxs(Be,{children:[u.jsx(wr,{children:u.jsxs(Sr,{className:"flex items-center space-x-2",children:[u.jsx(wp,{className:"w-5 h-5"}),u.jsx("span",{children:"Performance Metrics"})]})}),u.jsx(Ye,{className:"space-y-6",children:e.map(n=>u.jsxs("div",{className:"space-y-2",children:[u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsx("span",{className:"text-sm text-muted-foreground",children:n.label}),u.jsx("span",{className:"text-sm font-medium",children:n.value})]}),u.jsx(ds,{value:n.progress,className:"h-2"})]},n.label))})]}),u.jsxs(Be,{children:[u.jsx(wr,{children:u.jsxs(Sr,{className:"flex items-center space-x-2",children:[u.jsx(NC,{className:"w-5 h-5"}),u.jsx("span",{children:"API Integration"})]})}),u.jsx(Ye,{className:"space-y-4",children:t.map(n=>u.jsxs("div",{className:"bg-muted/50 rounded-lg p-4 border",children:[u.jsxs("div",{className:"flex items-center justify-between mb-2",children:[u.jsxs("div",{className:"flex items-center space-x-2",children:[u.jsx(n.icon,{className:"w-4 h-4"}),u.jsx("span",{className:"font-medium",children:n.name})]}),u.jsxs(Hn,{variant:"secondary",className:"text-xs",children:[u.jsx("div",{className:`w-2 h-2 rounded-full mr-1 ${n.statusColor}`}),n.status]})]}),u.jsx("div",{className:"text-xs text-muted-foreground font-mono",children:n.endpoint})]},n.name))})]})]})}function QN(){var l;const[e,t]=f.useState(null);f.useState("");const{data:n,isLoading:r}=So({queryKey:["/api/streams"]}),{data:o}=So({queryKey:["/api/stats"],refetchInterval:3e4});Fd("/ws");const i=((n==null?void 0:n.filter(a=>a.status==="live"))||[])[0];return f.useEffect(()=>{i&&!e&&t(i.id)},[i,e]),u.jsxs("div",{className:"min-h-screen flex bg-background",children:[u.jsxs("nav",{className:"w-64 bg-card border-r border-border flex flex-col",children:[u.jsx("div",{className:"p-6 border-b border-border",children:u.jsxs("div",{className:"flex items-center space-x-3",children:[u.jsx("div",{className:"w-8 h-8 bg-primary rounded-lg flex items-center justify-center",children:u.jsx(cs,{className:"w-5 h-5 text-primary-foreground"})}),u.jsxs("div",{children:[u.jsx("h1",{className:"text-xl font-bold",children:"StreamEngine"}),u.jsx("p",{className:"text-sm text-muted-foreground",children:"Pro"})]})]})}),u.jsxs("div",{className:"flex-1 py-4",children:[u.jsx("div",{className:"px-4 mb-4",children:u.jsx("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wider",children:"Main"})}),u.jsxs("ul",{className:"space-y-1 px-2",children:[u.jsx("li",{children:u.jsxs(ll,{href:"/",className:"flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-primary text-primary-foreground",children:[u.jsx(_a,{className:"w-5 h-5 mr-3"}),"Dashboard"]})}),u.jsx("li",{children:u.jsxs("a",{href:"#",className:"flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground",children:[u.jsx(Ep,{className:"w-5 h-5 mr-3"}),"Live Streams"]})}),u.jsx("li",{children:u.jsxs("a",{href:"#",className:"flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground",children:[u.jsx(Co,{className:"w-5 h-5 mr-3"}),"Recordings"]})}),u.jsx("li",{children:u.jsxs("a",{href:"#",className:"flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground",children:[u.jsx(_a,{className:"w-5 h-5 mr-3"}),"Analytics"]})})]}),u.jsx("div",{className:"px-4 mt-8 mb-4",children:u.jsx("p",{className:"text-xs font-medium text-muted-foreground uppercase tracking-wider",children:"Management"})}),u.jsxs("ul",{className:"space-y-1 px-2",children:[u.jsx("li",{children:u.jsxs("a",{href:"#",className:"flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground",children:[u.jsx(fl,{className:"w-5 h-5 mr-3"}),"Users & Access"]})}),u.jsx("li",{children:u.jsxs("a",{href:"#",className:"flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground",children:[u.jsx(Ed,{className:"w-5 h-5 mr-3"}),"Settings"]})}),u.jsx("li",{children:u.jsxs("a",{href:"#",className:"flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground",children:[u.jsx(gC,{className:"w-5 h-5 mr-3"}),"API Docs"]})})]})]}),u.jsx("div",{className:"p-4 border-t border-border",children:u.jsxs("div",{className:"flex items-center space-x-3 p-3 rounded-lg bg-accent",children:[u.jsx("div",{className:"w-8 h-8 bg-muted rounded-full flex items-center justify-center",children:u.jsx(fl,{className:"w-4 h-4 text-muted-foreground"})}),u.jsxs("div",{className:"flex-1",children:[u.jsx("p",{className:"text-sm font-medium",children:"Admin User"}),u.jsx("p",{className:"text-xs text-muted-foreground",children:"System Administrator"})]})]})})]}),u.jsxs("main",{className:"flex-1 overflow-auto",children:[u.jsxs("header",{className:"bg-card border-b border-border px-8 py-4 flex items-center justify-between",children:[u.jsxs("div",{children:[u.jsx("h2",{className:"text-2xl font-bold",children:"Live Streaming Dashboard"}),u.jsx("p",{className:"text-muted-foreground",children:"Monitor and manage your enterprise streaming infrastructure"})]}),u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsxs(we,{className:"bg-primary hover:bg-primary/90",children:[u.jsx(Cp,{className:"w-4 h-4 mr-2"}),"New Stream"]}),u.jsx(we,{variant:"outline",size:"icon",children:u.jsx(fC,{className:"w-4 h-4"})})]})]}),u.jsxs("div",{className:"p-8",children:[u.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[u.jsx(Be,{children:u.jsxs(Ye,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("div",{className:"w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center",children:u.jsx(Ep,{className:"w-5 h-5 text-green-500"})}),u.jsx("span",{className:"text-xs font-medium text-muted-foreground uppercase",children:"Active Streams"})]}),u.jsx("div",{className:"text-2xl font-bold mb-1",children:(o==null?void 0:o.activeStreams)||0}),u.jsxs("div",{className:"text-sm text-muted-foreground",children:[u.jsx("span",{className:"text-green-500",children:"+2"})," from last hour"]})]})}),u.jsx(Be,{children:u.jsxs(Ye,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("div",{className:"w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center",children:u.jsx($l,{className:"w-5 h-5 text-blue-500"})}),u.jsx("span",{className:"text-xs font-medium text-muted-foreground uppercase",children:"Total Viewers"})]}),u.jsx("div",{className:"text-2xl font-bold mb-1",children:((l=o==null?void 0:o.totalViewers)==null?void 0:l.toLocaleString())||0}),u.jsxs("div",{className:"text-sm text-muted-foreground",children:[u.jsx("span",{className:"text-green-500",children:"+12%"})," from yesterday"]})]})}),u.jsx(Be,{children:u.jsxs(Ye,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("div",{className:"w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center",children:u.jsx(xg,{className:"w-5 h-5 text-yellow-500"})}),u.jsx("span",{className:"text-xs font-medium text-muted-foreground uppercase",children:"Avg Latency"})]}),u.jsx("div",{className:"text-2xl font-bold mb-1",children:(o==null?void 0:o.avgLatency)||"2.3s"}),u.jsxs("div",{className:"text-sm text-muted-foreground",children:[u.jsx("span",{className:"text-green-500",children:"-0.2s"})," improvement"]})]})}),u.jsx(Be,{children:u.jsxs(Ye,{className:"p-6",children:[u.jsxs("div",{className:"flex items-center justify-between mb-4",children:[u.jsx("div",{className:"w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center",children:u.jsx(bC,{className:"w-5 h-5 text-red-500"})}),u.jsx("span",{className:"text-xs font-medium text-muted-foreground uppercase",children:"Server Load"})]}),u.jsx("div",{className:"text-2xl font-bold mb-1",children:(o==null?void 0:o.serverLoad)||"67%"}),u.jsx("div",{className:"text-sm text-muted-foreground",children:u.jsx("span",{className:"text-muted-foreground",children:"Normal range"})})]})})]}),u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[u.jsxs("div",{className:"lg:col-span-2",children:[i?u.jsx(E0,{stream:i,className:"mb-6"}):u.jsx(Be,{className:"mb-6",children:u.jsxs(Ye,{className:"p-8 text-center",children:[u.jsx(cs,{className:"w-16 h-16 mx-auto mb-4 text-muted-foreground"}),u.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No Active Streams"}),u.jsx("p",{className:"text-muted-foreground mb-4",children:"Start streaming to see your content here"}),u.jsxs(we,{children:[u.jsx(Cp,{className:"w-4 h-4 mr-2"}),"Create Stream"]})]})}),e&&u.jsx(R0,{streamId:e})]}),u.jsxs("div",{className:"space-y-6",children:[e&&u.jsx(b0,{streamId:e}),u.jsxs(Be,{children:[u.jsx(wr,{children:u.jsx(Sr,{children:"Quick Actions"})}),u.jsxs(Ye,{className:"space-y-3",children:[u.jsxs(we,{className:"w-full",size:"lg",children:[u.jsx(cs,{className:"w-4 h-4 mr-2"}),"Start New Stream"]}),u.jsxs(we,{variant:"outline",className:"w-full",size:"lg",children:[u.jsx(Co,{className:"w-4 h-4 mr-2"}),"Upload Recording"]}),u.jsxs(we,{variant:"outline",className:"w-full",size:"lg",children:[u.jsx(_a,{className:"w-4 h-4 mr-2"}),"Export Analytics"]})]})]})]})]}),u.jsx("div",{className:"mt-8",children:u.jsx(WN,{streams:n||[],isLoading:r,onStreamSelect:t})}),u.jsx("div",{className:"mt-8",children:u.jsx(KN,{})})]})]})]})}function GN(){const{id:e}=Pv(),t=parseInt(e||"0"),{data:n,isLoading:r}=So({queryKey:[`/api/streams/${t}`],enabled:!!t});return r?u.jsx("div",{className:"min-h-screen flex items-center justify-center",children:u.jsxs("div",{className:"text-center",children:[u.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),u.jsx("p",{className:"text-muted-foreground",children:"Loading stream..."})]})}):n?u.jsxs("div",{className:"min-h-screen bg-background",children:[u.jsx("header",{className:"bg-card border-b border-border px-8 py-4",children:u.jsxs("div",{className:"flex items-center justify-between",children:[u.jsxs("div",{className:"flex items-center space-x-4",children:[u.jsx(ll,{href:"/",children:u.jsxs(we,{variant:"ghost",size:"sm",children:[u.jsx(Sp,{className:"w-4 h-4 mr-2"}),"Back"]})}),u.jsxs("div",{children:[u.jsx("h1",{className:"text-2xl font-bold",children:n.title}),u.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[u.jsxs(Hn,{variant:n.status==="live"?"default":"secondary",children:[n.status==="live"&&u.jsx("div",{className:"w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse"}),n.status.toUpperCase()]}),u.jsxs("span",{className:"text-sm text-muted-foreground flex items-center",children:[u.jsx($l,{className:"w-4 h-4 mr-1"}),n.viewerCount||0," viewers"]}),u.jsxs("span",{className:"text-sm text-muted-foreground flex items-center",children:[u.jsx(xg,{className:"w-4 h-4 mr-1"}),n.status==="live"?"Live now":"Scheduled"]})]})]})]}),u.jsx("div",{className:"flex items-center space-x-2",children:u.jsxs(we,{variant:"outline",size:"sm",children:[u.jsx(Ed,{className:"w-4 h-4 mr-2"}),"Settings"]})})]})}),u.jsx("div",{className:"p-8",children:u.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-8",children:[u.jsxs("div",{className:"lg:col-span-2 space-y-6",children:[u.jsx(E0,{stream:n}),u.jsx(R0,{streamId:t})]}),u.jsx("div",{children:u.jsx(b0,{streamId:t})})]})})]}):u.jsx("div",{className:"min-h-screen flex items-center justify-center",children:u.jsx(Be,{className:"w-full max-w-md",children:u.jsxs(Ye,{className:"pt-6 text-center",children:[u.jsx("h2",{className:"text-2xl font-bold mb-2",children:"Stream Not Found"}),u.jsx("p",{className:"text-muted-foreground mb-4",children:"The stream you're looking for doesn't exist or has been removed."}),u.jsx(ll,{href:"/",children:u.jsxs(we,{children:[u.jsx(Sp,{className:"w-4 h-4 mr-2"}),"Back to Dashboard"]})})]})})})}function qN(){return u.jsx("div",{className:"min-h-screen w-full flex items-center justify-center bg-gray-50",children:u.jsx(Be,{className:"w-full max-w-md mx-4",children:u.jsxs(Ye,{className:"pt-6",children:[u.jsxs("div",{className:"flex mb-4 gap-2",children:[u.jsx(mC,{className:"h-8 w-8 text-red-500"}),u.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"404 Page Not Found"})]}),u.jsx("p",{className:"mt-4 text-sm text-gray-600",children:"Did you forget to add the page to the router?"})]})})})}function YN(){return u.jsxs(L1,{children:[u.jsx(ka,{path:"/",component:QN}),u.jsx(ka,{path:"/stream/:id",component:GN}),u.jsx(ka,{component:qN})]})}function XN(){return u.jsx(nS,{client:mS,children:u.jsxs(z2,{children:[u.jsx(pE,{}),u.jsx(YN,{})]})})}gv(document.getElementById("root")).render(u.jsx(XN,{}));

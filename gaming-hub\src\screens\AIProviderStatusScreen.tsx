import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  RefreshControl,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { ollamaSetupService } from '../services/ollamaSetupService';
import { apiManager } from '../services/apiManager';
import { Environment } from '../config/environment';
import { theme, colors } from '../theme';

interface ProviderStatus {
  name: string;
  displayName: string;
  status: 'connected' | 'disconnected' | 'testing' | 'error';
  responseTime?: number;
  error?: string;
  features: string[];
  priority: number;
}

export default function AIProviderStatusScreen() {
  const navigation = useNavigation();
  const [providers, setProviders] = useState<ProviderStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    checkAllProviders();
  }, []);

  const checkAllProviders = async () => {
    setLoading(true);
    
    const providerChecks = [
      checkOllamaProvider(),
      checkDeepSeekProvider(),
      checkOpenRouterProvider(),
      checkAnthropicProvider(),
      checkElevenLabsProvider(),
    ];

    try {
      const results = await Promise.all(providerChecks);
      setProviders(results.sort((a, b) => a.priority - b.priority));
    } catch (error) {
      console.error('Error checking providers:', error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await checkAllProviders();
    setRefreshing(false);
  };

  const checkOllamaProvider = async (): Promise<ProviderStatus> => {
    const provider: ProviderStatus = {
      name: 'ollama',
      displayName: 'Ollama (Local AI)',
      status: 'testing',
      features: ['Gaming Assistant', 'Code Generation', 'Local Processing'],
      priority: 1,
    };

    try {
      const startTime = Date.now();
      const isConnected = await ollamaSetupService.checkOllamaConnection();
      const responseTime = Date.now() - startTime;

      if (isConnected) {
        // Test with a simple model if available
        const installedModels = await ollamaSetupService.getInstalledModels();
        if (installedModels.length > 0) {
          const testResult = await ollamaSetupService.testModel(installedModels[0]);
          provider.status = testResult.success ? 'connected' : 'error';
          provider.responseTime = testResult.responseTime;
          provider.error = testResult.error;
        } else {
          provider.status = 'disconnected';
          provider.error = 'No models installed';
        }
      } else {
        provider.status = 'disconnected';
        provider.error = 'Ollama service not running';
      }
    } catch (error: any) {
      provider.status = 'error';
      provider.error = error.message;
    }

    return provider;
  };

  const checkDeepSeekProvider = async (): Promise<ProviderStatus> => {
    const provider: ProviderStatus = {
      name: 'deepseek',
      displayName: 'DeepSeek AI',
      status: 'testing',
      features: ['Code Generation', 'Technical Assistance', 'Game Development'],
      priority: 2,
    };

    if (!Environment.DEEPSEEK.API_KEY) {
      provider.status = 'disconnected';
      provider.error = 'API key not configured';
      return provider;
    }

    try {
      const startTime = Date.now();
      const response = await apiManager.chatWithDeepSeek([
        { role: 'user', content: 'Hello, respond with just "OK"' }
      ]);
      const responseTime = Date.now() - startTime;

      provider.status = response.success ? 'connected' : 'error';
      provider.responseTime = responseTime;
      provider.error = response.error;
    } catch (error: any) {
      provider.status = 'error';
      provider.error = error.message;
    }

    return provider;
  };

  const checkOpenRouterProvider = async (): Promise<ProviderStatus> => {
    const provider: ProviderStatus = {
      name: 'openrouter',
      displayName: 'OpenRouter',
      status: 'testing',
      features: ['Multiple Models', 'Reliable Fallback', 'High Availability'],
      priority: 3,
    };

    if (!Environment.OPENROUTER.API_KEY) {
      provider.status = 'disconnected';
      provider.error = 'API key not configured';
      return provider;
    }

    try {
      const startTime = Date.now();
      const response = await apiManager.chatWithAI([
        { role: 'user', content: 'Hello, respond with just "OK"' }
      ]);
      const responseTime = Date.now() - startTime;

      provider.status = response.success ? 'connected' : 'error';
      provider.responseTime = responseTime;
      provider.error = response.error;
    } catch (error: any) {
      provider.status = 'error';
      provider.error = error.message;
    }

    return provider;
  };

  const checkAnthropicProvider = async (): Promise<ProviderStatus> => {
    const provider: ProviderStatus = {
      name: 'anthropic',
      displayName: 'Anthropic Claude',
      status: 'testing',
      features: ['High Quality Responses', 'Safety Focused', 'Advanced Reasoning'],
      priority: 4,
    };

    if (!Environment.ANTHROPIC.API_KEY) {
      provider.status = 'disconnected';
      provider.error = 'API key not configured';
      return provider;
    }

    try {
      const startTime = Date.now();
      const response = await apiManager.chatWithClaude([
        { role: 'user', content: 'Hello, respond with just "OK"' }
      ]);
      const responseTime = Date.now() - startTime;

      provider.status = response.success ? 'connected' : 'error';
      provider.responseTime = responseTime;
      provider.error = response.error;
    } catch (error: any) {
      provider.status = 'error';
      provider.error = error.message;
    }

    return provider;
  };

  const checkElevenLabsProvider = async (): Promise<ProviderStatus> => {
    const provider: ProviderStatus = {
      name: 'elevenlabs',
      displayName: 'ElevenLabs Voice',
      status: 'testing',
      features: ['Text-to-Speech', 'Multiple Voices', 'Natural Audio'],
      priority: 5,
    };

    if (!Environment.ELEVENLABS.API_KEY) {
      provider.status = 'disconnected';
      provider.error = 'API key not configured';
      return provider;
    }

    try {
      // Simple API key validation (without generating audio)
      const startTime = Date.now();
      // In a real implementation, you might make a simple API call to validate the key
      const responseTime = Date.now() - startTime;

      provider.status = 'connected';
      provider.responseTime = responseTime;
    } catch (error: any) {
      provider.status = 'error';
      provider.error = error.message;
    }

    return provider;
  };

  const getStatusIcon = (status: ProviderStatus['status']) => {
    switch (status) {
      case 'connected':
        return { name: 'checkmark-circle', color: colors.success };
      case 'disconnected':
        return { name: 'close-circle', color: colors.error };
      case 'testing':
        return { name: 'time', color: colors.warning };
      case 'error':
        return { name: 'warning', color: colors.error };
      default:
        return { name: 'help-circle', color: colors.textSecondary };
    }
  };

  const renderProvider = (provider: ProviderStatus) => {
    const statusIcon = getStatusIcon(provider.status);

    return (
      <View key={provider.name} style={styles.providerCard}>
        <View style={styles.providerHeader}>
          <View style={styles.providerInfo}>
            <Text style={styles.providerName}>{provider.displayName}</Text>
            <Text style={styles.providerPriority}>Priority: {provider.priority}</Text>
          </View>
          
          <View style={styles.statusContainer}>
            <Ionicons 
              name={statusIcon.name as any} 
              size={24} 
              color={statusIcon.color} 
            />
            <Text style={[styles.statusText, { color: statusIcon.color }]}>
              {provider.status.charAt(0).toUpperCase() + provider.status.slice(1)}
            </Text>
          </View>
        </View>

        {provider.responseTime && (
          <Text style={styles.responseTime}>
            Response time: {provider.responseTime}ms
          </Text>
        )}

        {provider.error && (
          <Text style={styles.errorText}>Error: {provider.error}</Text>
        )}

        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>Features:</Text>
          {provider.features.map((feature, index) => (
            <Text key={index} style={styles.featureItem}>• {feature}</Text>
          ))}
        </View>

        {provider.name === 'ollama' && (
          <TouchableOpacity 
            style={styles.setupButton}
            onPress={() => navigation.navigate('OllamaSetup' as never)}
          >
            <Text style={styles.setupButtonText}>Configure Ollama</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Checking AI providers...</Text>
      </View>
    );
  }

  const connectedProviders = providers.filter(p => p.status === 'connected').length;
  const totalProviders = providers.length;

  return (
    <ScrollView 
      style={styles.container} 
      showsVerticalScrollIndicator={false}
      refreshControl={
        <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
      }
    >
      {/* Header */}
      <LinearGradient
        colors={colors.gradientPrimary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Ionicons name="cloud" size={32} color={colors.text} />
            <Text style={styles.headerTitle}>AI Provider Status</Text>
            <Text style={styles.headerSubtitle}>
              {connectedProviders}/{totalProviders} providers connected
            </Text>
          </View>
        </View>
      </LinearGradient>

      {/* Status Overview */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>System Status</Text>
        <View style={styles.overviewCard}>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Connected Providers</Text>
            <Text style={[styles.overviewValue, { color: colors.success }]}>
              {connectedProviders}
            </Text>
          </View>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>Total Providers</Text>
            <Text style={styles.overviewValue}>{totalProviders}</Text>
          </View>
          <View style={styles.overviewItem}>
            <Text style={styles.overviewLabel}>System Health</Text>
            <Text style={[
              styles.overviewValue, 
              { color: connectedProviders > 0 ? colors.success : colors.error }
            ]}>
              {connectedProviders > 0 ? 'Healthy' : 'Degraded'}
            </Text>
          </View>
        </View>
      </View>

      {/* Providers List */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>AI Providers</Text>
        {providers.map(renderProvider)}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginTop: theme.spacing.md,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: theme.spacing.xs,
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    ...theme.typography.textStyles.h2,
    color: colors.text,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  headerSubtitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    opacity: 0.9,
    textAlign: 'center',
  },
  section: {
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginBottom: theme.spacing.md,
  },
  overviewCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    flexDirection: 'row',
    justifyContent: 'space-around',
    ...theme.shadows.sm,
  },
  overviewItem: {
    alignItems: 'center',
  },
  overviewLabel: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  overviewValue: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
  },
  providerCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  providerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  providerInfo: {
    flex: 1,
  },
  providerName: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginBottom: theme.spacing.xs,
  },
  providerPriority: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusText: {
    ...theme.typography.textStyles.caption,
    marginLeft: theme.spacing.xs,
    fontWeight: '600',
  },
  responseTime: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  errorText: {
    ...theme.typography.textStyles.small,
    color: colors.error,
    marginBottom: theme.spacing.sm,
  },
  featuresContainer: {
    marginTop: theme.spacing.sm,
  },
  featuresTitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  featureItem: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    marginBottom: 2,
  },
  setupButton: {
    backgroundColor: colors.primary,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    marginTop: theme.spacing.md,
  },
  setupButtonText: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
    fontWeight: '600',
  },
});

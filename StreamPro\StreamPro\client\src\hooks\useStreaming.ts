import { useState, useCallback } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useWebRTC } from './useWebRTC';
import { useToast } from './use-toast';

interface UseStreamingOptions {
  streamId?: number;
  onStreamStart?: () => void;
  onStreamStop?: () => void;
  onError?: (error: string) => void;
}

export function useStreaming(options: UseStreamingOptions = {}) {
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamId, setStreamId] = useState<number | null>(options.streamId || null);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { 
    startStreaming: startWebRTC, 
    stopStreaming: stopWebRTC,
    isConnected,
    localStream,
    error: webrtcError 
  } = useWebRTC(streamId || 0, { isPublisher: true });
  
  const createStreamMutation = useMutation({
    mutationFn: async (streamData: any) => {
      const response = await apiRequest('POST', '/api/streams', streamData);
      return response.json();
    },
    onSuccess: (stream) => {
      setStreamId(stream.id);
      queryClient.invalidateQueries({ queryKey: ['/api/streams'] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to create stream',
        variant: 'destructive',
      });
      options.onError?.(error.message);
    },
  });
  
  const updateStreamMutation = useMutation({
    mutationFn: async ({ streamId, updates }: { streamId: number; updates: any }) => {
      const response = await apiRequest('PUT', `/api/streams/${streamId}`, updates);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/streams'] });
    },
    onError: (error) => {
      toast({
        title: 'Error',
        description: 'Failed to update stream',
        variant: 'destructive',
      });
      options.onError?.(error.message);
    },
  });
  
  const startStream = useCallback(async (streamData?: any) => {
    try {
      let currentStreamId = streamId;
      
      // Create stream if it doesn't exist
      if (!currentStreamId) {
        const stream = await createStreamMutation.mutateAsync(streamData);
        currentStreamId = stream.id;
      }
      
      // Start WebRTC streaming
      await startWebRTC();
      
      // Update stream status to live
      await updateStreamMutation.mutateAsync({
        streamId: currentStreamId,
        updates: { 
          status: 'live',
          startedAt: new Date().toISOString()
        }
      });
      
      setIsStreaming(true);
      options.onStreamStart?.();
      
      toast({
        title: 'Stream Started',
        description: 'Your stream is now live!',
      });
      
    } catch (error) {
      console.error('Failed to start stream:', error);
      options.onError?.(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [streamId, startWebRTC, createStreamMutation, updateStreamMutation, options, toast]);
  
  const stopStream = useCallback(async () => {
    try {
      if (streamId) {
        await updateStreamMutation.mutateAsync({
          streamId,
          updates: { 
            status: 'ended',
            endedAt: new Date().toISOString()
          }
        });
      }
      
      stopWebRTC();
      setIsStreaming(false);
      options.onStreamStop?.();
      
      toast({
        title: 'Stream Stopped',
        description: 'Your stream has been stopped.',
      });
      
    } catch (error) {
      console.error('Failed to stop stream:', error);
      options.onError?.(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [streamId, stopWebRTC, updateStreamMutation, options, toast]);
  
  const pauseStream = useCallback(async () => {
    try {
      if (streamId) {
        await updateStreamMutation.mutateAsync({
          streamId,
          updates: { status: 'paused' }
        });
      }
      
      // Pause local stream tracks
      if (localStream) {
        localStream.getTracks().forEach(track => {
          track.enabled = false;
        });
      }
      
      toast({
        title: 'Stream Paused',
        description: 'Your stream has been paused.',
      });
      
    } catch (error) {
      console.error('Failed to pause stream:', error);
      options.onError?.(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [streamId, localStream, updateStreamMutation, options, toast]);
  
  const resumeStream = useCallback(async () => {
    try {
      if (streamId) {
        await updateStreamMutation.mutateAsync({
          streamId,
          updates: { status: 'live' }
        });
      }
      
      // Resume local stream tracks
      if (localStream) {
        localStream.getTracks().forEach(track => {
          track.enabled = true;
        });
      }
      
      toast({
        title: 'Stream Resumed',
        description: 'Your stream has been resumed.',
      });
      
    } catch (error) {
      console.error('Failed to resume stream:', error);
      options.onError?.(error instanceof Error ? error.message : 'Unknown error');
    }
  }, [streamId, localStream, updateStreamMutation, options, toast]);
  
  return {
    isStreaming,
    streamId,
    isConnected,
    localStream,
    error: webrtcError,
    startStream,
    stopStream,
    pauseStream,
    resumeStream,
    isCreating: createStreamMutation.isPending,
    isUpdating: updateStreamMutation.isPending,
  };
}

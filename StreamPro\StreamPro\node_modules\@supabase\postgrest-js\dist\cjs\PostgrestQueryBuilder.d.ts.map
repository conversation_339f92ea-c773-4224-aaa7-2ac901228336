{"version": 3, "file": "PostgrestQueryBuilder.d.ts", "sourceRoot": "", "sources": ["../../src/PostgrestQueryBuilder.ts"], "names": [], "mappings": "AACA,OAAO,sBAAsB,MAAM,0BAA0B,CAAA;AAC7D,OAAO,EAAE,SAAS,EAAE,MAAM,8BAA8B,CAAA;AACxD,OAAO,EAAE,KAAK,EAAE,aAAa,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,SAAS,CAAA;AAEzE,MAAM,CAAC,OAAO,OAAO,qBAAqB,CACxC,MAAM,SAAS,aAAa,EAC5B,QAAQ,SAAS,YAAY,GAAG,WAAW,EAC3C,YAAY,GAAG,OAAO,EACtB,aAAa,GAAG,QAAQ,SAAS;IAAE,aAAa,EAAE,MAAM,CAAC,CAAA;CAAE,GAAG,CAAC,GAAG,OAAO;IAEzE,GAAG,EAAE,GAAG,CAAA;IACR,OAAO,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;IAC/B,MAAM,CAAC,EAAE,MAAM,CAAA;IACf,MAAM,CAAC,EAAE,WAAW,CAAA;IACpB,KAAK,CAAC,EAAE,KAAK,CAAA;gBAGX,GAAG,EAAE,GAAG,EACR,EACE,OAAY,EACZ,MAAM,EACN,KAAK,GACN,EAAE;QACD,OAAO,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAChC,MAAM,CAAC,EAAE,MAAM,CAAA;QACf,KAAK,CAAC,EAAE,KAAK,CAAA;KACd;IAQH;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CACJ,KAAK,SAAS,MAAM,GAAG,GAAG,EAC1B,SAAS,GAAG,SAAS,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,YAAY,EAAE,aAAa,EAAE,KAAK,CAAC,EAElF,OAAO,CAAC,EAAE,KAAK,EACf,EACE,IAAY,EACZ,KAAK,GACN,GAAE;QACD,IAAI,CAAC,EAAE,OAAO,CAAA;QACd,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KACrC,GACL,sBAAsB,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,SAAS,EAAE,EAAE,YAAY,EAAE,aAAa,CAAC;IAgC5F,MAAM,CAAC,GAAG,SAAS,QAAQ,SAAS;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,EAClF,MAAM,EAAE,GAAG,EACX,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KAC1C,GACA,sBAAsB,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,CAAC;IACrF,MAAM,CAAC,GAAG,SAAS,QAAQ,SAAS;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,EAClF,MAAM,EAAE,GAAG,EAAE,EACb,OAAO,CAAC,EAAE;QACR,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;QACzC,aAAa,CAAC,EAAE,OAAO,CAAA;KACxB,GACA,sBAAsB,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,CAAC;IAuErF,MAAM,CAAC,GAAG,SAAS,QAAQ,SAAS;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,EAClF,MAAM,EAAE,GAAG,EACX,OAAO,CAAC,EAAE;QACR,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,gBAAgB,CAAC,EAAE,OAAO,CAAA;QAC1B,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KAC1C,GACA,sBAAsB,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,CAAC;IACrF,MAAM,CAAC,GAAG,SAAS,QAAQ,SAAS;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,EAClF,MAAM,EAAE,GAAG,EAAE,EACb,OAAO,CAAC,EAAE;QACR,UAAU,CAAC,EAAE,MAAM,CAAA;QACnB,gBAAgB,CAAC,EAAE,OAAO,CAAA;QAC1B,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;QACzC,aAAa,CAAC,EAAE,OAAO,CAAA;KACxB,GACA,sBAAsB,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,CAAC;IAwFrF;;;;;;;;;;;;;;;;;;;;OAoBG;IACH,MAAM,CAAC,GAAG,SAAS,QAAQ,SAAS;QAAE,MAAM,EAAE,OAAO,CAAA;KAAE,GAAG,QAAQ,CAAC,QAAQ,CAAC,GAAG,KAAK,EAClF,MAAM,EAAE,GAAG,EACX,EACE,KAAK,GACN,GAAE;QACD,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KACrC,GACL,sBAAsB,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,CAAC;IAsBrF;;;;;;;;;;;;;;;;;;OAkBG;IACH,MAAM,CAAC,EACL,KAAK,GACN,GAAE;QACD,KAAK,CAAC,EAAE,OAAO,GAAG,SAAS,GAAG,WAAW,CAAA;KACrC,GAAG,sBAAsB,CAAC,MAAM,EAAE,QAAQ,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,CAAC;CAoB5F"}
import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';
import { BottomTabNavigationProp } from '@react-navigation/bottom-tabs';

import { MainTabParamList, Game, User } from '../types';
import { theme, colors } from '../theme';
import { supabase } from '../config/supabase';

type HomeScreenNavigationProp = BottomTabNavigationProp<MainTabParamList>;

const { width } = Dimensions.get('window');

export default function HomeScreen() {
  const navigation = useNavigation<HomeScreenNavigationProp>();
  const [user, setUser] = useState<User | null>(null);
  const [featuredGames, setFeaturedGames] = useState<Game[]>([]);
  const [recentGames, setRecentGames] = useState<Game[]>([]);

  useEffect(() => {
    loadUserData();
    loadFeaturedGames();
    loadRecentGames();
  }, []);

  const loadUserData = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // Load user profile data
        const { data: profile } = await supabase
          .from('users')
          .select('*')
          .eq('id', user.id)
          .single();
        
        if (profile) {
          setUser(profile);
        }
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const loadFeaturedGames = async () => {
    try {
      const { data: games } = await supabase
        .from('games')
        .select('*')
        .eq('is_active', true)
        .order('play_count', { ascending: false })
        .limit(5);
      
      if (games) {
        setFeaturedGames(games);
      }
    } catch (error) {
      console.error('Error loading featured games:', error);
      // Mock data for development
      setFeaturedGames([
        {
          id: '1',
          title: 'Clicker Master',
          description: 'Click your way to victory!',
          thumbnail_url: 'https://via.placeholder.com/300x200',
          game_url: 'https://example.com/clicker-game',
          category: 'Casual',
          difficulty: 'easy',
          is_active: true,
          created_at: new Date().toISOString(),
          play_count: 1250,
          average_score: 850,
        },
        {
          id: '2',
          title: 'Puzzle Quest',
          description: 'Solve challenging puzzles',
          thumbnail_url: 'https://via.placeholder.com/300x200',
          game_url: 'https://example.com/puzzle-game',
          category: 'Puzzle',
          difficulty: 'medium',
          is_active: true,
          created_at: new Date().toISOString(),
          play_count: 890,
          average_score: 720,
        },
      ]);
    }
  };

  const loadRecentGames = async () => {
    try {
      if (user) {
        const { data: sessions } = await supabase
          .from('game_sessions')
          .select(`
            *,
            games (*)
          `)
          .eq('user_id', user.id)
          .order('completed_at', { ascending: false })
          .limit(3);
        
        if (sessions) {
          setRecentGames(sessions.map((session: any) => session.games));
        }
      }
    } catch (error) {
      console.error('Error loading recent games:', error);
    }
  };

  const playGame = (game: Game) => {
    navigation.navigate('Game', {
      gameId: game.id,
      gameUrl: game.game_url,
      gameTitle: game.title,
    });
  };

  const renderGameCard = (game: Game, index: number) => (
    <TouchableOpacity
      key={game.id}
      style={styles.gameCard}
      onPress={() => playGame(game)}
    >
      <Image source={{ uri: game.thumbnail_url }} style={styles.gameImage} />
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.8)']}
        style={styles.gameOverlay}
      >
        <View style={styles.gameInfo}>
          <Text style={styles.gameTitle}>{game.title}</Text>
          <Text style={styles.gameDescription}>{game.description}</Text>
          <View style={styles.gameStats}>
            <View style={styles.statItem}>
              <Ionicons name="play" size={12} color={colors.textSecondary} />
              <Text style={styles.statText}>{game.play_count}</Text>
            </View>
            <View style={styles.statItem}>
              <Ionicons name="star" size={12} color={colors.gold} />
              <Text style={styles.statText}>{game.average_score}</Text>
            </View>
          </View>
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <LinearGradient
        colors={colors.gradientDark}
        style={styles.header}
      >
        <View style={styles.welcomeSection}>
          <Text style={styles.welcomeText}>
            Welcome back{user ? `, ${user.username}` : ''}!
          </Text>
          <Text style={styles.subtitleText}>Ready to play some games?</Text>
        </View>
        
        {user && (
          <View style={styles.statsContainer}>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{user.total_score}</Text>
              <Text style={styles.statLabel}>Total Score</Text>
            </View>
            <View style={styles.statCard}>
              <Text style={styles.statNumber}>{user.games_played}</Text>
              <Text style={styles.statLabel}>Games Played</Text>
            </View>
          </View>
        )}
      </LinearGradient>

      {/* Quick Actions */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        <View style={styles.quickActionsContainer}>
          <TouchableOpacity
            style={styles.quickActionCard}
            onPress={() => navigation.navigate('GameDevelopment' as never)}
          >
            <LinearGradient
              colors={[colors.info, colors.primary]}
              style={styles.quickActionGradient}
            >
              <Ionicons name="code-slash" size={24} color={colors.text} />
              <Text style={styles.quickActionTitle}>Create Games</Text>
              <Text style={styles.quickActionSubtitle}>Build with AI</Text>
            </LinearGradient>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.quickActionCard}
            onPress={() => navigation.navigate('AIProviderStatus' as never)}
          >
            <LinearGradient
              colors={[colors.warning, colors.secondary]}
              style={styles.quickActionGradient}
            >
              <Ionicons name="cloud" size={24} color={colors.text} />
              <Text style={styles.quickActionTitle}>AI Status</Text>
              <Text style={styles.quickActionSubtitle}>Check providers</Text>
            </LinearGradient>
          </TouchableOpacity>
        </View>
      </View>

      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Featured Games</Text>
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.gamesScroll}
        >
          {featuredGames.map(renderGameCard)}
        </ScrollView>
      </View>

      {recentGames.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Continue Playing</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            style={styles.gamesScroll}
          >
            {recentGames.map(renderGameCard)}
          </ScrollView>
        </View>
      )}

      <View style={styles.section}>
        <TouchableOpacity 
          style={styles.exploreButton}
          onPress={() => navigation.navigate('Games')}
        >
          <LinearGradient
            colors={colors.gradientPrimary}
            style={styles.exploreGradient}
          >
            <Ionicons name="game-controller" size={24} color={colors.text} />
            <Text style={styles.exploreText}>Explore All Games</Text>
            <Ionicons name="arrow-forward" size={20} color={colors.text} />
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    paddingTop: theme.spacing.xl,
  },
  welcomeSection: {
    marginBottom: theme.spacing.lg,
  },
  welcomeText: {
    ...theme.typography.textStyles.h2,
    color: colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitleText: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    backgroundColor: colors.cardOverlay,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  statNumber: {
    ...theme.typography.textStyles.h3,
    color: colors.primary,
    marginBottom: theme.spacing.xs,
  },
  statLabel: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
  },
  section: {
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginBottom: theme.spacing.md,
  },
  gamesScroll: {
    marginHorizontal: -theme.spacing.lg,
    paddingHorizontal: theme.spacing.lg,
  },
  gameCard: {
    width: width * 0.7,
    height: 200,
    marginRight: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  gameImage: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  gameOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '60%',
    justifyContent: 'flex-end',
  },
  gameInfo: {
    padding: theme.spacing.md,
  },
  gameTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginBottom: theme.spacing.xs,
  },
  gameDescription: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  gameStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statText: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  exploreButton: {
    marginTop: theme.spacing.md,
  },
  exploreGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
  },
  exploreText: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginHorizontal: theme.spacing.md,
  },
  quickActionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: theme.spacing.sm,
  },
  quickActionCard: {
    flex: 1,
    marginHorizontal: theme.spacing.xs,
  },
  quickActionGradient: {
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  quickActionTitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
    marginTop: theme.spacing.sm,
    textAlign: 'center',
  },
  quickActionSubtitle: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
    opacity: 0.8,
    marginTop: theme.spacing.xs,
    textAlign: 'center',
  },
});

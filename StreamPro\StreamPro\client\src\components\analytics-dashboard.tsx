import { useQuery } from "@tanstack/react-query";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { 
  Activity, 
  Wifi, 
  Server, 
  Zap,
  Globe,
  Shield,
  CheckCircle
} from "lucide-react";

export function AnalyticsDashboard() {
  const { data: stats } = useQuery({
    queryKey: ['/api/stats'],
    refetchInterval: 30000,
  });
  
  const performanceMetrics = [
    {
      label: "Average Latency",
      value: "2.3s",
      progress: 23,
      color: "bg-green-500"
    },
    {
      label: "Bandwidth Usage",
      value: "145 Mbps",
      progress: 72,
      color: "bg-yellow-500"
    },
    {
      label: "CPU Usage",
      value: "34%",
      progress: 34,
      color: "bg-blue-500"
    }
  ];
  
  const integrationServices = [
    {
      name: "REST API",
      status: "Active",
      endpoint: "https://api.streamengine.com/v1/",
      icon: Globe,
      statusColor: "bg-green-500"
    },
    {
      name: "WebSocket",
      status: "Connected",
      endpoint: "wss://ws.streamengine.com/v1/",
      icon: Wifi,
      statusColor: "bg-green-500"
    },
    {
      name: "WebRTC",
      status: "P2P Ready",
      endpoint: "12 peer connections active",
      icon: Activity,
      statusColor: "bg-green-500"
    }
  ];
  
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Activity className="w-5 h-5" />
            <span>Performance Metrics</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {performanceMetrics.map((metric) => (
            <div key={metric.label} className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">{metric.label}</span>
                <span className="text-sm font-medium">{metric.value}</span>
              </div>
              <Progress value={metric.progress} className="h-2" />
            </div>
          ))}
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Shield className="w-5 h-5" />
            <span>API Integration</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {integrationServices.map((service) => (
            <div key={service.name} className="bg-muted/50 rounded-lg p-4 border">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <service.icon className="w-4 h-4" />
                  <span className="font-medium">{service.name}</span>
                </div>
                <Badge variant="secondary" className="text-xs">
                  <div className={`w-2 h-2 rounded-full mr-1 ${service.statusColor}`} />
                  {service.status}
                </Badge>
              </div>
              <div className="text-xs text-muted-foreground font-mono">
                {service.endpoint}
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    </div>
  );
}

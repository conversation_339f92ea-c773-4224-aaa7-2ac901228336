{"name": "gaming-hub", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "build:web": "expo export:web", "preview": "expo start --tunnel", "lint": "eslint src --ext .ts,.tsx", "type-check": "tsc --noEmit", "setup-ollama": "node scripts/setup-ollama.js", "test-ai": "node scripts/test-ai-integration.js", "setup-vscode": "node scripts/setup-vscode-extension.js", "setup": "npm run setup-ollama && npm run test-ai", "setup-dev": "npm run setup-ollama && npm run setup-vscode && npm run test-ai"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.2.0", "@react-navigation/bottom-tabs": "^7.4.2", "@react-navigation/native": "^7.1.14", "@react-navigation/stack": "^7.4.2", "@supabase/supabase-js": "^2.50.3", "axios": "^1.10.0", "expo": "~53.0.17", "expo-av": "^15.1.7", "expo-blur": "^14.1.5", "expo-file-system": "^18.1.11", "expo-font": "^13.3.2", "expo-haptics": "^14.1.4", "expo-linear-gradient": "^14.1.5", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "^19.1.0", "react-native": "0.79.5", "react-native-gesture-handler": "^2.27.1", "react-native-linear-gradient": "^2.8.3", "react-native-markdown-display": "^7.0.2", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "^5.5.1", "react-native-screens": "^4.11.1", "react-native-syntax-highlighter": "^2.1.0", "react-native-url-polyfill": "^2.0.0", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-webview": "^13.15.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@react-native-community/cli": "^18.0.0", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}
const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://opgyuyeuczddftaqkvdt.supabase.co/';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.IZQxZn2Upqtql-filzrSQ1q9zK1nPaweXzwB4LVDzuM';

const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function createConfirmedUser() {
  console.log('🎮 Creating confirmed test user for Findz Gaming Hub...\n');

  const testEmail = '<EMAIL>';
  const testPassword = 'gaming123';
  const testUsername = 'ConfirmedGamer';

  try {
    console.log('📝 Creating confirmed user account...');
    const { data: adminData, error: adminError } = await supabaseAdmin.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        username: testUsername,
        display_name: 'Confirmed Gamer',
      },
    });

    if (adminError) {
      console.error('❌ User creation failed:', adminError.message);
      
      if (adminError.message.includes('already registered') || adminError.message.includes('already exists')) {
        console.log('🔧 User already exists, trying to confirm existing user...');
        
        // Get existing user and confirm
        const { data: userData, error: getUserError } = await supabaseAdmin.auth.admin.listUsers();
        if (!getUserError && userData.users) {
          const existingUser = userData.users.find(u => u.email === testEmail);
          if (existingUser) {
            const { error: confirmError } = await supabaseAdmin.auth.admin.updateUserById(
              existingUser.id,
              { email_confirm: true }
            );
            
            if (!confirmError) {
              console.log('✅ Existing user email confirmed successfully!');
              console.log('User ID:', existingUser.id);
            } else {
              console.error('❌ Failed to confirm existing user email:', confirmError.message);
            }
          }
        }
      }
      return;
    }

    console.log('✅ Confirmed user account created successfully!');
    console.log('User ID:', adminData.user?.id);
    console.log('Email confirmed:', adminData.user?.email_confirmed_at ? 'Yes' : 'No');

    console.log('\n🎯 CONFIRMED TEST CREDENTIALS:');
    console.log('==============================');
    console.log('Email:', testEmail);
    console.log('Password:', testPassword);
    console.log('Username:', testUsername);
    console.log('\n✨ This user is pre-confirmed and ready to sign in!');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Run the script
createConfirmedUser();

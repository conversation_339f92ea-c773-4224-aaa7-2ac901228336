import { useParams } from "wouter";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { ArrowL<PERSON>t, Eye, Clock, Settings } from "lucide-react";
import { StreamPlayer } from "@/components/stream-player";
import { Chat } from "@/components/chat";
import { StreamControls } from "@/components/stream-controls";
import { Link } from "wouter";

export default function Stream() {
  const { id } = useParams<{ id: string }>();
  const streamId = parseInt(id || "0");
  
  const { data: stream, isLoading } = useQuery({
    queryKey: [`/api/streams/${streamId}`],
    enabled: !!streamId,
  });
  
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading stream...</p>
        </div>
      </div>
    );
  }
  
  if (!stream) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6 text-center">
            <h2 className="text-2xl font-bold mb-2">Stream Not Found</h2>
            <p className="text-muted-foreground mb-4">
              The stream you're looking for doesn't exist or has been removed.
            </p>
            <Link href="/">
              <Button>
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="bg-card border-b border-border px-8 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Link href="/">
              <Button variant="ghost" size="sm">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back
              </Button>
            </Link>
            <div>
              <h1 className="text-2xl font-bold">{stream.title}</h1>
              <div className="flex items-center space-x-4 mt-1">
                <Badge variant={stream.status === 'live' ? 'default' : 'secondary'}>
                  {stream.status === 'live' && <div className="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse" />}
                  {stream.status.toUpperCase()}
                </Badge>
                <span className="text-sm text-muted-foreground flex items-center">
                  <Eye className="w-4 h-4 mr-1" />
                  {stream.viewerCount || 0} viewers
                </span>
                <span className="text-sm text-muted-foreground flex items-center">
                  <Clock className="w-4 h-4 mr-1" />
                  {stream.status === 'live' ? 'Live now' : 'Scheduled'}
                </span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="sm">
              <Settings className="w-4 h-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>
      </header>
      
      <div className="p-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Stream Player and Controls */}
          <div className="lg:col-span-2 space-y-6">
            <StreamPlayer stream={stream} />
            <StreamControls streamId={streamId} />
          </div>
          
          {/* Chat */}
          <div>
            <Chat streamId={streamId} />
          </div>
        </div>
      </div>
    </div>
  );
}

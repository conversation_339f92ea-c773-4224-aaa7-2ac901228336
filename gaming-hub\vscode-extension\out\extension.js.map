{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,+CAAiC;AACjC,sDAAqD;AACrD,2CAA6B;AAC7B,uCAAyB;AAGzB,IAAI,MAAW,CAAC;AAChB,MAAM,IAAI,GAAG,IAAI,CAAC;AAElB,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,2CAA2C,CAAC,CAAC;IAEzD,uDAAuD;IACvD,gBAAgB,EAAE,CAAC;IAEnB,oBAAoB;IACpB,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,yBAAyB,EAAE,iBAAiB,CAAC,CAAC;IAC3G,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;IACzF,MAAM,eAAe,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gBAAgB,EAAE,QAAQ,CAAC,CAAC;IACpF,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mBAAmB,EAAE,WAAW,CAAC,CAAC;IAExF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,oBAAoB,EAAE,cAAc,EAAE,eAAe,EAAE,aAAa,CAAC,CAAC;IAEjG,uBAAuB;IACvB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,mEAAmE,CAAC,CAAC;AAC9G,CAAC;AAhBD,4BAgBC;AAED,SAAgB,UAAU;IACtB,IAAI,MAAM,EAAE;QACR,MAAM,CAAC,KAAK,EAAE,CAAC;KAClB;AACL,CAAC;AAJD,gCAIC;AAED,SAAS,gBAAgB;IACrB,MAAM,GAAG,GAAG,IAAA,iBAAO,GAAE,CAAC;IACtB,GAAG,CAAC,GAAG,CAAC,iBAAO,CAAC,IAAI,EAAE,CAAC,CAAC;IAExB,wBAAwB;IACxB,GAAG,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;QAC/C,GAAG,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,kBAAkB,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;IAChF,CAAC,CAAC,CAAC;IAEH,0BAA0B;IAC1B,GAAG,CAAC,IAAI,CAAC,iBAAiB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAC9D,IAAI;YACA,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YAC3C,MAAM,WAAW,GAAG,MAAM,yBAAyB,CAAC,QAAQ,EAAE,WAAW,CAAC,CAAC;YAC3E,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,CAAC,CAAC;SAC5C;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAClE;IACL,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAC5D,IAAI;YACA,MAAM,EAAE,WAAW,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YACjC,MAAM,mBAAmB,CAAC,WAAW,CAAC,CAAC;YACvC,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC,CAAC;SAC/B;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAClE;IACL,CAAC,CAAC,CAAC;IAEH,wBAAwB;IACxB,GAAG,CAAC,GAAG,CAAC,WAAW,EAAE,CAAC,GAAY,EAAE,GAAa,EAAE,EAAE;QACjD,IAAI;YACA,MAAM,QAAQ,GAAG,iBAAiB,EAAE,CAAC;YACrC,GAAG,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,CAAC,CAAC;SAC1B;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAClD;IACL,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,GAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE,KAAK,EAAE,GAAY,EAAE,GAAa,EAAE,EAAE;QAC/D,IAAI;YACA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;YACnC,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,OAAO,EAAE,GAAG,CAAC,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC;YAC9E,GAAG,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,CAAC;SACvC;QAAC,OAAO,KAAU,EAAE;YACjB,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;SAClE;IACL,CAAC,CAAC,CAAC;IAEH,MAAM,GAAG,GAAG,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,EAAE;QAC3B,OAAO,CAAC,GAAG,CAAC,qDAAqD,IAAI,EAAE,CAAC,CAAC;IAC7E,CAAC,CAAC,CAAC;AACP,CAAC;AAED,KAAK,UAAU,iBAAiB;IAC5B,MAAM,SAAS,GAAG;QACd,EAAE,KAAK,EAAE,mBAAmB,EAAE,KAAK,EAAE,aAAa,EAAE;QACpD,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,mBAAmB,EAAE;QAC7D,EAAE,KAAK,EAAE,sBAAsB,EAAE,KAAK,EAAE,YAAY,EAAE;KACzD,CAAC;IAEF,MAAM,gBAAgB,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,aAAa,CAAC,SAAS,EAAE;QAClE,WAAW,EAAE,wBAAwB;KACxC,CAAC,CAAC;IAEH,IAAI,CAAC,gBAAgB;QAAE,OAAO;IAE9B,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,YAAY,CAAC;QACjD,MAAM,EAAE,oBAAoB;QAC5B,WAAW,EAAE,iBAAiB;KACjC,CAAC,CAAC;IAEH,IAAI,CAAC,WAAW;QAAE,OAAO;IAEzB,IAAI;QACA,MAAM,WAAW,GAAG,MAAM,yBAAyB,CAAC,gBAAgB,CAAC,KAAK,EAAE,WAAW,CAAC,CAAC;QAEzF,MAAM,WAAW,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAC1D,YAAY,WAAW,yBAAyB,EAChD,cAAc,EACd,oBAAoB,CACvB,CAAC;QAEF,IAAI,WAAW,KAAK,cAAc,EAAE;YAChC,MAAM,mBAAmB,CAAC,WAAW,CAAC,CAAC;SAC1C;aAAM,IAAI,WAAW,KAAK,oBAAoB,EAAE;YAC7C,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,CAAC,CAAC;SACjG;KACJ;IAAC,OAAO,KAAU,EAAE;QACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,6BAA6B,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KAChF;AACL,CAAC;AAED,KAAK,UAAU,yBAAyB,CAAC,UAAkB,EAAE,WAAmB;IAC5E,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;IAC3D,IAAI,CAAC,gBAAgB,EAAE;QACnB,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;KAC/C;IAED,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;IAE3E,2BAA2B;IAC3B,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE;QAC7B,EAAE,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;KAClD;IAED,oEAAoE;IACpE,MAAM,aAAa,GAAG,gBAAgB,CAAC,UAAU,CAAC,CAAC;IAEnD,eAAe;IACf,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE;QAC9B,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;QACnD,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAEvC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACzB,EAAE,CAAC,SAAS,CAAC,OAAO,EAAE,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;SAC9C;QAED,EAAE,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,CAAC;KAC5C;IAED,OAAO,WAAW,CAAC;AACvB,CAAC;AAED,SAAS,gBAAgB,CAAC,UAAkB;IACxC,kFAAkF;IAClF,MAAM,SAAS,GAAQ;QACnB,aAAa,EAAE;YACX;gBACI,IAAI,EAAE,YAAY;gBAClB,OAAO,EAAE;;;;;;;;;;;;;;;QAejB;aACK;YACD;gBACI,IAAI,EAAE,SAAS;gBACf,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAiEb;aACC;YACD;gBACI,IAAI,EAAE,WAAW;gBACjB,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;CAqBxB;aACY;SACJ;KACJ,CAAC;IAEF,OAAO,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;AACvC,CAAC;AAED,KAAK,UAAU,mBAAmB,CAAC,WAAmB;IAClD,MAAM,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,mBAAmB,EAAE,MAAM,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC;AAC5F,CAAC;AAED,SAAS,iBAAiB;IACtB,4CAA4C;IAC5C,8BAA8B;IAC9B,OAAO,EAAE,CAAC;AACd,CAAC;AAED,KAAK,UAAU,WAAW;IACtB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC;IAC1D,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,uBAAuB,CAAC,CAAC;IAE7D,MAAM,MAAM,CAAC,GAAG,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC;AAC5D,CAAC;AAED,KAAK,UAAU,QAAQ;IACnB,MAAM,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC;IAC9C,IAAI,CAAC,MAAM,EAAE;QACT,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,wBAAwB,CAAC,CAAC;QAC3D,OAAO;KACV;IAED,MAAM,SAAS,GAAG,MAAM,CAAC,SAAS,CAAC;IACnC,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;IAExD,IAAI,CAAC,YAAY,EAAE;QACf,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,2CAA2C,CAAC,CAAC;QAC9E,OAAO;KACV;IAED,IAAI;QACA,iDAAiD;QACjD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,oCAAoC,CAAC,CAAC;KAC9E;IAAC,OAAO,KAAU,EAAE;QACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,yBAAyB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KAC5E;AACL,CAAC;AAED,KAAK,UAAU,WAAW;IACtB,MAAM,gBAAgB,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC;IAC3D,IAAI,CAAC,gBAAgB,EAAE;QACnB,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,CAAC,CAAC;QAC7D,OAAO;KACV;IAED,IAAI;QACA,+CAA+C;QAC/C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2CAA2C,CAAC,CAAC;KACrF;IAAC,OAAO,KAAU,EAAE;QACjB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,sBAAsB,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;KACzE;AACL,CAAC"}
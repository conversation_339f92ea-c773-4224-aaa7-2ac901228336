import axios from 'axios';
import { Environment } from '../config/environment';

export interface ModelSetupResult {
  model: string;
  status: 'installed' | 'downloading' | 'failed' | 'not_found';
  progress?: number;
  error?: string;
  size?: string;
}

export interface GamingModelConfig {
  name: string;
  displayName: string;
  description: string;
  useCase: string;
  priority: number;
  size: string;
  parameters: {
    temperature: number;
    top_p: number;
    top_k: number;
    repeat_penalty: number;
    num_ctx: number;
    num_predict: number;
  };
  systemPrompt: string;
}

class OllamaSetupService {
  private baseUrl: string;
  private gamingModels: GamingModelConfig[];

  constructor() {
    this.baseUrl = Environment.OLLAMA.BASE_URL;
    this.initializeGamingModels();
  }

  private initializeGamingModels() {
    this.gamingModels = [
      {
        name: 'mistral:7b-instruct',
        displayName: 'Mistral 7B Instruct',
        description: 'Best overall gaming assistant - excellent instruction following',
        useCase: 'General gaming assistance, strategy advice, coaching',
        priority: 1,
        size: '4.1GB',
        parameters: {
          temperature: 0.7,
          top_p: 0.9,
          top_k: 40,
          repeat_penalty: 1.1,
          num_ctx: 4096,
          num_predict: 1000,
        },
        systemPrompt: 'You are an expert gaming assistant specializing in strategy, coaching, and player development.',
      },
      {
        name: 'deepseek-coder:6.7b-instruct',
        displayName: 'DeepSeek Coder 6.7B',
        description: 'Specialized for game development and coding assistance',
        useCase: 'Game development, code review, debugging, technical help',
        priority: 2,
        size: '3.8GB',
        parameters: {
          temperature: 0.3,
          top_p: 0.8,
          top_k: 30,
          repeat_penalty: 1.2,
          num_ctx: 8192,
          num_predict: 1500,
        },
        systemPrompt: 'You are an expert game developer and coding assistant specializing in game programming, optimization, and technical problem-solving.',
      },
      {
        name: 'neural-chat:7b',
        displayName: 'Neural Chat 7B',
        description: 'Excellent for interactive conversations and real-time gaming help',
        useCase: 'Interactive chat, real-time assistance, casual gaming support',
        priority: 3,
        size: '4.1GB',
        parameters: {
          temperature: 0.8,
          top_p: 0.95,
          top_k: 50,
          repeat_penalty: 1.05,
          num_ctx: 4096,
          num_predict: 800,
        },
        systemPrompt: 'You are a friendly and knowledgeable gaming companion, ready to help with any gaming questions or challenges.',
      },
      {
        name: 'codellama:7b-instruct',
        displayName: 'Code Llama 7B Instruct',
        description: 'Advanced code understanding for game development',
        useCase: 'Complex game development, architecture design, code optimization',
        priority: 4,
        size: '3.8GB',
        parameters: {
          temperature: 0.2,
          top_p: 0.75,
          top_k: 25,
          repeat_penalty: 1.3,
          num_ctx: 8192,
          num_predict: 2000,
        },
        systemPrompt: 'You are an expert software architect and game developer with deep knowledge of programming languages, frameworks, and game engine development.',
      },
      {
        name: 'phi3:3.8b',
        displayName: 'Phi-3 3.8B',
        description: 'Lightweight and fast for quick gaming tips and responses',
        useCase: 'Quick tips, fast responses, mobile gaming, casual assistance',
        priority: 5,
        size: '2.2GB',
        parameters: {
          temperature: 0.6,
          top_p: 0.85,
          top_k: 35,
          repeat_penalty: 1.1,
          num_ctx: 4096,
          num_predict: 600,
        },
        systemPrompt: 'You are a quick and helpful gaming assistant, providing concise and effective gaming advice.',
      },
    ];
  }

  async checkOllamaConnection(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/tags`, { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      console.error('Ollama connection failed:', error);
      return false;
    }
  }

  async getInstalledModels(): Promise<string[]> {
    try {
      const response = await axios.get(`${this.baseUrl}/api/tags`);
      return response.data.models?.map((model: any) => model.name) || [];
    } catch (error) {
      console.error('Error fetching installed models:', error);
      return [];
    }
  }

  async setupGamingModels(): Promise<ModelSetupResult[]> {
    const results: ModelSetupResult[] = [];
    const installedModels = await this.getInstalledModels();

    for (const model of this.gamingModels) {
      const isInstalled = installedModels.some(installed => 
        installed.includes(model.name.split(':')[0])
      );

      if (isInstalled) {
        results.push({
          model: model.name,
          status: 'installed',
          size: model.size,
        });
      } else {
        // Attempt to pull the model
        const pullResult = await this.pullModel(model.name);
        results.push({
          model: model.name,
          status: pullResult.success ? 'installed' : 'failed',
          error: pullResult.error,
          size: model.size,
        });
      }
    }

    return results;
  }

  async pullModel(modelName: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log(`Pulling model: ${modelName}`);
      
      const response = await axios.post(
        `${this.baseUrl}/api/pull`,
        { name: modelName },
        { 
          timeout: 600000, // 10 minutes timeout
          headers: { 'Content-Type': 'application/json' }
        }
      );

      return { success: response.status === 200 };
    } catch (error: any) {
      console.error(`Error pulling model ${modelName}:`, error);
      return { 
        success: false, 
        error: error.response?.data?.error || error.message || 'Failed to pull model'
      };
    }
  }

  async testModel(modelName: string): Promise<{ success: boolean; responseTime: number; error?: string }> {
    const startTime = Date.now();
    
    try {
      const testPrompt = 'Give me one quick gaming tip in 10 words or less.';
      
      const response = await axios.post(
        `${this.baseUrl}/api/generate`,
        {
          model: modelName,
          prompt: testPrompt,
          stream: false,
          options: this.getModelParameters(modelName),
        },
        { timeout: 30000 }
      );

      const responseTime = Date.now() - startTime;
      
      if (response.data.response && response.data.response.length > 0) {
        return { success: true, responseTime };
      } else {
        return { success: false, responseTime, error: 'Empty response' };
      }
    } catch (error: any) {
      const responseTime = Date.now() - startTime;
      return { 
        success: false, 
        responseTime, 
        error: error.message || 'Test failed' 
      };
    }
  }

  getModelParameters(modelName: string): any {
    const model = this.gamingModels.find(m => m.name === modelName);
    return model?.parameters || {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      repeat_penalty: 1.1,
      num_ctx: 4096,
      num_predict: 1000,
    };
  }

  getModelConfig(modelName: string): GamingModelConfig | undefined {
    return this.gamingModels.find(m => m.name === modelName);
  }

  getRecommendedModels(): GamingModelConfig[] {
    return this.gamingModels.sort((a, b) => a.priority - b.priority);
  }

  async optimizeForHardware(): Promise<{
    recommendedModels: string[];
    configuration: any;
    reasoning: string;
  }> {
    // Simple hardware detection based on available memory
    // In a real implementation, you might use more sophisticated detection
    const availableMemory = this.estimateAvailableMemory();
    
    let recommendedModels: string[] = [];
    let reasoning = '';

    if (availableMemory >= 16) {
      // High-end setup
      recommendedModels = [
        'mistral:7b-instruct',
        'deepseek-coder:6.7b-instruct',
        'neural-chat:7b',
        'codellama:7b-instruct'
      ];
      reasoning = 'High-memory system detected. Installing full model suite for optimal performance.';
    } else if (availableMemory >= 8) {
      // Mid-range setup
      recommendedModels = [
        'mistral:7b-instruct',
        'deepseek-coder:6.7b-instruct',
        'phi3:3.8b'
      ];
      reasoning = 'Medium-memory system detected. Installing balanced model selection.';
    } else {
      // Low-end setup
      recommendedModels = [
        'phi3:3.8b',
        'mistral:7b-instruct'
      ];
      reasoning = 'Limited memory detected. Installing lightweight models for best performance.';
    }

    return {
      recommendedModels,
      configuration: {
        concurrent_requests: availableMemory >= 16 ? 4 : availableMemory >= 8 ? 2 : 1,
        keep_alive: '5m',
        num_parallel: availableMemory >= 16 ? 2 : 1,
      },
      reasoning,
    };
  }

  private estimateAvailableMemory(): number {
    // This is a simplified estimation
    // In a real implementation, you might use system APIs
    if (typeof navigator !== 'undefined' && 'deviceMemory' in navigator) {
      return (navigator as any).deviceMemory || 8;
    }
    return 8; // Default assumption
  }

  async createModelfile(modelName: string, customizations?: any): Promise<string> {
    const model = this.getModelConfig(modelName);
    if (!model) {
      throw new Error(`Model configuration not found: ${modelName}`);
    }

    const modelfile = `
FROM ${modelName}

# Gaming-optimized parameters
PARAMETER temperature ${customizations?.temperature || model.parameters.temperature}
PARAMETER top_p ${customizations?.top_p || model.parameters.top_p}
PARAMETER top_k ${customizations?.top_k || model.parameters.top_k}
PARAMETER repeat_penalty ${customizations?.repeat_penalty || model.parameters.repeat_penalty}
PARAMETER num_ctx ${customizations?.num_ctx || model.parameters.num_ctx}
PARAMETER num_predict ${customizations?.num_predict || model.parameters.num_predict}

# Gaming-specific system prompt
SYSTEM """${customizations?.systemPrompt || model.systemPrompt}"""

# Additional gaming context
TEMPLATE """{{ if .System }}<|system|>
{{ .System }}<|end|>
{{ end }}{{ if .Prompt }}<|user|>
{{ .Prompt }}<|end|>
{{ end }}<|assistant|>
{{ .Response }}<|end|>
"""
`;

    return modelfile;
  }

  async generateSetupReport(): Promise<string> {
    const isConnected = await this.checkOllamaConnection();
    const installedModels = await this.getInstalledModels();
    const optimization = await this.optimizeForHardware();

    return `
# Ollama Gaming Setup Report

## Connection Status
- Ollama Service: ${isConnected ? '✅ Connected' : '❌ Not Connected'}
- Base URL: ${this.baseUrl}

## Installed Models
${installedModels.length > 0 
  ? installedModels.map(model => `- ✅ ${model}`).join('\n')
  : '- No models installed'
}

## Hardware Optimization
${optimization.reasoning}

### Recommended Models:
${optimization.recommendedModels.map(model => `- ${model}`).join('\n')}

### Recommended Configuration:
\`\`\`json
${JSON.stringify(optimization.configuration, null, 2)}
\`\`\`

## Gaming Model Recommendations

${this.gamingModels.map(model => `
### ${model.displayName}
- **Use Case**: ${model.useCase}
- **Size**: ${model.size}
- **Priority**: ${model.priority}
- **Description**: ${model.description}
`).join('\n')}

## Next Steps
1. Install recommended models using the Gaming Hub setup screen
2. Test each model to ensure proper functionality
3. Configure preferred models in the AI Assistant settings
4. Start using the AI-powered gaming features!
`;
  }
}

export const ollamaSetupService = new OllamaSetupService();
export default ollamaSetupService;

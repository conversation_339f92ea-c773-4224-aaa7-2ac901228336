import { useState } from "react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { 
  Play, 
  Square, 
  Circle, 
  Pause,
  Settings,
  Wifi,
  Server,
  Zap
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";

interface StreamControlsProps {
  streamId: number;
}

export function StreamControls({ streamId }: StreamControlsProps) {
  const [isRecording, setIsRecording] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();
  
  const { data: stream } = useQuery({
    queryKey: [`/api/streams/${streamId}`],
    enabled: !!streamId,
  });
  
  const updateStreamMutation = useMutation({
    mutationFn: async (updates: any) => {
      return await apiRequest('PUT', `/api/streams/${streamId}`, updates);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/streams/${streamId}`] });
      queryClient.invalidateQueries({ queryKey: ['/api/streams'] });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });
  
  const handleStreamToggle = () => {
    if (stream?.status === 'live') {
      updateStreamMutation.mutate({ 
        status: 'inactive',
        endedAt: new Date().toISOString()
      });
      toast({
        title: "Stream Stopped",
        description: "Your stream has been stopped successfully.",
      });
    } else {
      updateStreamMutation.mutate({ 
        status: 'live',
        startedAt: new Date().toISOString()
      });
      toast({
        title: "Stream Started",
        description: "Your stream is now live!",
      });
    }
  };
  
  const handleRecordToggle = () => {
    setIsRecording(!isRecording);
    updateStreamMutation.mutate({ isRecording: !isRecording });
    toast({
      title: isRecording ? "Recording Stopped" : "Recording Started",
      description: isRecording 
        ? "Stream recording has been stopped."
        : "Stream recording has been started.",
    });
  };
  
  if (!stream) return null;
  
  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Stream Controls</CardTitle>
          <div className="flex items-center space-x-2">
            <Button 
              variant={isRecording ? "destructive" : "default"}
              size="sm"
              onClick={handleRecordToggle}
              disabled={updateStreamMutation.isPending}
            >
              <Circle className="w-4 h-4 mr-1" />
              {isRecording ? "Stop Recording" : "Record"}
            </Button>
            <Button 
              variant={stream.status === 'live' ? "destructive" : "default"}
              size="sm"
              onClick={handleStreamToggle}
              disabled={updateStreamMutation.isPending}
            >
              {stream.status === 'live' ? (
                <>
                  <Square className="w-4 h-4 mr-1" />
                  Stop Stream
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-1" />
                  Start Stream
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <div className="bg-muted/50 p-4 rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Bitrate</span>
              <Zap className="w-4 h-4 text-muted-foreground" />
            </div>
            <div className="text-lg font-semibold">{stream.bitrate || 2500} kbps</div>
            <div className="text-xs text-green-600">Adaptive</div>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Quality</span>
              <Settings className="w-4 h-4 text-muted-foreground" />
            </div>
            <div className="text-lg font-semibold">{stream.quality || '720p'}</div>
            <div className="text-xs text-muted-foreground">60fps</div>
          </div>
          
          <div className="bg-muted/50 p-4 rounded-lg border">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Protocol</span>
              <Wifi className="w-4 h-4 text-muted-foreground" />
            </div>
            <div className="text-lg font-semibold">{stream.protocol?.toUpperCase() || 'WEBRTC'}</div>
            <div className="text-xs text-green-600">P2P Enabled</div>
          </div>
        </div>
        
        {/* Performance Metrics */}
        <div className="space-y-4">
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">CPU Usage</span>
              <span className="text-sm font-medium">34%</span>
            </div>
            <Progress value={34} className="h-2" />
          </div>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Memory Usage</span>
              <span className="text-sm font-medium">52%</span>
            </div>
            <Progress value={52} className="h-2" />
          </div>
          
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm text-muted-foreground">Network Usage</span>
              <span className="text-sm font-medium">145 Mbps</span>
            </div>
            <Progress value={72} className="h-2" />
          </div>
        </div>
        
        {/* Stream Information */}
        <div className="mt-6 p-4 bg-muted/30 rounded-lg border">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium">Stream Key</span>
            <Badge variant="outline">{stream.streamKey}</Badge>
          </div>
          <div className="text-xs text-muted-foreground">
            Keep this key private and secure
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

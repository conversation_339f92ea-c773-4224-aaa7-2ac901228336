import Constants from 'expo-constants';

// Environment configuration with fallbacks
const getEnvVar = (key: string, fallback?: string): string => {
  const value = Constants.expoConfig?.extra?.[key] || 
                process.env[key] || 
                Constants.manifest?.extra?.[key];
  
  if (!value && !fallback) {
    console.warn(`Environment variable ${key} is not set`);
  }
  
  return value || fallback || '';
};

export const Environment = {
  // App Configuration
  APP_ENV: getEnvVar('EXPO_PUBLIC_APP_ENV', 'development'),
  API_TIMEOUT: parseInt(getEnvVar('EXPO_PUBLIC_API_TIMEOUT', '30000')),
  AI_ENABLED: getEnvVar('EXPO_PUBLIC_AI_ENABLED', 'true') === 'true',
  VOICE_ENABLED: getEnvVar('EXPO_PUBLIC_VOICE_ENABLED', 'true') === 'true',

  // Supabase Configuration
  SUPABASE: {
    URL: getEnvVar('EXPO_PUBLIC_SUPABASE_URL'),
    ANON_KEY: getEnvVar('EXPO_PUBLIC_SUPABASE_ANON_KEY'),
    SECRET_KEY: getEnvVar('SUPABASE_SECRET_KEY'),
  },

  // AI Services
  OLLAMA: {
    BASE_URL: getEnvVar('EXPO_PUBLIC_OLLAMA_BASE_URL', 'http://127.0.0.1:11434'),
    MODEL: getEnvVar('EXPO_PUBLIC_OLLAMA_MODEL', 'mistral-small3.1:24b'),
    FALLBACK_URL: getEnvVar('EXPO_PUBLIC_OLLAMA_FALLBACK_URL', 'http://localhost:8000'),
    BACKUP_URL: getEnvVar('EXPO_PUBLIC_OLLAMA_BACKUP_URL', 'http://localhost:1234'),
  },

  OPENROUTER: {
    API_KEY: getEnvVar('OPENROUTER_API_KEY'),
    BASE_URL: 'https://openrouter.ai/api/v1',
  },

  ANTHROPIC: {
    API_KEY: getEnvVar('ANTHROPIC_API_KEY'),
    BASE_URL: 'https://api.anthropic.com',
  },

  DEEPSEEK: {
    API_KEY: getEnvVar('DEEPSEEK_API_KEY'),
    BASE_URL: 'https://api.deepseek.com',
  },

  // Media Services
  ELEVENLABS: {
    API_KEY: getEnvVar('ELEVENLABS_API_KEY'),
    BASE_URL: 'https://api.elevenlabs.io/v1',
  },

  CLOUDINARY: {
    CLOUD_NAME: getEnvVar('EXPO_PUBLIC_CLOUDINARY_CLOUD_NAME'),
    API_KEY: getEnvVar('CLOUDINARY_API_KEY'),
    API_SECRET: getEnvVar('CLOUDINARY_API_SECRET'),
    BASE_URL: 'https://api.cloudinary.com/v1_1',
  },

  // Search and Web APIs
  BRAVE: {
    API_KEY: getEnvVar('BRAVE_API_KEY'),
    BASE_URL: 'https://api.search.brave.com/res/v1',
  },

  FIRECRAWL: {
    API_KEY: getEnvVar('FIRECRAWL_API_KEY'),
    BASE_URL: 'https://api.firecrawl.dev/v0',
  },

  EXA: {
    API_KEY: getEnvVar('EXA_API_KEY'),
    BASE_URL: 'https://api.exa.ai',
  },

  // Development APIs
  GITHUB: {
    API_KEY: getEnvVar('GITHUB_API_KEY'),
    BASE_URL: 'https://api.github.com',
  },

  // Video Streaming & Communication
  AGORA: {
    APP_ID: getEnvVar('AGORA_APP_ID'),
    CERTIFICATE: getEnvVar('AGORA_CERTIFICATE'),
  },
};

// Validation function to check if required environment variables are set
export const validateEnvironment = (): { isValid: boolean; missingVars: string[] } => {
  const requiredVars = [
    'EXPO_PUBLIC_SUPABASE_URL',
    'EXPO_PUBLIC_SUPABASE_ANON_KEY',
  ];

  const missingVars = requiredVars.filter(varName => !getEnvVar(varName));
  
  return {
    isValid: missingVars.length === 0,
    missingVars,
  };
};

// Helper function to get API headers for different services
export const getApiHeaders = (service: keyof typeof Environment) => {
  const baseHeaders = {
    'Content-Type': 'application/json',
    'User-Agent': 'FindzGamingHub/1.0.0',
  };

  switch (service) {
    case 'OPENROUTER':
      return {
        ...baseHeaders,
        'Authorization': `Bearer ${Environment.OPENROUTER.API_KEY}`,
        'HTTP-Referer': 'https://findz-gaming-hub.com',
        'X-Title': 'Findz Gaming Hub',
      };

    case 'ANTHROPIC':
      return {
        ...baseHeaders,
        'x-api-key': Environment.ANTHROPIC.API_KEY,
        'anthropic-version': '2023-06-01',
      };

    case 'ELEVENLABS':
      return {
        ...baseHeaders,
        'xi-api-key': Environment.ELEVENLABS.API_KEY,
      };

    case 'BRAVE':
      return {
        ...baseHeaders,
        'X-Subscription-Token': Environment.BRAVE.API_KEY,
      };

    case 'GITHUB':
      return {
        ...baseHeaders,
        'Authorization': `token ${Environment.GITHUB.API_KEY}`,
        'Accept': 'application/vnd.github.v3+json',
      };

    default:
      return baseHeaders;
  }
};

// Development helpers
export const isDevelopment = Environment.APP_ENV === 'development';
export const isProduction = Environment.APP_ENV === 'production';

// API endpoint builders
export const buildApiUrl = (service: keyof typeof Environment, endpoint: string): string => {
  const serviceConfig = Environment[service] as any;
  const baseUrl = serviceConfig?.BASE_URL || serviceConfig?.URL;
  
  if (!baseUrl) {
    throw new Error(`Base URL not configured for service: ${service}`);
  }
  
  return `${baseUrl}${endpoint.startsWith('/') ? '' : '/'}${endpoint}`;
};

// Timeout configuration
export const getTimeoutConfig = (customTimeout?: number) => ({
  timeout: customTimeout || Environment.API_TIMEOUT,
});

export default Environment;

const fs = require('fs');
const path = require('path');

console.log('🎥 Testing Agora Integration');
console.log('============================\n');

// Read credentials from api_credentials.md
const credentialsPath = path.join(__dirname, '..', '..', 'api_credentials.md');

try {
  const credentialsContent = fs.readFileSync(credentialsPath, 'utf8');
  
  // Extract Agora credentials
  const agoraAppIdMatch = credentialsContent.match(/AGORA_APP_ID=(.+)/);
  const agoraCertificateMatch = credentialsContent.match(/AGORA_CERTIFICATE=(.+)/);
  
  const agoraAppId = agoraAppIdMatch ? agoraAppIdMatch[1].trim() : null;
  const agoraCertificate = agoraCertificateMatch ? agoraCertificateMatch[1].trim() : null;
  
  console.log('📋 Agora Configuration Check:');
  console.log('==============================');
  console.log(`✅ App ID: ${agoraAppId ? 'Configured' : '❌ Missing'}`);
  console.log(`✅ Certificate: ${agoraCertificate ? 'Configured' : '❌ Missing'}`);
  
  if (agoraAppId) {
    console.log(`📱 App ID: ${agoraAppId.substring(0, 8)}...${agoraAppId.substring(agoraAppId.length - 4)}`);
  }
  
  if (agoraCertificate) {
    console.log(`🔐 Certificate: ${agoraCertificate.substring(0, 8)}...${agoraCertificate.substring(agoraCertificate.length - 4)}`);
  }
  
  console.log('\n🔧 Environment Integration:');
  console.log('============================');
  
  // Check if environment.ts has Agora config
  const envPath = path.join(__dirname, '..', 'src', 'config', 'environment.ts');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  const hasAgoraConfig = envContent.includes('AGORA:');
  console.log(`✅ Environment Config: ${hasAgoraConfig ? 'Integrated' : '❌ Missing'}`);
  
  // Check if app.json has Agora variables
  const appJsonPath = path.join(__dirname, '..', 'app.json');
  const appJsonContent = fs.readFileSync(appJsonPath, 'utf8');
  
  const hasAgoraInAppJson = appJsonContent.includes('AGORA_APP_ID') && appJsonContent.includes('AGORA_CERTIFICATE');
  console.log(`✅ App.json Config: ${hasAgoraInAppJson ? 'Integrated' : '❌ Missing'}`);
  
  // Check if Agora service exists
  const agoraServicePath = path.join(__dirname, '..', 'src', 'services', 'agoraService.ts');
  const hasAgoraService = fs.existsSync(agoraServicePath);
  console.log(`✅ Agora Service: ${hasAgoraService ? 'Created' : '❌ Missing'}`);
  
  console.log('\n🎮 Integration Status:');
  console.log('======================');
  
  if (agoraAppId && agoraCertificate && hasAgoraConfig && hasAgoraInAppJson && hasAgoraService) {
    console.log('🎉 ✅ AGORA FULLY INTEGRATED!');
    console.log('');
    console.log('🚀 Ready for:');
    console.log('   • Live video streaming');
    console.log('   • Voice chat during gaming');
    console.log('   • Screen sharing');
    console.log('   • Multi-user video calls');
    console.log('   • Real-time communication');
    console.log('');
    console.log('💡 Next Steps:');
    console.log('   1. Test video calling in the app');
    console.log('   2. Create gaming rooms with voice chat');
    console.log('   3. Enable screen sharing for tutorials');
    console.log('   4. Set up live streaming features');
  } else {
    console.log('⚠️  PARTIAL INTEGRATION');
    console.log('');
    console.log('❌ Missing components:');
    if (!agoraAppId) console.log('   • Agora App ID');
    if (!agoraCertificate) console.log('   • Agora Certificate');
    if (!hasAgoraConfig) console.log('   • Environment configuration');
    if (!hasAgoraInAppJson) console.log('   • App.json variables');
    if (!hasAgoraService) console.log('   • Agora service implementation');
  }
  
  console.log('\n📚 Agora Features Available:');
  console.log('=============================');
  console.log('🎥 Video Calling: Real-time video communication');
  console.log('🎤 Voice Chat: Crystal clear audio during gaming');
  console.log('📺 Live Streaming: Broadcast gameplay to audience');
  console.log('🖥️  Screen Sharing: Share game screens and tutorials');
  console.log('👥 Multi-user: Support for multiple participants');
  console.log('🌐 Cross-platform: Works on mobile, web, and desktop');
  console.log('🔒 Secure: Enterprise-grade security and encryption');
  console.log('⚡ Low Latency: Optimized for real-time gaming');
  
} catch (error) {
  console.error('❌ Error testing Agora integration:', error.message);
}

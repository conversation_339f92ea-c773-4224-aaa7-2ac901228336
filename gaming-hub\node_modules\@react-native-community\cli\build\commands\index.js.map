{"version": 3, "names": ["projectCommands", "configCommands", "cleanCommands", "clean", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "info", "detachedCommands", "init", "doctor"], "sources": ["../../src/commands/index.ts"], "sourcesContent": ["import {Command, DetachedCommand} from '@react-native-community/cli-types';\nimport {commands as cleanCommands} from '@react-native-community/cli-clean';\nimport {commands as doctorCommands} from '@react-native-community/cli-doctor';\nimport {commands as configCommands} from '@react-native-community/cli-config';\nimport init from './init';\n\nexport const projectCommands = [\n  ...configCommands,\n  cleanCommands.clean,\n  doctorCommands.info,\n] as Command[];\n\nexport const detachedCommands = [\n  init,\n  doctorCommands.doctor,\n] as DetachedCommand[];\n"], "mappings": ";;;;;;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;AAA0B;AAEnB,MAAMA,eAAe,GAAG,CAC7B,GAAGC,qBAAc,EACjBC,oBAAa,CAACC,KAAK,EACnBC,qBAAc,CAACC,IAAI,CACP;AAAC;AAER,MAAMC,gBAAgB,GAAG,CAC9BC,aAAI,EACJH,qBAAc,CAACI,MAAM,CACD;AAAC"}
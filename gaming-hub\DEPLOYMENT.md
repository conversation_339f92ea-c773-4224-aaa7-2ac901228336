# 🚀 Deployment Guide - Findz Gaming Hub

This guide covers deploying the Gaming Hub to various platforms and environments.

## 📋 Prerequisites

Before deploying, ensure you have:

- [Expo CLI](https://docs.expo.dev/get-started/installation/) installed
- [EAS CLI](https://docs.expo.dev/build/setup/) installed (`npm install -g eas-cli`)
- Expo account created and logged in (`expo login`)
- Supabase project set up with database schema

## 🗄️ Database Setup

### 1. Create Supabase Project

1. Go to [supabase.com](https://supabase.com) and create a new project
2. Wait for the project to be fully initialized
3. Go to Settings > API to get your project URL and anon key

### 2. Set up Database Schema

1. Go to SQL Editor in your Supabase dashboard
2. Run the contents of `database/schema.sql` to create tables and functions
3. Run the contents of `database/sample_data.sql` to add sample games and achievements

### 3. Configure Row Level Security

The schema includes RLS policies, but verify they're enabled:
- Go to Authentication > Policies
- Ensure all tables have appropriate policies enabled

## 📱 Mobile App Deployment

### 1. Configure EAS

Initialize EAS in your project:
```bash
eas init
```

### 2. Configure app.json

Update your `app.json` with production settings:
```json
{
  "expo": {
    "name": "Findz Gaming Hub",
    "slug": "findz-gaming-hub",
    "version": "1.0.0",
    "ios": {
      "bundleIdentifier": "com.financefindz.finderzfundz"
    },
    "android": {
      "package": "com.financefindz.finderzfundz"
    }
  }
}
```

### 3. Build for Android

```bash
# Development build
eas build --platform android --profile development

# Production build
eas build --platform android --profile production
```

### 4. Build for iOS

```bash
# Development build
eas build --platform ios --profile development

# Production build
eas build --platform ios --profile production
```

### 5. Submit to App Stores

```bash
# Submit to Google Play Store
eas submit --platform android

# Submit to Apple App Store
eas submit --platform ios
```

## 🌐 Web Deployment

### 1. Build for Web

```bash
npm run build:web
```

This creates a `web-build` folder with static files.

### 2. Deploy to Hosting Services

#### Vercel
```bash
npm install -g vercel
vercel --prod
```

#### Netlify
```bash
npm install -g netlify-cli
netlify deploy --prod --dir=web-build
```

#### Firebase Hosting
```bash
npm install -g firebase-tools
firebase init hosting
firebase deploy
```

#### GitHub Pages
1. Push the `web-build` folder to a `gh-pages` branch
2. Enable GitHub Pages in repository settings

## 🔧 Environment Configuration

### Development Environment

Create `.env.local`:
```
EXPO_PUBLIC_SUPABASE_URL=your_dev_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_dev_anon_key
```

### Production Environment

For EAS builds, use environment variables:
```bash
eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_URL --value your_prod_url
eas secret:create --scope project --name EXPO_PUBLIC_SUPABASE_ANON_KEY --value your_prod_key
```

## 🎮 Game Template Deployment

### 1. Host Game Templates

Deploy your game templates to a web server:

#### Option A: Same Domain
- Place games in `web-build/games/` folder
- Access via `https://yourdomain.com/games/clicker-game/`

#### Option B: Separate Hosting
- Deploy games to a CDN or separate hosting
- Update game URLs in your Supabase `games` table

### 2. CORS Configuration

Ensure your game hosting allows embedding:
```html
<!-- Add to game HTML head -->
<meta http-equiv="Content-Security-Policy" content="frame-ancestors 'self' https://yourdomain.com;">
```

## 📊 Monitoring and Analytics

### 1. Expo Analytics

Add analytics to track app usage:
```bash
expo install expo-analytics-amplitude
```

### 2. Supabase Analytics

Monitor database performance in Supabase dashboard:
- Go to Reports to see usage statistics
- Monitor API calls and performance
- Set up alerts for high usage

### 3. Error Tracking

Add error tracking with Sentry:
```bash
expo install @sentry/react-native
```

## 🔒 Security Considerations

### 1. API Keys

- Never commit API keys to version control
- Use environment variables for all sensitive data
- Rotate keys regularly

### 2. Database Security

- Ensure RLS policies are properly configured
- Regularly audit user permissions
- Monitor for suspicious activity

### 3. Game Security

- Validate all game scores server-side
- Implement rate limiting for score submissions
- Use HTTPS for all game communications

## 🚀 CI/CD Pipeline

### GitHub Actions Example

Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy Gaming Hub

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '16'
          
      - name: Install dependencies
        run: npm install
        
      - name: Build web
        run: npm run build:web
        
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
```

## 📈 Performance Optimization

### 1. Bundle Size

- Use Expo's bundle analyzer: `expo export --dump-sourcemap`
- Remove unused dependencies
- Optimize images and assets

### 2. Database Performance

- Add indexes for frequently queried columns
- Use database functions for complex operations
- Implement caching where appropriate

### 3. Game Loading

- Preload game assets
- Use CDN for game files
- Implement progressive loading

## 🔄 Updates and Maintenance

### 1. Over-the-Air Updates

Use Expo Updates for quick fixes:
```bash
expo publish
```

### 2. Database Migrations

Create migration scripts for schema changes:
```sql
-- Example migration
ALTER TABLE users ADD COLUMN premium_member BOOLEAN DEFAULT FALSE;
```

### 3. Monitoring

Set up monitoring for:
- App crashes and errors
- Database performance
- User engagement metrics
- Game loading times

## 📞 Support and Troubleshooting

### Common Issues

1. **Build Failures**: Check EAS build logs for detailed error messages
2. **Database Connection**: Verify Supabase URL and keys
3. **Game Loading**: Check CORS settings and game URLs
4. **Authentication**: Ensure RLS policies are correctly configured

### Getting Help

- Check Expo documentation
- Review Supabase guides
- Open GitHub issues for bugs
- Contact support team

---

**Ready to deploy! 🚀**

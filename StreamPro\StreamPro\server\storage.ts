import {
  users,
  streams,
  streamViewers,
  chatMessages,
  streamAnalytics,
  webrtcConnections,
  type User,
  type InsertUser,
  type Stream,
  type InsertStream,
  type StreamViewer,
  type ChatMessage,
  type InsertChatMessage,
  type StreamAnalytics,
  type InsertStreamAnalytics,
  type WebrtcConnection,
  type InsertWebrtcConnection,
} from "@shared/schema";
import { db } from "./db";
import { eq, desc, and, sql } from "drizzle-orm";

export interface IStorage {
  // User operations
  getUser(id: number): Promise<User | undefined>;
  getUserByUsername(username: string): Promise<User | undefined>;
  createUser(user: InsertUser): Promise<User>;
  
  // Stream operations
  getStreams(): Promise<Stream[]>;
  getStream(id: number): Promise<Stream | undefined>;
  getStreamByKey(streamKey: string): Promise<Stream | undefined>;
  createStream(stream: InsertStream): Promise<Stream>;
  updateStream(id: number, updates: Partial<Stream>): Promise<Stream | undefined>;
  deleteStream(id: number): Promise<boolean>;
  
  // Stream viewer operations
  addViewer(streamId: number, userId: number | null, sessionId: string): Promise<StreamViewer>;
  removeViewer(streamId: number, sessionId: string): Promise<boolean>;
  getActiveViewers(streamId: number): Promise<StreamViewer[]>;
  
  // Chat operations
  getChatMessages(streamId: number, limit?: number): Promise<ChatMessage[]>;
  addChatMessage(message: InsertChatMessage): Promise<ChatMessage>;
  
  // Analytics operations
  addAnalytics(analytics: InsertStreamAnalytics): Promise<StreamAnalytics>;
  getStreamAnalytics(streamId: number, hours?: number): Promise<StreamAnalytics[]>;
  
  // WebRTC operations
  addWebrtcConnection(connection: InsertWebrtcConnection): Promise<WebrtcConnection>;
  updateWebrtcConnection(id: number, updates: Partial<WebrtcConnection>): Promise<WebrtcConnection | undefined>;
  removeWebrtcConnection(id: number): Promise<boolean>;
  getActiveConnections(streamId: number): Promise<WebrtcConnection[]>;
}

export class DatabaseStorage implements IStorage {
  async getUser(id: number): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }

  async getUserByUsername(username: string): Promise<User | undefined> {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }

  async getStreams(): Promise<Stream[]> {
    return await db.select().from(streams).orderBy(desc(streams.createdAt));
  }

  async getStream(id: number): Promise<Stream | undefined> {
    const [stream] = await db.select().from(streams).where(eq(streams.id, id));
    return stream;
  }

  async getStreamByKey(streamKey: string): Promise<Stream | undefined> {
    const [stream] = await db.select().from(streams).where(eq(streams.streamKey, streamKey));
    return stream;
  }

  async createStream(insertStream: InsertStream): Promise<Stream> {
    const [stream] = await db.insert(streams).values(insertStream).returning();
    return stream;
  }

  async updateStream(id: number, updates: Partial<Stream>): Promise<Stream | undefined> {
    const [stream] = await db
      .update(streams)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(streams.id, id))
      .returning();
    return stream;
  }

  async deleteStream(id: number): Promise<boolean> {
    const result = await db.delete(streams).where(eq(streams.id, id));
    return result.rowCount > 0;
  }

  async addViewer(streamId: number, userId: number | null, sessionId: string): Promise<StreamViewer> {
    const [viewer] = await db
      .insert(streamViewers)
      .values({ streamId, userId, sessionId })
      .returning();
    return viewer;
  }

  async removeViewer(streamId: number, sessionId: string): Promise<boolean> {
    const result = await db
      .update(streamViewers)
      .set({ isActive: false, leftAt: new Date() })
      .where(and(eq(streamViewers.streamId, streamId), eq(streamViewers.sessionId, sessionId)));
    return result.rowCount > 0;
  }

  async getActiveViewers(streamId: number): Promise<StreamViewer[]> {
    return await db
      .select()
      .from(streamViewers)
      .where(and(eq(streamViewers.streamId, streamId), eq(streamViewers.isActive, true)));
  }

  async getChatMessages(streamId: number, limit = 50): Promise<ChatMessage[]> {
    return await db
      .select()
      .from(chatMessages)
      .where(eq(chatMessages.streamId, streamId))
      .orderBy(desc(chatMessages.timestamp))
      .limit(limit);
  }

  async addChatMessage(message: InsertChatMessage): Promise<ChatMessage> {
    const [chatMessage] = await db.insert(chatMessages).values(message).returning();
    return chatMessage;
  }

  async addAnalytics(analytics: InsertStreamAnalytics): Promise<StreamAnalytics> {
    const [streamAnalytics] = await db.insert(streamAnalytics).values(analytics).returning();
    return streamAnalytics;
  }

  async getStreamAnalytics(streamId: number, hours = 24): Promise<StreamAnalytics[]> {
    const hoursAgo = new Date(Date.now() - hours * 60 * 60 * 1000);
    return await db
      .select()
      .from(streamAnalytics)
      .where(
        and(
          eq(streamAnalytics.streamId, streamId),
          sql`${streamAnalytics.timestamp} >= ${hoursAgo}`
        )
      )
      .orderBy(desc(streamAnalytics.timestamp));
  }

  async addWebrtcConnection(connection: InsertWebrtcConnection): Promise<WebrtcConnection> {
    const [webrtcConnection] = await db.insert(webrtcConnections).values(connection).returning();
    return webrtcConnection;
  }

  async updateWebrtcConnection(id: number, updates: Partial<WebrtcConnection>): Promise<WebrtcConnection | undefined> {
    const [connection] = await db
      .update(webrtcConnections)
      .set({ ...updates, updatedAt: new Date() })
      .where(eq(webrtcConnections.id, id))
      .returning();
    return connection;
  }

  async removeWebrtcConnection(id: number): Promise<boolean> {
    const result = await db.delete(webrtcConnections).where(eq(webrtcConnections.id, id));
    return result.rowCount > 0;
  }

  async getActiveConnections(streamId: number): Promise<WebrtcConnection[]> {
    return await db
      .select()
      .from(webrtcConnections)
      .where(
        and(
          eq(webrtcConnections.streamId, streamId),
          eq(webrtcConnections.status, "connected")
        )
      );
  }
}

export const storage = new DatabaseStorage();

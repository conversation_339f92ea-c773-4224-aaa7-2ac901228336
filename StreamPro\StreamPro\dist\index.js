var __defProp = Object.defineProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};

// server/index.ts
import express2 from "express";

// server/routes.ts
import { createServer } from "http";
import { WebSocketServer, WebSocket } from "ws";

// shared/schema.ts
var schema_exports = {};
__export(schema_exports, {
  aiStreamAssistance: () => aiStreamAssistance,
  chatMessages: () => chatMessages,
  chatMessagesRelations: () => chatMessagesRelations,
  gameCategories: () => gameCategories,
  insertChatMessageSchema: () => insertChatMessageSchema,
  insertStreamAnalyticsSchema: () => insertStreamAnalyticsSchema,
  insertStreamSchema: () => insertStreamSchema,
  insertUserSchema: () => insertUserSchema,
  insertWebrtcConnectionSchema: () => insertWebrtcConnectionSchema,
  streamAnalytics: () => streamAnalytics,
  streamAnalyticsRelations: () => streamAnalyticsRelations,
  streamGames: () => streamGames,
  streamPerformanceMetrics: () => streamPerformanceMetrics,
  streamViewers: () => streamViewers,
  streamViewersRelations: () => streamViewersRelations,
  streams: () => streams,
  streamsRelations: () => streamsRelations,
  users: () => users,
  usersRelations: () => usersRelations,
  webrtcConnections: () => webrtcConnections,
  webrtcConnectionsRelations: () => webrtcConnectionsRelations
});
import { pgTable, text, serial, integer, boolean, timestamp } from "drizzle-orm/pg-core";
import { relations } from "drizzle-orm";
import { createInsertSchema } from "drizzle-zod";
var users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email").notNull().unique(),
  password: text("password").notNull(),
  role: text("role").notNull().default("user"),
  firstName: text("first_name"),
  lastName: text("last_name"),
  profileImageUrl: text("profile_image_url"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var streams = pgTable("gaming_streams", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").references(() => users.id).notNull(),
  title: text("title").notNull(),
  description: text("description"),
  streamKey: text("stream_key").notNull().unique(),
  status: text("status").notNull().default("inactive"),
  // inactive, live, scheduled, ended
  gameCategory: text("game_category").default("general"),
  // game-dev, gameplay, tutorial, competitive
  gameTitle: text("game_title"),
  // specific game being played/developed
  quality: text("quality").notNull().default("720p"),
  bitrate: integer("bitrate").default(2500),
  protocol: text("protocol").notNull().default("webrtc"),
  isRecording: boolean("is_recording").default(false),
  recordingUrl: text("recording_url"),
  thumbnailUrl: text("thumbnail_url"),
  viewerCount: integer("viewer_count").default(0),
  maxViewers: integer("max_viewers").default(0),
  aiAssistanceEnabled: boolean("ai_assistance_enabled").default(false),
  startedAt: timestamp("started_at"),
  endedAt: timestamp("ended_at"),
  scheduledAt: timestamp("scheduled_at"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var gameCategories = pgTable("game_categories", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(),
  description: text("description"),
  icon: text("icon"),
  isActive: boolean("is_active").default(true),
  createdAt: timestamp("created_at").defaultNow()
});
var streamGames = pgTable("stream_games", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  gameTitle: text("game_title").notNull(),
  gameGenre: text("game_genre"),
  gameEngine: text("game_engine"),
  // unity, unreal, html5, etc.
  developmentStage: text("development_stage"),
  // concept, development, testing, released
  repositoryUrl: text("repository_url"),
  playableUrl: text("playable_url"),
  createdAt: timestamp("created_at").defaultNow()
});
var streamAnalytics = pgTable("stream_analytics", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  viewerCount: integer("viewer_count").default(0),
  chatMessages: integer("chat_messages").default(0),
  aiInteractions: integer("ai_interactions").default(0),
  qualityChanges: integer("quality_changes").default(0),
  avgWatchTime: integer("avg_watch_time").default(0)
  // in seconds
});
var aiStreamAssistance = pgTable("ai_stream_assistance", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  assistanceType: text("assistance_type").notNull(),
  // code-help, game-tips, moderation, etc.
  prompt: text("prompt").notNull(),
  response: text("response").notNull(),
  isPublic: boolean("is_public").default(false),
  // visible to stream viewers
  createdAt: timestamp("created_at").defaultNow()
});
var streamViewers = pgTable("stream_viewers", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  userId: integer("user_id").references(() => users.id),
  sessionId: text("session_id").notNull(),
  joinedAt: timestamp("joined_at").defaultNow(),
  leftAt: timestamp("left_at"),
  isActive: boolean("is_active").default(true)
});
var chatMessages = pgTable("chat_messages", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  userId: integer("user_id").references(() => users.id).notNull(),
  message: text("message").notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  type: text("type").notNull().default("message")
  // message, system, moderator
});
var streamPerformanceMetrics = pgTable("stream_performance_metrics", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  timestamp: timestamp("timestamp").defaultNow(),
  viewerCount: integer("viewer_count").default(0),
  bitrate: integer("bitrate").default(0),
  latency: integer("latency").default(0),
  cpuUsage: integer("cpu_usage").default(0),
  memoryUsage: integer("memory_usage").default(0),
  bandwidth: integer("bandwidth").default(0)
});
var webrtcConnections = pgTable("webrtc_connections", {
  id: serial("id").primaryKey(),
  streamId: integer("stream_id").references(() => streams.id).notNull(),
  userId: integer("user_id").references(() => users.id),
  peerId: text("peer_id").notNull(),
  connectionType: text("connection_type").notNull(),
  // publisher, subscriber
  status: text("status").notNull().default("connecting"),
  // connecting, connected, disconnected, failed
  iceConnectionState: text("ice_connection_state"),
  connectionState: text("connection_state"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow()
});
var usersRelations = relations(users, ({ many }) => ({
  streams: many(streams),
  streamViewers: many(streamViewers),
  chatMessages: many(chatMessages),
  webrtcConnections: many(webrtcConnections)
}));
var streamsRelations = relations(streams, ({ one, many }) => ({
  user: one(users, { fields: [streams.userId], references: [users.id] }),
  viewers: many(streamViewers),
  chatMessages: many(chatMessages),
  analytics: many(streamAnalytics),
  webrtcConnections: many(webrtcConnections)
}));
var streamViewersRelations = relations(streamViewers, ({ one }) => ({
  stream: one(streams, { fields: [streamViewers.streamId], references: [streams.id] }),
  user: one(users, { fields: [streamViewers.userId], references: [users.id] })
}));
var chatMessagesRelations = relations(chatMessages, ({ one }) => ({
  stream: one(streams, { fields: [chatMessages.streamId], references: [streams.id] }),
  user: one(users, { fields: [chatMessages.userId], references: [users.id] })
}));
var streamAnalyticsRelations = relations(streamAnalytics, ({ one }) => ({
  stream: one(streams, { fields: [streamAnalytics.streamId], references: [streams.id] })
}));
var webrtcConnectionsRelations = relations(webrtcConnections, ({ one }) => ({
  stream: one(streams, { fields: [webrtcConnections.streamId], references: [streams.id] }),
  user: one(users, { fields: [webrtcConnections.userId], references: [users.id] })
}));
var insertUserSchema = createInsertSchema(users).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});
var insertStreamSchema = createInsertSchema(streams).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});
var insertChatMessageSchema = createInsertSchema(chatMessages).omit({
  id: true,
  timestamp: true
});
var insertStreamAnalyticsSchema = createInsertSchema(streamAnalytics).omit({
  id: true,
  timestamp: true
});
var insertWebrtcConnectionSchema = createInsertSchema(webrtcConnections).omit({
  id: true,
  createdAt: true,
  updatedAt: true
});

// server/db.ts
import { Pool, neonConfig } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-serverless";
import ws from "ws";
neonConfig.webSocketConstructor = ws;
if (!process.env.DATABASE_URL) {
  console.warn("DATABASE_URL not set. Using demo mode with mock data.");
  process.env.DATABASE_URL = "postgresql://demo:demo@localhost:5432/findz_streaming_demo";
}
var pool = new Pool({ connectionString: process.env.DATABASE_URL });
var db = drizzle({ client: pool, schema: schema_exports });

// server/storage.ts
import { eq, desc, and, sql } from "drizzle-orm";
var DatabaseStorage = class {
  async getUser(id) {
    const [user] = await db.select().from(users).where(eq(users.id, id));
    return user;
  }
  async getUserByUsername(username) {
    const [user] = await db.select().from(users).where(eq(users.username, username));
    return user;
  }
  async createUser(insertUser) {
    const [user] = await db.insert(users).values(insertUser).returning();
    return user;
  }
  async getStreams() {
    return await db.select().from(streams).orderBy(desc(streams.createdAt));
  }
  async getStream(id) {
    const [stream] = await db.select().from(streams).where(eq(streams.id, id));
    return stream;
  }
  async getStreamByKey(streamKey) {
    const [stream] = await db.select().from(streams).where(eq(streams.streamKey, streamKey));
    return stream;
  }
  async createStream(insertStream) {
    const [stream] = await db.insert(streams).values(insertStream).returning();
    return stream;
  }
  async updateStream(id, updates) {
    const [stream] = await db.update(streams).set({ ...updates, updatedAt: /* @__PURE__ */ new Date() }).where(eq(streams.id, id)).returning();
    return stream;
  }
  async deleteStream(id) {
    const result = await db.delete(streams).where(eq(streams.id, id));
    return result.rowCount > 0;
  }
  async addViewer(streamId, userId, sessionId) {
    const [viewer] = await db.insert(streamViewers).values({ streamId, userId, sessionId }).returning();
    return viewer;
  }
  async removeViewer(streamId, sessionId) {
    const result = await db.update(streamViewers).set({ isActive: false, leftAt: /* @__PURE__ */ new Date() }).where(and(eq(streamViewers.streamId, streamId), eq(streamViewers.sessionId, sessionId)));
    return result.rowCount > 0;
  }
  async getActiveViewers(streamId) {
    return await db.select().from(streamViewers).where(and(eq(streamViewers.streamId, streamId), eq(streamViewers.isActive, true)));
  }
  async getChatMessages(streamId, limit = 50) {
    return await db.select().from(chatMessages).where(eq(chatMessages.streamId, streamId)).orderBy(desc(chatMessages.timestamp)).limit(limit);
  }
  async addChatMessage(message) {
    const [chatMessage] = await db.insert(chatMessages).values(message).returning();
    return chatMessage;
  }
  async addAnalytics(analytics) {
    const [streamAnalytics2] = await db.insert(streamAnalytics2).values(analytics).returning();
    return streamAnalytics2;
  }
  async getStreamAnalytics(streamId, hours = 24) {
    const hoursAgo = new Date(Date.now() - hours * 60 * 60 * 1e3);
    return await db.select().from(streamAnalytics).where(
      and(
        eq(streamAnalytics.streamId, streamId),
        sql`${streamAnalytics.timestamp} >= ${hoursAgo}`
      )
    ).orderBy(desc(streamAnalytics.timestamp));
  }
  async addWebrtcConnection(connection) {
    const [webrtcConnection] = await db.insert(webrtcConnections).values(connection).returning();
    return webrtcConnection;
  }
  async updateWebrtcConnection(id, updates) {
    const [connection] = await db.update(webrtcConnections).set({ ...updates, updatedAt: /* @__PURE__ */ new Date() }).where(eq(webrtcConnections.id, id)).returning();
    return connection;
  }
  async removeWebrtcConnection(id) {
    const result = await db.delete(webrtcConnections).where(eq(webrtcConnections.id, id));
    return result.rowCount > 0;
  }
  async getActiveConnections(streamId) {
    return await db.select().from(webrtcConnections).where(
      and(
        eq(webrtcConnections.streamId, streamId),
        eq(webrtcConnections.status, "connected")
      )
    );
  }
};
var storage = new DatabaseStorage();

// server/routes.ts
async function registerRoutes(app2) {
  const httpServer = createServer(app2);
  const wss = new WebSocketServer({ server: httpServer, path: "/ws" });
  const connections = /* @__PURE__ */ new Map();
  const streamConnections = /* @__PURE__ */ new Map();
  wss.on("connection", (ws2, req) => {
    const connectionId = Math.random().toString(36).substring(7);
    connections.set(connectionId, ws2);
    ws2.on("message", async (message) => {
      try {
        const data = JSON.parse(message.toString());
        switch (data.type) {
          case "join_stream":
            const streamId = data.streamId;
            if (!streamConnections.has(streamId)) {
              streamConnections.set(streamId, /* @__PURE__ */ new Set());
            }
            streamConnections.get(streamId)?.add(connectionId);
            await storage.addViewer(streamId, data.userId, connectionId);
            broadcastToStream(streamId, {
              type: "viewer_count_update",
              count: streamConnections.get(streamId)?.size || 0
            });
            break;
          case "leave_stream":
            const leaveStreamId = data.streamId;
            streamConnections.get(leaveStreamId)?.delete(connectionId);
            await storage.removeViewer(leaveStreamId, connectionId);
            broadcastToStream(leaveStreamId, {
              type: "viewer_count_update",
              count: streamConnections.get(leaveStreamId)?.size || 0
            });
            break;
          case "chat_message":
            const chatMessage = await storage.addChatMessage({
              streamId: data.streamId,
              userId: data.userId,
              message: data.message,
              type: data.messageType || "message"
            });
            broadcastToStream(data.streamId, {
              type: "chat_message",
              message: chatMessage
            });
            break;
          case "webrtc_offer":
          case "webrtc_answer":
          case "webrtc_ice_candidate":
            const targetConnectionId = data.targetConnectionId;
            const targetWs = connections.get(targetConnectionId);
            if (targetWs && targetWs.readyState === WebSocket.OPEN) {
              targetWs.send(JSON.stringify(data));
            }
            break;
        }
      } catch (error) {
        console.error("WebSocket message error:", error);
      }
    });
    ws2.on("close", () => {
      connections.delete(connectionId);
      streamConnections.forEach((connectionSet, streamId) => {
        if (connectionSet.has(connectionId)) {
          connectionSet.delete(connectionId);
          broadcastToStream(streamId, {
            type: "viewer_count_update",
            count: connectionSet.size
          });
        }
      });
    });
  });
  function broadcastToStream(streamId, message) {
    const streamConnectionSet = streamConnections.get(streamId);
    if (streamConnectionSet) {
      streamConnectionSet.forEach((connectionId) => {
        const ws2 = connections.get(connectionId);
        if (ws2 && ws2.readyState === WebSocket.OPEN) {
          ws2.send(JSON.stringify(message));
        }
      });
    }
  }
  app2.get("/api/streams", async (req, res) => {
    try {
      const streams2 = await storage.getStreams();
      res.json(streams2);
    } catch (error) {
      console.error("Error fetching streams:", error);
      res.status(500).json({ message: "Failed to fetch streams" });
    }
  });
  app2.get("/api/streams/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const stream = await storage.getStream(id);
      if (!stream) {
        return res.status(404).json({ message: "Stream not found" });
      }
      res.json(stream);
    } catch (error) {
      console.error("Error fetching stream:", error);
      res.status(500).json({ message: "Failed to fetch stream" });
    }
  });
  app2.post("/api/streams", async (req, res) => {
    try {
      const streamData = insertStreamSchema.parse(req.body);
      const streamKey = generateStreamKey();
      const stream = await storage.createStream({
        ...streamData,
        streamKey
      });
      res.status(201).json(stream);
    } catch (error) {
      console.error("Error creating stream:", error);
      res.status(500).json({ message: "Failed to create stream" });
    }
  });
  app2.put("/api/streams/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      const stream = await storage.updateStream(id, updates);
      if (!stream) {
        return res.status(404).json({ message: "Stream not found" });
      }
      res.json(stream);
    } catch (error) {
      console.error("Error updating stream:", error);
      res.status(500).json({ message: "Failed to update stream" });
    }
  });
  app2.delete("/api/streams/:id", async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteStream(id);
      if (!success) {
        return res.status(404).json({ message: "Stream not found" });
      }
      res.status(204).send();
    } catch (error) {
      console.error("Error deleting stream:", error);
      res.status(500).json({ message: "Failed to delete stream" });
    }
  });
  app2.get("/api/streams/:id/chat", async (req, res) => {
    try {
      const streamId = parseInt(req.params.id);
      const limit = parseInt(req.query.limit) || 50;
      const messages = await storage.getChatMessages(streamId, limit);
      res.json(messages);
    } catch (error) {
      console.error("Error fetching chat messages:", error);
      res.status(500).json({ message: "Failed to fetch chat messages" });
    }
  });
  app2.get("/api/streams/:id/analytics", async (req, res) => {
    try {
      const streamId = parseInt(req.params.id);
      const hours = parseInt(req.query.hours) || 24;
      const analytics = await storage.getStreamAnalytics(streamId, hours);
      res.json(analytics);
    } catch (error) {
      console.error("Error fetching analytics:", error);
      res.status(500).json({ message: "Failed to fetch analytics" });
    }
  });
  app2.post("/api/streams/:id/analytics", async (req, res) => {
    try {
      const streamId = parseInt(req.params.id);
      const analyticsData = insertStreamAnalyticsSchema.parse({
        ...req.body,
        streamId
      });
      const analytics = await storage.addAnalytics(analyticsData);
      res.status(201).json(analytics);
    } catch (error) {
      console.error("Error adding analytics:", error);
      res.status(500).json({ message: "Failed to add analytics" });
    }
  });
  app2.get("/api/stats", async (req, res) => {
    try {
      const streams2 = await storage.getStreams();
      const activeStreams = streams2.filter((s) => s.status === "live");
      const totalViewers = activeStreams.reduce((sum, s) => sum + (s.viewerCount || 0), 0);
      res.json({
        activeStreams: activeStreams.length,
        totalViewers,
        totalStreams: streams2.length,
        avgLatency: "2.3s",
        serverLoad: "67%"
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
      res.status(500).json({ message: "Failed to fetch stats" });
    }
  });
  return httpServer;
}
function generateStreamKey() {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

// server/vite.ts
import express from "express";
import fs from "fs";
import path2 from "path";
import { createServer as createViteServer, createLogger } from "vite";

// vite.config.ts
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import path from "path";
import runtimeErrorOverlay from "@replit/vite-plugin-runtime-error-modal";
var vite_config_default = defineConfig({
  plugins: [
    react(),
    runtimeErrorOverlay(),
    ...process.env.NODE_ENV !== "production" && process.env.REPL_ID !== void 0 ? [
      await import("@replit/vite-plugin-cartographer").then(
        (m) => m.cartographer()
      )
    ] : []
  ],
  resolve: {
    alias: {
      "@": path.resolve(import.meta.dirname, "client", "src"),
      "@shared": path.resolve(import.meta.dirname, "shared"),
      "@assets": path.resolve(import.meta.dirname, "attached_assets")
    }
  },
  root: path.resolve(import.meta.dirname, "client"),
  build: {
    outDir: path.resolve(import.meta.dirname, "dist/public"),
    emptyOutDir: true
  },
  server: {
    fs: {
      strict: true,
      deny: ["**/.*"]
    }
  }
});

// server/vite.ts
import { nanoid } from "nanoid";
var viteLogger = createLogger();
function log(message, source = "express") {
  const formattedTime = (/* @__PURE__ */ new Date()).toLocaleTimeString("en-US", {
    hour: "numeric",
    minute: "2-digit",
    second: "2-digit",
    hour12: true
  });
  console.log(`${formattedTime} [${source}] ${message}`);
}
async function setupVite(app2, server) {
  const serverOptions = {
    middlewareMode: true,
    hmr: { server },
    allowedHosts: true
  };
  const vite = await createViteServer({
    ...vite_config_default,
    configFile: false,
    customLogger: {
      ...viteLogger,
      error: (msg, options) => {
        viteLogger.error(msg, options);
        process.exit(1);
      }
    },
    server: serverOptions,
    appType: "custom"
  });
  app2.use(vite.middlewares);
  app2.use("*", async (req, res, next) => {
    const url = req.originalUrl;
    try {
      const clientTemplate = path2.resolve(
        import.meta.dirname,
        "..",
        "client",
        "index.html"
      );
      let template = await fs.promises.readFile(clientTemplate, "utf-8");
      template = template.replace(
        `src="/src/main.tsx"`,
        `src="/src/main.tsx?v=${nanoid()}"`
      );
      const page = await vite.transformIndexHtml(url, template);
      res.status(200).set({ "Content-Type": "text/html" }).end(page);
    } catch (e) {
      vite.ssrFixStacktrace(e);
      next(e);
    }
  });
}
function serveStatic(app2) {
  const distPath = path2.resolve(import.meta.dirname, "public");
  if (!fs.existsSync(distPath)) {
    throw new Error(
      `Could not find the build directory: ${distPath}, make sure to build the client first`
    );
  }
  app2.use(express.static(distPath));
  app2.use("*", (_req, res) => {
    res.sendFile(path2.resolve(distPath, "index.html"));
  });
}

// server/index.ts
var app = express2();
app.use(express2.json());
app.use(express2.urlencoded({ extended: false }));
app.use((req, res, next) => {
  const start = Date.now();
  const path3 = req.path;
  let capturedJsonResponse = void 0;
  const originalResJson = res.json;
  res.json = function(bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };
  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path3.startsWith("/api")) {
      let logLine = `${req.method} ${path3} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }
      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "\u2026";
      }
      log(logLine);
    }
  });
  next();
});
(async () => {
  const server = await registerRoutes(app);
  app.use((err, _req, res, _next) => {
    const status = err.status || err.statusCode || 500;
    const message = err.message || "Internal Server Error";
    res.status(status).json({ message });
    throw err;
  });
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }
  const port = 5e3;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true
  }, () => {
    log(`serving on port ${port}`);
  });
})();

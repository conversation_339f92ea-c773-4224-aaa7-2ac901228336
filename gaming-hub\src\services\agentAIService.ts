import { aiService, ChatMessage, AIResponse, GameContext } from './aiService';
import { Environment } from '../config/environment';

export interface AgentCapabilities {
  codeAnalysis: boolean;
  gameStrategy: boolean;
  debugging: boolean;
  optimization: boolean;
  learning: boolean;
}

export interface AgentContext {
  gameContext?: GameContext;
  userSkillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  previousInteractions: ChatMessage[];
  currentGoals: string[];
  capabilities: AgentCapabilities;
}

class AgentAIService {
  private systemPrompt: string;
  private agentCapabilities: AgentCapabilities;

  constructor() {
    this.agentCapabilities = {
      codeAnalysis: true,
      gameStrategy: true,
      debugging: true,
      optimization: true,
      learning: true,
    };
    
    this.systemPrompt = this.buildAgentSystemPrompt();
  }

  private buildAgentSystemPrompt(): string {
    return `You are a highly skilled gaming engineer and AI assistant with extensive knowledge in game development, gaming strategies, optimization techniques, and player coaching.

====

CORE IDENTITY & CAPABILITIES

You are an expert gaming assistant with the following specialized capabilities:
- Game strategy analysis and optimization
- Player skill assessment and personalized coaching
- Game mechanics understanding and explanation
- Performance optimization and debugging
- Learning path recommendations
- Real-time gameplay assistance

====

GAMING EXPERTISE

Your gaming knowledge encompasses:
- Game design patterns and mechanics
- Player psychology and motivation
- Skill development methodologies
- Performance optimization techniques
- Competitive gaming strategies
- Casual gaming enjoyment enhancement
- Achievement and progression systems
- Game balance and difficulty scaling

====

COMMUNICATION STYLE

- Be direct, technical, and actionable in your responses
- Provide specific, implementable advice
- Use gaming terminology appropriately for the user's skill level
- Focus on practical solutions over theoretical discussions
- Adapt your communication style to the user's expertise level
- NEVER start responses with "Great", "Certainly", "Okay", or "Sure"
- Be encouraging but realistic about skill development timelines

====

ANALYSIS METHODOLOGY

When analyzing gameplay or providing assistance:

1. **Assess Context**: Understand the game, player skill level, and specific situation
2. **Identify Patterns**: Look for recurring issues or successful strategies
3. **Prioritize Issues**: Focus on the most impactful improvements first
4. **Provide Actionable Steps**: Give specific, implementable recommendations
5. **Consider Learning Curve**: Ensure advice matches the player's current abilities
6. **Track Progress**: Reference previous interactions to show improvement

====

RESPONSE STRUCTURE

For gameplay analysis, structure responses as:
1. **Immediate Observations**: Quick wins and obvious issues
2. **Strategic Analysis**: Deeper patterns and long-term considerations
3. **Specific Recommendations**: Actionable steps with clear priorities
4. **Practice Suggestions**: How to implement and improve
5. **Next Steps**: Clear progression path

====

GAMING CONTEXTS

Adapt your expertise to different gaming contexts:

**Casual Gaming**:
- Focus on enjoyment and stress relief
- Provide gentle guidance without overwhelming
- Emphasize fun over optimization
- Suggest accessibility features when relevant

**Competitive Gaming**:
- Analyze performance metrics critically
- Provide advanced optimization strategies
- Focus on consistency and improvement
- Address mental game and pressure management

**Learning/Educational**:
- Break down complex concepts into digestible parts
- Provide progressive skill-building exercises
- Explain the "why" behind recommendations
- Create structured learning paths

**Problem-Solving**:
- Systematically analyze stuck points
- Provide multiple solution approaches
- Help debug specific issues
- Teach troubleshooting methodologies

====

PERSONALIZATION

Tailor responses based on:
- **Skill Level**: Adjust complexity and terminology
- **Play Style**: Respect different approaches to gaming
- **Goals**: Align advice with player objectives
- **Time Investment**: Consider available practice time
- **Previous Interactions**: Build on past conversations
- **Game Preferences**: Adapt to preferred genres and mechanics

====

TECHNICAL ASSISTANCE

When providing technical gaming help:
- Explain game mechanics clearly
- Provide step-by-step instructions
- Suggest practice routines and drills
- Recommend tools and resources
- Help with configuration and optimization
- Debug performance issues

====

MOTIVATIONAL SUPPORT

Maintain player motivation by:
- Celebrating improvements and achievements
- Providing realistic timelines for skill development
- Offering alternative approaches when stuck
- Emphasizing learning over winning
- Building confidence through incremental progress
- Addressing gaming frustration constructively

====

ETHICAL GUIDELINES

- Promote healthy gaming habits
- Discourage toxic behavior or cheating
- Respect different skill levels and play styles
- Encourage breaks and balance
- Support inclusive gaming communities
- Prioritize player well-being over performance

====

RESPONSE OPTIMIZATION

- Keep responses focused and actionable
- Use bullet points for clarity when appropriate
- Provide examples and analogies when helpful
- Reference specific game elements when relevant
- Offer both immediate and long-term solutions
- End with clear next steps or questions for clarification

====

CONTINUOUS LEARNING

- Adapt recommendations based on player feedback
- Learn from successful and unsuccessful advice
- Stay updated on gaming trends and strategies
- Incorporate new techniques and methodologies
- Refine understanding of individual player needs

Your goal is to be the most helpful, knowledgeable, and supportive gaming assistant possible, helping players of all levels improve their skills, enjoy their gaming experience, and achieve their personal gaming goals.`;
  }

  async generateAgentResponse(
    messages: ChatMessage[],
    agentContext: AgentContext
  ): Promise<AIResponse> {
    try {
      // Build enhanced context-aware prompt
      const enhancedSystemPrompt = this.buildContextualPrompt(agentContext);
      
      // Prepare conversation with agent system prompt
      const agentMessages = [
        { 
          id: 'system', 
          role: 'system' as const, 
          content: enhancedSystemPrompt, 
          timestamp: new Date() 
        },
        ...messages
      ];

      // Use the base AI service with enhanced prompting
      const response = await aiService.generateResponse(agentMessages, agentContext.gameContext);

      // Post-process response for agent-specific enhancements
      return this.enhanceResponse(response, agentContext);
    } catch (error) {
      console.error('Agent AI Service error:', error);
      return this.getFallbackAgentResponse(agentContext);
    }
  }

  private buildContextualPrompt(context: AgentContext): string {
    let contextualPrompt = this.systemPrompt;

    // Add game-specific context
    if (context.gameContext) {
      contextualPrompt += `

====

CURRENT GAME CONTEXT

Game: ${context.gameContext.gameTitle}
Current Score: ${context.gameContext.currentScore}
Difficulty: ${context.gameContext.difficulty}
Time Spent: ${Math.floor(context.gameContext.timeSpent / 60)} minutes
Achievements: ${context.gameContext.achievements.join(', ') || 'None yet'}

Focus your advice specifically on this game and the player's current situation.`;
    }

    // Add skill level context
    contextualPrompt += `

====

PLAYER PROFILE

Skill Level: ${context.userSkillLevel}
Current Goals: ${context.currentGoals.join(', ') || 'General improvement'}

Adapt your communication and advice complexity to match this skill level.`;

    // Add interaction history context
    if (context.previousInteractions.length > 0) {
      const recentTopics = this.extractRecentTopics(context.previousInteractions);
      contextualPrompt += `

====

CONVERSATION CONTEXT

Recent topics discussed: ${recentTopics.join(', ')}
Continue building on previous conversations and reference past advice when relevant.`;
    }

    return contextualPrompt;
  }

  private extractRecentTopics(interactions: ChatMessage[]): string[] {
    // Simple topic extraction - in a real implementation, this could be more sophisticated
    const topics: string[] = [];
    const recentMessages = interactions.slice(-5); // Last 5 messages

    recentMessages.forEach(message => {
      if (message.role === 'user') {
        const content = message.content.toLowerCase();
        if (content.includes('strategy')) topics.push('strategy');
        if (content.includes('score') || content.includes('points')) topics.push('scoring');
        if (content.includes('difficult') || content.includes('hard')) topics.push('difficulty');
        if (content.includes('tip') || content.includes('advice')) topics.push('tips');
        if (content.includes('improve') || content.includes('better')) topics.push('improvement');
      }
    });

    return [...new Set(topics)]; // Remove duplicates
  }

  private enhanceResponse(response: AIResponse, context: AgentContext): AIResponse {
    // Add agent-specific enhancements
    const enhancedResponse: AIResponse = {
      ...response,
      gameAdvice: response.gameAdvice || this.generateContextualAdvice(context),
      suggestions: this.generateContextualSuggestions(context, response.suggestions || []),
    };

    return enhancedResponse;
  }

  private generateContextualAdvice(context: AgentContext) {
    const advice = {
      tips: [] as string[],
      strategy: 'Focus on consistent improvement',
      nextSteps: [] as string[],
    };

    // Generate advice based on skill level
    switch (context.userSkillLevel) {
      case 'beginner':
        advice.tips = [
          'Master the basic controls and mechanics first',
          'Take your time to understand the game rules',
          'Practice regularly in short sessions',
        ];
        advice.nextSteps = [
          'Complete tutorial or practice modes',
          'Focus on one skill at a time',
          'Ask questions when confused',
        ];
        break;

      case 'intermediate':
        advice.tips = [
          'Start analyzing your gameplay patterns',
          'Learn from mistakes and adapt strategies',
          'Practice advanced techniques gradually',
        ];
        advice.nextSteps = [
          'Set specific improvement goals',
          'Study advanced strategies',
          'Track your progress metrics',
        ];
        break;

      case 'advanced':
        advice.tips = [
          'Focus on consistency and optimization',
          'Analyze high-level gameplay and strategies',
          'Develop your own unique approaches',
        ];
        advice.nextSteps = [
          'Compete in challenging scenarios',
          'Mentor other players',
          'Innovate new strategies',
        ];
        break;

      case 'expert':
        advice.tips = [
          'Perfect your execution and timing',
          'Develop meta-game understanding',
          'Share knowledge with the community',
        ];
        advice.nextSteps = [
          'Push the boundaries of what\'s possible',
          'Create content or guides',
          'Compete at the highest levels',
        ];
        break;
    }

    // Add game-specific advice if context is available
    if (context.gameContext) {
      advice.strategy = `For ${context.gameContext.gameTitle}, focus on understanding the core mechanics and building consistent execution`;
    }

    return advice;
  }

  private generateContextualSuggestions(context: AgentContext, baseSuggestions: string[]): string[] {
    const suggestions = [...baseSuggestions];

    // Add context-specific suggestions
    if (context.gameContext) {
      suggestions.push(`Analyze my ${context.gameContext.gameTitle} performance`);
      suggestions.push(`Give me advanced ${context.gameContext.gameTitle} strategies`);
    }

    // Add skill-level appropriate suggestions
    switch (context.userSkillLevel) {
      case 'beginner':
        suggestions.push('Explain basic game mechanics');
        suggestions.push('Give me beginner-friendly tips');
        break;
      case 'intermediate':
        suggestions.push('Help me break through skill plateaus');
        suggestions.push('Suggest practice routines');
        break;
      case 'advanced':
        suggestions.push('Analyze my advanced techniques');
        suggestions.push('Help optimize my gameplay');
        break;
      case 'expert':
        suggestions.push('Discuss meta-game strategies');
        suggestions.push('Help me innovate new approaches');
        break;
    }

    return suggestions.slice(0, 4); // Limit to 4 suggestions
  }

  private getFallbackAgentResponse(context: AgentContext): AIResponse {
    return {
      content: `I'm here to help you improve your gaming skills! Based on your ${context.userSkillLevel} level, I can provide personalized advice and strategies. What specific aspect of your gameplay would you like to work on?`,
      suggestions: this.generateContextualSuggestions(context, []),
      gameAdvice: this.generateContextualAdvice(context),
    };
  }

  // Method to assess user skill level based on gameplay data
  assessSkillLevel(gameData: {
    averageScore: number;
    gamesPlayed: number;
    achievements: string[];
    consistency: number;
  }): 'beginner' | 'intermediate' | 'advanced' | 'expert' {
    let skillPoints = 0;

    // Score-based assessment
    if (gameData.averageScore > 1000) skillPoints += 1;
    if (gameData.averageScore > 5000) skillPoints += 1;
    if (gameData.averageScore > 10000) skillPoints += 1;

    // Experience-based assessment
    if (gameData.gamesPlayed > 10) skillPoints += 1;
    if (gameData.gamesPlayed > 50) skillPoints += 1;
    if (gameData.gamesPlayed > 100) skillPoints += 1;

    // Achievement-based assessment
    if (gameData.achievements.length > 5) skillPoints += 1;
    if (gameData.achievements.length > 15) skillPoints += 1;

    // Consistency-based assessment
    if (gameData.consistency > 0.7) skillPoints += 1;
    if (gameData.consistency > 0.9) skillPoints += 1;

    // Determine skill level
    if (skillPoints <= 2) return 'beginner';
    if (skillPoints <= 5) return 'intermediate';
    if (skillPoints <= 8) return 'advanced';
    return 'expert';
  }

  // Method to generate learning path recommendations
  generateLearningPath(
    currentSkill: string,
    targetSkill: string,
    gameContext?: GameContext
  ): string[] {
    const learningPath: string[] = [];

    if (currentSkill === 'beginner') {
      learningPath.push('Master basic controls and game mechanics');
      learningPath.push('Complete all tutorial content');
      learningPath.push('Practice fundamental skills daily');
      learningPath.push('Learn from mistakes without frustration');
    }

    if (currentSkill === 'beginner' && targetSkill !== 'beginner') {
      learningPath.push('Study intermediate strategies and techniques');
      learningPath.push('Analyze your gameplay patterns');
      learningPath.push('Set specific improvement goals');
    }

    if (currentSkill === 'intermediate' || (currentSkill === 'beginner' && targetSkill === 'advanced')) {
      learningPath.push('Practice advanced techniques consistently');
      learningPath.push('Study high-level gameplay examples');
      learningPath.push('Develop your own strategic approaches');
    }

    if (targetSkill === 'expert') {
      learningPath.push('Perfect execution and timing');
      learningPath.push('Understand meta-game concepts');
      learningPath.push('Compete at high levels');
      learningPath.push('Share knowledge with others');
    }

    return learningPath;
  }
}

export const agentAIService = new AgentAIService();
export default agentAIService;

-- Findz Gaming Streaming Platform - Supabase Database Schema
-- Run this SQL in your Supabase SQL Editor to set up the complete database

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Users table for streamers and viewers
CREATE TABLE IF NOT EXISTS users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email VARCHAR(255) UNIQUE NOT NULL,
  username VARCHAR(50) UNIQUE NOT NULL,
  display_name VARCHAR(100),
  avatar_url TEXT,
  bio TEXT,
  gaming_preferences JSONB DEFAULT '{}',
  streaming_settings JSONB DEFAULT '{}',
  is_streamer BOOLEAN DEFAULT false,
  is_verified BOOLEAN DEFAULT false,
  follower_count INTEGER DEFAULT 0,
  following_count INTEGER DEFAULT 0,
  total_stream_time INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Gaming streams table
CREATE TABLE IF NOT EXISTS gaming_streams (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  streamer_id UUID REFERENCES users(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  game_category VARCHAR(100),
  game_title VARCHAR(200),
  thumbnail_url TEXT,
  stream_key VARCHAR(255) UNIQUE,
  rtmp_url TEXT,
  webrtc_url TEXT,
  status VARCHAR(20) DEFAULT 'offline' CHECK (status IN ('live', 'offline', 'starting', 'ending')),
  viewer_count INTEGER DEFAULT 0,
  max_viewers INTEGER DEFAULT 0,
  quality VARCHAR(10) DEFAULT '720p',
  bitrate INTEGER DEFAULT 2500,
  fps INTEGER DEFAULT 30,
  is_recording BOOLEAN DEFAULT false,
  recording_url TEXT,
  stream_settings JSONB DEFAULT '{}',
  analytics_data JSONB DEFAULT '{}',
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Chat messages table
CREATE TABLE IF NOT EXISTS chat_messages (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID REFERENCES gaming_streams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  message TEXT NOT NULL,
  message_type VARCHAR(20) DEFAULT 'text' CHECK (message_type IN ('text', 'emote', 'system', 'ai_response')),
  metadata JSONB DEFAULT '{}',
  is_deleted BOOLEAN DEFAULT false,
  is_moderated BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Stream analytics table
CREATE TABLE IF NOT EXISTS stream_analytics (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID REFERENCES gaming_streams(id) ON DELETE CASCADE,
  timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  viewer_count INTEGER DEFAULT 0,
  chat_activity INTEGER DEFAULT 0,
  quality_metrics JSONB DEFAULT '{}',
  engagement_data JSONB DEFAULT '{}',
  technical_metrics JSONB DEFAULT '{}'
);

-- Game categories table
CREATE TABLE IF NOT EXISTS game_categories (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(100) UNIQUE NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  icon_url TEXT,
  banner_url TEXT,
  is_featured BOOLEAN DEFAULT false,
  stream_count INTEGER DEFAULT 0,
  total_viewers INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI stream assistance table
CREATE TABLE IF NOT EXISTS ai_stream_assistance (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  stream_id UUID REFERENCES gaming_streams(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  query TEXT NOT NULL,
  response TEXT NOT NULL,
  ai_provider VARCHAR(50) DEFAULT 'ollama',
  response_time INTEGER,
  confidence_score FLOAT,
  feedback_rating INTEGER CHECK (feedback_rating >= 1 AND feedback_rating <= 5),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_gaming_streams_streamer_id ON gaming_streams(streamer_id);
CREATE INDEX IF NOT EXISTS idx_gaming_streams_status ON gaming_streams(status);
CREATE INDEX IF NOT EXISTS idx_gaming_streams_game_category ON gaming_streams(game_category);
CREATE INDEX IF NOT EXISTS idx_chat_messages_stream_id ON chat_messages(stream_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_user_id ON chat_messages(user_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_created_at ON chat_messages(created_at);
CREATE INDEX IF NOT EXISTS idx_stream_analytics_stream_id ON stream_analytics(stream_id);
CREATE INDEX IF NOT EXISTS idx_stream_analytics_timestamp ON stream_analytics(timestamp);
CREATE INDEX IF NOT EXISTS idx_ai_assistance_stream_id ON ai_stream_assistance(stream_id);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- Insert sample game categories
INSERT INTO game_categories (name, slug, description, is_featured) VALUES
  ('Action Games', 'action', 'Fast-paced action and adventure games', true),
  ('Strategy Games', 'strategy', 'Strategic thinking and planning games', true),
  ('RPG Games', 'rpg', 'Role-playing and character development games', true),
  ('Indie Games', 'indie', 'Independent and creative games', false),
  ('Multiplayer Games', 'multiplayer', 'Online multiplayer experiences', true),
  ('Game Development', 'gamedev', 'Live game development and coding', true)
ON CONFLICT (slug) DO NOTHING;

-- Enable Row Level Security (RLS) for better security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE gaming_streams ENABLE ROW LEVEL SECURITY;
ALTER TABLE chat_messages ENABLE ROW LEVEL SECURITY;
ALTER TABLE stream_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_stream_assistance ENABLE ROW LEVEL SECURITY;

-- Create basic RLS policies (you can customize these based on your needs)
CREATE POLICY "Users can view all profiles" ON users FOR SELECT USING (true);
CREATE POLICY "Users can update own profile" ON users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Anyone can view live streams" ON gaming_streams FOR SELECT USING (true);
CREATE POLICY "Streamers can manage own streams" ON gaming_streams FOR ALL USING (auth.uid() = streamer_id);

CREATE POLICY "Anyone can view chat messages" ON chat_messages FOR SELECT USING (true);
CREATE POLICY "Authenticated users can send messages" ON chat_messages FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Anyone can view game categories" ON game_categories FOR SELECT USING (true);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gaming_streams_updated_at BEFORE UPDATE ON gaming_streams
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Success message
SELECT 'Findz Gaming Streaming Platform database setup complete! 🎮🚀' as status;

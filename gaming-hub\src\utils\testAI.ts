import { aiService } from '../services/aiService';
import { voiceService } from '../services/voiceService';
import { gameCoachService } from '../services/gameCoachService';
import { apiManager } from '../services/apiManager';
import { Environment } from '../config/environment';

export interface TestResult {
  service: string;
  test: string;
  success: boolean;
  message: string;
  duration?: number;
}

export class AITestSuite {
  private results: TestResult[] = [];

  async runAllTests(): Promise<TestResult[]> {
    this.results = [];
    
    console.log('🧪 Starting AI Integration Test Suite...');
    
    // Test environment configuration
    await this.testEnvironmentConfig();
    
    // Test API connections
    await this.testAPIConnections();
    
    // Test AI services
    await this.testAIServices();
    
    // Test voice services
    await this.testVoiceServices();
    
    // Test game coaching
    await this.testGameCoaching();
    
    console.log('✅ AI Integration Test Suite Complete!');
    this.printResults();
    
    return this.results;
  }

  private async testEnvironmentConfig(): Promise<void> {
    const startTime = Date.now();
    
    try {
      // Test required environment variables
      const requiredVars = [
        'EXPO_PUBLIC_SUPABASE_URL',
        'EXPO_PUBLIC_SUPABASE_ANON_KEY',
        'EXPO_PUBLIC_OLLAMA_BASE_URL',
      ];

      const missingVars = requiredVars.filter(varName => !Environment.SUPABASE.URL && varName.includes('SUPABASE'));
      
      if (missingVars.length === 0) {
        this.addResult('Environment', 'Configuration Check', true, 'All required environment variables are set', Date.now() - startTime);
      } else {
        this.addResult('Environment', 'Configuration Check', false, `Missing variables: ${missingVars.join(', ')}`, Date.now() - startTime);
      }
    } catch (error) {
      this.addResult('Environment', 'Configuration Check', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testAPIConnections(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const healthStatus = await apiManager.checkServiceHealth();
      
      Object.entries(healthStatus).forEach(([service, isHealthy]) => {
        this.addResult('API Manager', `${service} Connection`, isHealthy, 
          isHealthy ? 'Service is accessible' : 'Service is not accessible');
      });
      
      this.addResult('API Manager', 'Health Check', true, 'Health check completed', Date.now() - startTime);
    } catch (error) {
      this.addResult('API Manager', 'Health Check', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testAIServices(): Promise<void> {
    // Test Ollama connection
    await this.testOllamaConnection();
    
    // Test AI response generation
    await this.testAIResponseGeneration();
    
    // Test game tips generation
    await this.testGameTipsGeneration();
  }

  private async testOllamaConnection(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const isConnected = await aiService.checkConnection();
      this.addResult('AI Service', 'Ollama Connection', isConnected, 
        isConnected ? 'Ollama is accessible' : 'Ollama is not accessible', Date.now() - startTime);
    } catch (error) {
      this.addResult('AI Service', 'Ollama Connection', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testAIResponseGeneration(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const testMessage = {
        id: 'test',
        role: 'user' as const,
        content: 'Hello, can you give me a quick gaming tip?',
        timestamp: new Date(),
      };

      const response = await aiService.generateResponse([testMessage]);
      
      if (response.content && response.content.length > 0) {
        this.addResult('AI Service', 'Response Generation', true, 
          `Generated response: ${response.content.substring(0, 50)}...`, Date.now() - startTime);
      } else {
        this.addResult('AI Service', 'Response Generation', false, 'Empty response received', Date.now() - startTime);
      }
    } catch (error) {
      this.addResult('AI Service', 'Response Generation', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testGameTipsGeneration(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const tips = await aiService.generateGameTips('Test Game', 'medium');
      
      if (tips && tips.length > 0) {
        this.addResult('AI Service', 'Game Tips Generation', true, 
          `Generated ${tips.length} tips`, Date.now() - startTime);
      } else {
        this.addResult('AI Service', 'Game Tips Generation', false, 'No tips generated', Date.now() - startTime);
      }
    } catch (error) {
      this.addResult('AI Service', 'Game Tips Generation', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testVoiceServices(): Promise<void> {
    // Test voice availability
    await this.testVoiceAvailability();
    
    // Test speech generation (without playing)
    await this.testSpeechGeneration();
  }

  private async testVoiceAvailability(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const isAvailable = await voiceService.isVoiceEnabled();
      this.addResult('Voice Service', 'Availability Check', isAvailable, 
        isAvailable ? 'Voice service is enabled' : 'Voice service is disabled', Date.now() - startTime);
    } catch (error) {
      this.addResult('Voice Service', 'Availability Check', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testSpeechGeneration(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const isEnabled = await voiceService.isVoiceEnabled();
      
      if (!isEnabled) {
        this.addResult('Voice Service', 'Speech Generation', false, 'Voice service not enabled', Date.now() - startTime);
        return;
      }

      const result = await voiceService.generateSpeech('This is a test message for the gaming hub.');
      
      this.addResult('Voice Service', 'Speech Generation', result.success, 
        result.success ? 'Speech generated successfully' : result.error || 'Unknown error', Date.now() - startTime);
    } catch (error) {
      this.addResult('Voice Service', 'Speech Generation', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testGameCoaching(): Promise<void> {
    // Test player profile creation
    await this.testPlayerProfile();
    
    // Test game analysis
    await this.testGameAnalysis();
    
    // Test personalized tips
    await this.testPersonalizedTips();
  }

  private async testPlayerProfile(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const testUserId = 'test-user-123';
      const profile = await gameCoachService.getPlayerProfile(testUserId);
      
      if (profile && profile.userId === testUserId) {
        this.addResult('Game Coach', 'Player Profile', true, 
          `Profile created for skill level: ${profile.skillLevel}`, Date.now() - startTime);
      } else {
        this.addResult('Game Coach', 'Player Profile', false, 'Failed to create profile', Date.now() - startTime);
      }
    } catch (error) {
      this.addResult('Game Coach', 'Player Profile', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testGameAnalysis(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const testGameContext = {
        gameId: 'test-game',
        gameTitle: 'Test Game',
        currentScore: 1000,
        difficulty: 'medium' as const,
        timeSpent: 300,
        achievements: ['first-win'],
      };

      const testSessionData = {
        score: 1000,
        duration: 300,
        achievements: ['first-win'],
      };

      const analysis = await gameCoachService.analyzeGameSession(
        'test-user-123',
        testGameContext,
        testSessionData
      );

      if (analysis && analysis.recommendations && analysis.recommendations.length > 0) {
        this.addResult('Game Coach', 'Game Analysis', true, 
          `Analysis completed with ${analysis.recommendations.length} recommendations`, Date.now() - startTime);
      } else {
        this.addResult('Game Coach', 'Game Analysis', false, 'No analysis generated', Date.now() - startTime);
      }
    } catch (error) {
      this.addResult('Game Coach', 'Game Analysis', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private async testPersonalizedTips(): Promise<void> {
    const startTime = Date.now();
    
    try {
      const tips = await gameCoachService.generatePersonalizedTips('test-user-123', 'test-game', 3);
      
      if (tips && tips.length > 0) {
        this.addResult('Game Coach', 'Personalized Tips', true, 
          `Generated ${tips.length} personalized tips`, Date.now() - startTime);
      } else {
        this.addResult('Game Coach', 'Personalized Tips', false, 'No tips generated', Date.now() - startTime);
      }
    } catch (error) {
      this.addResult('Game Coach', 'Personalized Tips', false, `Error: ${error}`, Date.now() - startTime);
    }
  }

  private addResult(service: string, test: string, success: boolean, message: string, duration?: number): void {
    this.results.push({ service, test, success, message, duration });
  }

  private printResults(): void {
    console.log('\n📊 Test Results Summary:');
    console.log('========================');
    
    const groupedResults = this.results.reduce((acc, result) => {
      if (!acc[result.service]) {
        acc[result.service] = [];
      }
      acc[result.service].push(result);
      return acc;
    }, {} as { [key: string]: TestResult[] });

    Object.entries(groupedResults).forEach(([service, tests]) => {
      console.log(`\n🔧 ${service}:`);
      tests.forEach(test => {
        const status = test.success ? '✅' : '❌';
        const duration = test.duration ? ` (${test.duration}ms)` : '';
        console.log(`  ${status} ${test.test}: ${test.message}${duration}`);
      });
    });

    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log(`\n📈 Overall Success Rate: ${passedTests}/${totalTests} (${successRate}%)`);
  }

  getSuccessRate(): number {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    return totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0;
  }
}

export const aiTestSuite = new AITestSuite();

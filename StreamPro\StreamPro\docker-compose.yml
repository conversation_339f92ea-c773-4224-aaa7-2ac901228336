version: '3.8'

services:
  # PostgreSQL Database for Findz Gaming Streaming
  findz-db:
    image: postgres:15-alpine
    container_name: findz-gaming-db
    environment:
      POSTGRES_DB: findz_gaming_streaming
      POSTGRES_USER: findz_user
      POSTGRES_PASSWORD: findz_secure_password_2024
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - findz_db_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    ports:
      - "5432:5432"
    networks:
      - findz-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U findz_user -d findz_gaming_streaming"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Session Management and Caching
  findz-redis:
    image: redis:7-alpine
    container_name: findz-gaming-redis
    ports:
      - "6379:6379"
    volumes:
      - findz_redis_data:/data
    networks:
      - findz-network
    restart: unless-stopped
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Findz Gaming Streaming Platform
  findz-streaming:
    build: .
    container_name: findz-gaming-streaming
    environment:
      NODE_ENV: production
      PORT: 5001
      DATABASE_URL: postgresql://postgres.opgyuyeuczddftaqkvdt:<EMAIL>:6543/postgres
      SUPABASE_URL: https://opgyuyeuczddftaqkvdt.supabase.co/
      SUPABASE_ANON_KEY: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.s7cPyvnAe32_YkB3a2bFifid3eIgkeLTllmvgZ96wQ8
      REDIS_URL: redis://findz-redis:6379
      
      # Gaming Streaming Configuration
      STREAM_QUALITY_DEFAULT: 720p
      STREAM_BITRATE_DEFAULT: 2500
      MAX_VIEWERS_PER_STREAM: 1000
      
      # WebRTC Configuration
      STUN_SERVER_URL: "stun:stun.l.google.com:19302"
      
      # Gaming Features
      ENABLE_GAME_CATEGORIES: "true"
      ENABLE_AI_MODERATION: "true"
      ENABLE_STREAM_RECORDING: "true"
      
      # Security
      JWT_SECRET: "findz_gaming_streaming_jwt_secret_2024_super_secure"
      CORS_ORIGIN: "http://localhost:8082,http://localhost:3000"
      
      # Gaming Hub Integration
      GAMING_HUB_URL: "http://host.docker.internal:8082"
      GAMING_HUB_API_KEY: "findz-gaming-hub-integration"
      
      # File Upload Configuration
      MAX_FILE_SIZE: "100MB"
      UPLOAD_PATH: "/app/uploads"
      STREAM_RECORDING_PATH: "/app/recordings"
      
    ports:
      - "5001:5001"
    volumes:
      - findz_uploads:/app/uploads
      - findz_recordings:/app/recordings
      - findz_streams:/app/streams
    networks:
      - findz-network
    depends_on:
      findz-redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # NGINX Reverse Proxy (Optional - for production)
  findz-proxy:
    image: nginx:alpine
    container_name: findz-gaming-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - findz_ssl_certs:/etc/nginx/ssl
    networks:
      - findz-network
    depends_on:
      - findz-streaming
    restart: unless-stopped
    profiles:
      - production

volumes:
  findz_db_data:
    driver: local
  findz_redis_data:
    driver: local
  findz_uploads:
    driver: local
  findz_recordings:
    driver: local
  findz_streams:
    driver: local
  findz_ssl_certs:
    driver: local

networks:
  findz-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

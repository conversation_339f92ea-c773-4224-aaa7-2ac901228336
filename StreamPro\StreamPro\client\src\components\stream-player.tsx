import { useState, useRef, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import { 
  Play, 
  Pause, 
  Volume2, 
  VolumeX, 
  Maximize,
  Eye,
  Settings
} from "lucide-react";
import { useWebRTC } from "@/hooks/useWebRTC";
import { cn } from "@/lib/utils";

interface StreamPlayerProps {
  stream: any;
  className?: string;
}

export function StreamPlayer({ stream, className }: StreamPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [isMuted, setIsMuted] = useState(false);
  const [volume, setVolume] = useState([50]);
  const [quality, setQuality] = useState("1080p");
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  
  const { 
    localStream, 
    remoteStream, 
    isConnected, 
    startStreaming, 
    stopStreaming,
    joinStream 
  } = useWebRTC(stream.id);
  
  useEffect(() => {
    if (stream.status === 'live') {
      joinStream();
    }
  }, [stream.id, stream.status, joinStream]);
  
  useEffect(() => {
    if (videoRef.current && remoteStream) {
      videoRef.current.srcObject = remoteStream;
    }
  }, [remoteStream]);
  
  const togglePlay = () => {
    if (videoRef.current) {
      if (isPlaying) {
        videoRef.current.pause();
      } else {
        videoRef.current.play();
      }
      setIsPlaying(!isPlaying);
    }
  };
  
  const toggleMute = () => {
    if (videoRef.current) {
      videoRef.current.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };
  
  const handleVolumeChange = (value: number[]) => {
    setVolume(value);
    if (videoRef.current) {
      videoRef.current.volume = value[0] / 100;
    }
  };
  
  const toggleFullscreen = () => {
    if (!isFullscreen) {
      if (containerRef.current?.requestFullscreen) {
        containerRef.current.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
  };
  
  const handleMouseMove = () => {
    setShowControls(true);
    // Auto-hide controls after 3 seconds
    setTimeout(() => setShowControls(false), 3000);
  };
  
  return (
    <Card className={cn("overflow-hidden", className)}>
      <div className="p-6 border-b border-border">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">{stream.title}</h3>
            <p className="text-muted-foreground text-sm">{stream.description}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant={stream.status === 'live' ? 'default' : 'secondary'}>
              {stream.status === 'live' && <div className="w-2 h-2 bg-red-500 rounded-full mr-1 animate-pulse" />}
              {stream.status.toUpperCase()}
            </Badge>
            <span className="text-sm text-muted-foreground flex items-center">
              <Eye className="w-4 h-4 mr-1" />
              {stream.viewerCount || 0} viewers
            </span>
          </div>
        </div>
      </div>
      
      {/* Video Player */}
      <div 
        ref={containerRef}
        className="relative bg-black aspect-video"
        onMouseMove={handleMouseMove}
        onMouseLeave={() => setShowControls(false)}
      >
        {stream.status === 'live' ? (
          <video
            ref={videoRef}
            className="w-full h-full object-cover"
            autoPlay
            playsInline
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          />
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center text-white">
              <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Play className="w-8 h-8" />
              </div>
              <h4 className="text-lg font-semibold mb-2">
                {stream.status === 'scheduled' ? 'Stream Scheduled' : 'Stream Offline'}
              </h4>
              <p className="text-white/70">
                {stream.status === 'scheduled' 
                  ? `Starts ${new Date(stream.scheduledAt).toLocaleString()}`
                  : 'This stream is currently offline'
                }
              </p>
            </div>
          </div>
        )}
        
        {/* Stream Controls Overlay */}
        {stream.status === 'live' && (
          <div className={cn(
            "absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300",
            showControls ? "opacity-100" : "opacity-0"
          )}>
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-10 h-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-0"
                  onClick={togglePlay}
                >
                  {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                </Button>
                
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-10 h-10 bg-white/20 hover:bg-white/30 text-white rounded-full p-0"
                  onClick={toggleMute}
                >
                  {isMuted ? <VolumeX className="w-4 h-4" /> : <Volume2 className="w-4 h-4" />}
                </Button>
                
                <div className="flex items-center space-x-2">
                  <Slider
                    value={volume}
                    onValueChange={handleVolumeChange}
                    max={100}
                    step={1}
                    className="w-20"
                  />
                </div>
                
                <div className="text-white text-sm font-medium">
                  <span>Live</span>
                </div>
              </div>
              
              <div className="flex items-center space-x-2">
                <Select value={quality} onValueChange={setQuality}>
                  <SelectTrigger className="w-20 bg-black/50 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1080p">1080p</SelectItem>
                    <SelectItem value="720p">720p</SelectItem>
                    <SelectItem value="480p">480p</SelectItem>
                    <SelectItem value="auto">Auto</SelectItem>
                  </SelectContent>
                </Select>
                
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="w-8 h-8 bg-white/20 hover:bg-white/30 text-white rounded p-0"
                  onClick={toggleFullscreen}
                >
                  <Maximize className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        )}
        
        {/* Connection Status */}
        {stream.status === 'live' && (
          <div className="absolute top-4 right-4">
            <Badge variant={isConnected ? 'default' : 'destructive'}>
              {isConnected ? 'Connected' : 'Connecting...'}
            </Badge>
          </div>
        )}
      </div>
    </Card>
  );
}

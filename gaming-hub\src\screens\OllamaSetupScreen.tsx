import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  Linking,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { ollamaOptimizer, ModelRecommendation, OllamaModel } from '../services/ollamaOptimizer';
import { theme, colors } from '../theme';

export default function OllamaSetupScreen() {
  const navigation = useNavigation();
  const [loading, setLoading] = useState(true);
  const [ollamaStatus, setOllamaStatus] = useState<{
    running: boolean;
    version?: string;
    models?: OllamaModel[];
  }>({ running: false });
  const [recommendations, setRecommendations] = useState<ModelRecommendation[]>([]);
  const [optimization, setOptimization] = useState<any>(null);
  const [pullingModel, setPullingModel] = useState<string | null>(null);

  useEffect(() => {
    checkSetup();
  }, []);

  const checkSetup = async () => {
    try {
      setLoading(true);
      
      // Check Ollama status
      const status = await ollamaOptimizer.checkOllamaStatus();
      setOllamaStatus(status);
      
      // Get recommendations
      const recs = await ollamaOptimizer.getRecommendedModels();
      setRecommendations(recs);
      
      // Get optimization suggestions
      const opt = await ollamaOptimizer.optimizeForGaming();
      setOptimization(opt);
    } catch (error) {
      console.error('Error checking Ollama setup:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleInstallOllama = () => {
    Alert.alert(
      'Install Ollama',
      'You will be redirected to the Ollama website to download and install Ollama.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Open Website', 
          onPress: () => Linking.openURL('https://ollama.ai')
        },
      ]
    );
  };

  const handlePullModel = async (modelName: string) => {
    if (pullingModel) return;

    Alert.alert(
      'Pull Model',
      `This will download ${modelName}. This may take several minutes and requires a good internet connection.`,
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Download', 
          onPress: async () => {
            setPullingModel(modelName);
            try {
              const result = await ollamaOptimizer.pullRecommendedModel(modelName);
              if (result.success) {
                Alert.alert('Success', `${modelName} has been downloaded successfully!`);
                await checkSetup(); // Refresh status
              } else {
                Alert.alert('Error', result.error || 'Failed to download model');
              }
            } catch (error) {
              Alert.alert('Error', 'Failed to download model');
            } finally {
              setPullingModel(null);
            }
          }
        },
      ]
    );
  };

  const renderStatusCard = () => (
    <View style={styles.statusCard}>
      <View style={styles.statusHeader}>
        <Ionicons 
          name={ollamaStatus.running ? 'checkmark-circle' : 'close-circle'} 
          size={24} 
          color={ollamaStatus.running ? colors.success : colors.error} 
        />
        <Text style={styles.statusTitle}>
          Ollama Status: {ollamaStatus.running ? 'Running' : 'Not Running'}
        </Text>
      </View>
      
      {ollamaStatus.version && (
        <Text style={styles.statusDetail}>Version: {ollamaStatus.version}</Text>
      )}
      
      {ollamaStatus.models && (
        <Text style={styles.statusDetail}>
          Installed Models: {ollamaStatus.models.length}
        </Text>
      )}
      
      {!ollamaStatus.running && (
        <TouchableOpacity style={styles.installButton} onPress={handleInstallOllama}>
          <Text style={styles.installButtonText}>Install Ollama</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  const renderRecommendation = (rec: ModelRecommendation) => {
    const isInstalled = ollamaStatus.models?.some(model => 
      model.name.includes(rec.model.split(':')[0])
    );
    const isPulling = pullingModel === rec.model;

    return (
      <View key={rec.model} style={styles.recommendationCard}>
        <View style={styles.recommendationHeader}>
          <Text style={styles.modelName}>{rec.model}</Text>
          <View style={styles.badges}>
            <View style={[styles.badge, styles[`${rec.performance}Badge`]]}>
              <Text style={styles.badgeText}>{rec.performance}</Text>
            </View>
            <View style={[styles.badge, styles[`${rec.memoryUsage}MemoryBadge`]]}>
              <Text style={styles.badgeText}>{rec.memoryUsage} RAM</Text>
            </View>
          </View>
        </View>
        
        <Text style={styles.useCase}>{rec.useCase}</Text>
        <Text style={styles.reason}>{rec.reason}</Text>
        
        <View style={styles.recommendationFooter}>
          {isInstalled ? (
            <View style={styles.installedIndicator}>
              <Ionicons name="checkmark-circle" size={16} color={colors.success} />
              <Text style={styles.installedText}>Installed</Text>
            </View>
          ) : (
            <TouchableOpacity 
              style={[styles.pullButton, isPulling && styles.pullingButton]}
              onPress={() => handlePullModel(rec.model)}
              disabled={isPulling || !ollamaStatus.running}
            >
              {isPulling ? (
                <>
                  <ActivityIndicator size="small" color={colors.text} />
                  <Text style={styles.pullButtonText}>Downloading...</Text>
                </>
              ) : (
                <>
                  <Ionicons name="download" size={16} color={colors.text} />
                  <Text style={styles.pullButtonText}>Download</Text>
                </>
              )}
            </TouchableOpacity>
          )}
        </View>
      </View>
    );
  };

  const renderOptimizationTips = () => {
    if (!optimization) return null;

    return (
      <View style={styles.optimizationCard}>
        <Text style={styles.optimizationTitle}>Optimization Tips</Text>
        
        <View style={styles.recommendedModelSection}>
          <Text style={styles.recommendedModelLabel}>Recommended Model:</Text>
          <Text style={styles.recommendedModelName}>{optimization.recommendedModel}</Text>
        </View>
        
        <View style={styles.instructionsSection}>
          <Text style={styles.instructionsTitle}>Setup Instructions:</Text>
          {optimization.instructions.map((instruction: string, index: number) => (
            <Text key={index} style={styles.instructionItem}>
              {index + 1}. {instruction}
            </Text>
          ))}
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Checking Ollama setup...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={colors.gradientPrimary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Ionicons name="settings" size={32} color={colors.text} />
            <Text style={styles.headerTitle}>AI Assistant Setup</Text>
            <Text style={styles.headerSubtitle}>
              Configure Ollama for optimal gaming assistance
            </Text>
          </View>
        </View>
      </LinearGradient>

      {/* Status Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Current Status</Text>
        {renderStatusCard()}
      </View>

      {/* Recommendations Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Recommended Models</Text>
        <Text style={styles.sectionDescription}>
          These models are optimized for gaming assistance and coaching
        </Text>
        {recommendations.map(renderRecommendation)}
      </View>

      {/* Optimization Section */}
      <View style={styles.section}>
        {renderOptimizationTips()}
      </View>

      {/* Help Section */}
      <View style={styles.section}>
        <View style={styles.helpCard}>
          <Text style={styles.helpTitle}>Need Help?</Text>
          <Text style={styles.helpText}>
            If you're having trouble setting up Ollama, check out the documentation or contact support.
          </Text>
          <TouchableOpacity 
            style={styles.helpButton}
            onPress={() => Linking.openURL('https://ollama.ai/docs')}
          >
            <Text style={styles.helpButtonText}>View Documentation</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginTop: theme.spacing.md,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: theme.spacing.xs,
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    ...theme.typography.textStyles.h2,
    color: colors.text,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  headerSubtitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    opacity: 0.9,
    textAlign: 'center',
  },
  section: {
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginBottom: theme.spacing.sm,
  },
  sectionDescription: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    marginBottom: theme.spacing.md,
  },
  statusCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  statusHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  statusTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginLeft: theme.spacing.sm,
  },
  statusDetail: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  installButton: {
    backgroundColor: colors.primary,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    marginTop: theme.spacing.md,
  },
  installButtonText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
  },
  recommendationCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  recommendationHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: theme.spacing.sm,
  },
  modelName: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    flex: 1,
  },
  badges: {
    flexDirection: 'row',
  },
  badge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    marginLeft: theme.spacing.xs,
  },
  fastBadge: {
    backgroundColor: colors.success,
  },
  balancedBadge: {
    backgroundColor: colors.warning,
  },
  qualityBadge: {
    backgroundColor: colors.info,
  },
  lowMemoryBadge: {
    backgroundColor: colors.success,
  },
  mediumMemoryBadge: {
    backgroundColor: colors.warning,
  },
  highMemoryBadge: {
    backgroundColor: colors.error,
  },
  badgeText: {
    ...theme.typography.textStyles.small,
    color: colors.text,
    fontSize: 10,
    fontWeight: '600',
  },
  useCase: {
    ...theme.typography.textStyles.body,
    color: colors.primary,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  reason: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    lineHeight: 20,
    marginBottom: theme.spacing.md,
  },
  recommendationFooter: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  installedIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  installedText: {
    ...theme.typography.textStyles.caption,
    color: colors.success,
    marginLeft: theme.spacing.xs,
  },
  pullButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.primary,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
  },
  pullingButton: {
    backgroundColor: colors.textSecondary,
  },
  pullButtonText: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
    marginLeft: theme.spacing.xs,
    fontWeight: '600',
  },
  optimizationCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  optimizationTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginBottom: theme.spacing.md,
  },
  recommendedModelSection: {
    marginBottom: theme.spacing.md,
  },
  recommendedModelLabel: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  recommendedModelName: {
    ...theme.typography.textStyles.h4,
    color: colors.primary,
  },
  instructionsSection: {
    marginTop: theme.spacing.md,
  },
  instructionsTitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.sm,
  },
  instructionItem: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
    lineHeight: 20,
  },
  helpCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    ...theme.shadows.sm,
  },
  helpTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginBottom: theme.spacing.sm,
  },
  helpText: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.md,
    lineHeight: 20,
  },
  helpButton: {
    backgroundColor: colors.secondary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  helpButtonText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
  },
});

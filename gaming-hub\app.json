{"expo": {"name": "Findz Gaming Hub", "slug": "findz-gaming-hub", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#1a1a2e"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.financefindz.finderzfundz"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#1a1a2e"}, "edgeToEdgeEnabled": true, "package": "com.financefindz.finderzfundz"}, "web": {"favicon": "./assets/favicon.png"}, "extra": {"eas": {"projectId": "findz-gaming-hub"}, "EXPO_PUBLIC_SUPABASE_URL": "${EXPO_PUBLIC_SUPABASE_URL}", "EXPO_PUBLIC_SUPABASE_ANON_KEY": "${EXPO_PUBLIC_SUPABASE_ANON_KEY}", "EXPO_PUBLIC_OLLAMA_BASE_URL": "${EXPO_PUBLIC_OLLAMA_BASE_URL}", "EXPO_PUBLIC_OLLAMA_MODEL": "${EXPO_PUBLIC_OLLAMA_MODEL}", "EXPO_PUBLIC_CLOUDINARY_CLOUD_NAME": "${EXPO_PUBLIC_CLOUDINARY_CLOUD_NAME}", "EXPO_PUBLIC_APP_ENV": "${EXPO_PUBLIC_APP_ENV}", "EXPO_PUBLIC_AI_ENABLED": "${EXPO_PUBLIC_AI_ENABLED}", "EXPO_PUBLIC_VOICE_ENABLED": "${EXPO_PUBLIC_VOICE_ENABLED}", "AGORA_APP_ID": "${AGORA_APP_ID}", "AGORA_CERTIFICATE": "${AGORA_CERTIFICATE}"}}}
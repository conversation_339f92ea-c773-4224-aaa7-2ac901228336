const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://opgyuyeuczddftaqkvdt.supabase.co/';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.s7cPyvnAe32_YkB3a2bFifid3eIgkeLTllmvgZ96wQ8';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createTestUser() {
  console.log('🎮 Creating test user for Findz Gaming Hub...\n');

  const testEmail = '<EMAIL>';
  const testPassword = 'gaming123';
  const testUsername = 'FindzGamer';

  try {
    // First, try to sign up the user
    console.log('📝 Attempting to create user account...');
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: testEmail,
      password: testPassword,
      options: {
        data: {
          username: testUsername,
          display_name: '<PERSON><PERSON> Gamer',
        },
      },
    });

    if (signUpError) {
      if (signUpError.message.includes('already registered')) {
        console.log('✅ User already exists, attempting to sign in...');
        
        // Try to sign in with existing user
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: testEmail,
          password: testPassword,
        });

        if (signInError) {
          console.error('❌ Sign in failed:', signInError.message);
          console.log('\n🔧 You may need to reset the password or create a new user manually.');
          return;
        }

        console.log('✅ Successfully signed in with existing user!');
        console.log('User ID:', signInData.user.id);
      } else {
        console.error('❌ Sign up failed:', signUpError.message);
        return;
      }
    } else {
      console.log('✅ User account created successfully!');
      console.log('User ID:', signUpData.user?.id);
      
      if (signUpData.user && !signUpData.user.email_confirmed_at) {
        console.log('📧 Please check email for verification (if email confirmation is enabled)');
      }
    }

    console.log('\n🎯 TEST CREDENTIALS:');
    console.log('==================');
    console.log('Email:', testEmail);
    console.log('Password:', testPassword);
    console.log('Username:', testUsername);
    console.log('\n✨ You can now use these credentials to sign in to the gaming hub!');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Run the script
createTestUser();

const { createClient } = require('@supabase/supabase-js');

// Supabase configuration
const supabaseUrl = 'https://opgyuyeuczddftaqkvdt.supabase.co/';
const supabaseAnonKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9wZ3l1eWV1Y3pkZGZ0YXFrdmR0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI2NjA0MzQsImV4cCI6MjA1ODIzNjQzNH0.s7cPyvnAe32_YkB3a2bFifid3eIgkeLTllmvgZ96wQ8';
const supabaseServiceKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9wZ3l1eWV1Y3pkZGZ0YXFrdmR0Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MjY2MDQzNCwiZXhwIjoyMDU4MjM2NDM0fQ.IZQxZn2Upqtql-filzrSQ1q9zK1nPaweXzwB4LVDzuM';

const supabase = createClient(supabaseUrl, supabaseAnonKey);
const supabaseAdmin = createClient(supabaseUrl, supabaseServiceKey);

async function createTestUser() {
  console.log('🎮 Creating confirmed test user for Findz Gaming Hub...\n');

  const testEmail = '<EMAIL>';
  const testPassword = 'gaming123';
  const testUsername = 'FindzGamer';

  try {
    // First, try to create user with admin client (auto-confirmed)
    console.log('📝 Creating confirmed user account...');
    const { data: adminData, error: adminError } = await supabaseAdmin.auth.admin.createUser({
      email: testEmail,
      password: testPassword,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        username: testUsername,
        display_name: 'Findz Gamer',
      },
    });

    if (adminError) {
      if (adminError.message.includes('already registered') || adminError.message.includes('already exists')) {
        console.log('✅ User already exists, checking confirmation status...');

        // Try to sign in with existing user
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: testEmail,
          password: testPassword,
        });

        if (signInError) {
          if (signInError.message.includes('Email not confirmed')) {
            console.log('🔧 Email not confirmed, attempting to confirm...');

            // Try to confirm the user
            const { data: userData, error: getUserError } = await supabaseAdmin.auth.admin.listUsers();
            if (!getUserError && userData.users) {
              const existingUser = userData.users.find(u => u.email === testEmail);
              if (existingUser) {
                const { error: confirmError } = await supabaseAdmin.auth.admin.updateUserById(
                  existingUser.id,
                  { email_confirm: true }
                );

                if (!confirmError) {
                  console.log('✅ Email confirmed successfully!');
                } else {
                  console.error('❌ Failed to confirm email:', confirmError.message);
                }
              }
            }
          } else {
            console.error('❌ Sign in failed:', signInError.message);
            return;
          }
        } else {
          console.log('✅ Successfully signed in with existing user!');
          console.log('User ID:', signInData.user.id);
        }
      } else {
        console.error('❌ User creation failed:', adminError.message);
        return;
      }
    } else {
      console.log('✅ Confirmed user account created successfully!');
      console.log('User ID:', adminData.user?.id);
      console.log('Email confirmed:', adminData.user?.email_confirmed_at ? 'Yes' : 'No');
    }

    console.log('\n🎯 TEST CREDENTIALS:');
    console.log('==================');
    console.log('Email:', testEmail);
    console.log('Password:', testPassword);
    console.log('Username:', testUsername);
    console.log('\n✨ You can now use these credentials to sign in to the gaming hub!');

  } catch (error) {
    console.error('❌ Unexpected error:', error.message);
  }
}

// Run the script
createTestUser();

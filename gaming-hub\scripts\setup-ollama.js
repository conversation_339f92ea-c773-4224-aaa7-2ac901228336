#!/usr/bin/env node

/**
 * Ollama Gaming Setup Script
 * Automatically configures Ollama with optimal models for gaming AI
 */

const axios = require('axios');
const readline = require('readline');
const { spawn } = require('child_process');

const OLLAMA_BASE_URL = 'http://127.0.0.1:11434';

const GAMING_MODELS = [
  {
    name: 'mistral:7b-instruct',
    displayName: 'Mistral 7B Instruct',
    description: 'Best overall gaming assistant',
    priority: 1,
    size: '4.1GB',
    essential: true,
  },
  {
    name: 'deepseek-coder:6.7b-instruct',
    displayName: 'DeepSeek Coder 6.7B',
    description: 'Game development and coding',
    priority: 2,
    size: '3.8GB',
    essential: true,
  },
  {
    name: 'neural-chat:7b',
    displayName: 'Neural Chat 7B',
    description: 'Interactive conversations',
    priority: 3,
    size: '4.1GB',
    essential: false,
  },
  {
    name: 'phi3:3.8b',
    displayName: 'Phi-3 3.8B',
    description: 'Lightweight and fast',
    priority: 4,
    size: '2.2GB',
    essential: false,
  },
];

class OllamaSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async run() {
    console.log('🎮 Findz Gaming Hub - Ollama Setup');
    console.log('=====================================\n');

    try {
      // Check if Ollama is installed and running
      const isRunning = await this.checkOllamaStatus();
      
      if (!isRunning) {
        console.log('❌ Ollama is not running or not installed.');
        console.log('\n📥 Please install Ollama first:');
        console.log('   1. Visit: https://ollama.ai');
        console.log('   2. Download and install Ollama');
        console.log('   3. Start the Ollama service');
        console.log('   4. Run this script again\n');
        process.exit(1);
      }

      console.log('✅ Ollama is running!\n');

      // Get installed models
      const installedModels = await this.getInstalledModels();
      console.log(`📦 Currently installed models: ${installedModels.length}`);
      
      if (installedModels.length > 0) {
        console.log('   ' + installedModels.join(', '));
      }
      console.log('');

      // Show recommended models
      console.log('🎯 Recommended Gaming Models:');
      console.log('==============================');
      
      for (const model of GAMING_MODELS) {
        const isInstalled = installedModels.some(installed => 
          installed.includes(model.name.split(':')[0])
        );
        
        const status = isInstalled ? '✅ Installed' : '⬇️  Available';
        const essential = model.essential ? '(Essential)' : '(Optional)';
        
        console.log(`${status} ${model.displayName} - ${model.size} ${essential}`);
        console.log(`   ${model.description}`);
        console.log('');
      }

      // Ask user what to install
      const choice = await this.askQuestion(
        'What would you like to do?\n' +
        '1. Install all essential models (recommended)\n' +
        '2. Install all models\n' +
        '3. Choose specific models\n' +
        '4. Skip installation\n' +
        'Enter your choice (1-4): '
      );

      switch (choice.trim()) {
        case '1':
          await this.installModels(GAMING_MODELS.filter(m => m.essential));
          break;
        case '2':
          await this.installModels(GAMING_MODELS);
          break;
        case '3':
          await this.interactiveModelSelection();
          break;
        case '4':
          console.log('⏭️  Skipping model installation.');
          break;
        default:
          console.log('❌ Invalid choice. Exiting.');
          process.exit(1);
      }

      // Test models
      console.log('\n🧪 Testing installed models...');
      await this.testModels();

      // Show completion message
      console.log('\n🎉 Setup Complete!');
      console.log('==================');
      console.log('Your Ollama setup is now optimized for gaming AI.');
      console.log('You can now use the Gaming Hub AI features.');
      console.log('\n💡 Next steps:');
      console.log('   1. Start the Gaming Hub app');
      console.log('   2. Navigate to AI Assistant');
      console.log('   3. Start chatting with your AI gaming coach!');

    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  async checkOllamaStatus() {
    try {
      const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`, { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      return false;
    }
  }

  async getInstalledModels() {
    try {
      const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`);
      return response.data.models?.map(model => model.name) || [];
    } catch (error) {
      return [];
    }
  }

  async installModels(models) {
    console.log(`\n📥 Installing ${models.length} models...`);
    
    for (const model of models) {
      console.log(`\n⬇️  Installing ${model.displayName} (${model.size})...`);
      
      try {
        await this.pullModel(model.name);
        console.log(`✅ ${model.displayName} installed successfully!`);
      } catch (error) {
        console.log(`❌ Failed to install ${model.displayName}: ${error.message}`);
      }
    }
  }

  async pullModel(modelName) {
    return new Promise((resolve, reject) => {
      const process = spawn('ollama', ['pull', modelName], { stdio: 'inherit' });
      
      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Process exited with code ${code}`));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  async interactiveModelSelection() {
    console.log('\n📋 Select models to install:');
    const selectedModels = [];

    for (const model of GAMING_MODELS) {
      const answer = await this.askQuestion(
        `Install ${model.displayName} (${model.size})? [y/N]: `
      );
      
      if (answer.toLowerCase().startsWith('y')) {
        selectedModels.push(model);
      }
    }

    if (selectedModels.length > 0) {
      await this.installModels(selectedModels);
    } else {
      console.log('⏭️  No models selected for installation.');
    }
  }

  async testModels() {
    const installedModels = await this.getInstalledModels();
    const gamingModels = installedModels.filter(model => 
      GAMING_MODELS.some(gm => model.includes(gm.name.split(':')[0]))
    );

    if (gamingModels.length === 0) {
      console.log('⚠️  No gaming models found to test.');
      return;
    }

    console.log(`🧪 Testing ${gamingModels.length} gaming models...`);

    for (const modelName of gamingModels.slice(0, 2)) { // Test first 2 models
      try {
        console.log(`   Testing ${modelName}...`);
        const startTime = Date.now();
        
        const response = await axios.post(
          `${OLLAMA_BASE_URL}/api/generate`,
          {
            model: modelName,
            prompt: 'Give me one quick gaming tip in 10 words or less.',
            stream: false,
          },
          { timeout: 30000 }
        );

        const responseTime = Date.now() - startTime;
        
        if (response.data.response) {
          console.log(`   ✅ ${modelName} (${responseTime}ms): ${response.data.response.trim()}`);
        } else {
          console.log(`   ⚠️  ${modelName}: Empty response`);
        }
      } catch (error) {
        console.log(`   ❌ ${modelName}: Test failed`);
      }
    }
  }

  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }
}

// Run the setup
if (require.main === module) {
  const setup = new OllamaSetup();
  setup.run().catch(console.error);
}

module.exports = OllamaSetup;

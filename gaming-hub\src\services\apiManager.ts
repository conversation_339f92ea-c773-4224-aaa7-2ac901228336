import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { Environment, getApiHeaders, buildApiUrl } from '../config/environment';

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  statusCode?: number;
}

export interface SearchResult {
  title: string;
  url: string;
  snippet: string;
  thumbnail?: string;
}

export interface VoiceGenerationOptions {
  text: string;
  voiceId?: string;
  stability?: number;
  similarityBoost?: number;
}

export interface ImageUploadOptions {
  file: string; // base64 or file path
  folder?: string;
  publicId?: string;
  transformation?: any;
}

class APIManager {
  private clients: Map<string, AxiosInstance> = new Map();

  constructor() {
    this.initializeClients();
  }

  private initializeClients() {
    // OpenRouter Client
    if (Environment.OPENROUTER.API_KEY) {
      this.clients.set('openrouter', axios.create({
        baseURL: Environment.OPENROUTER.BASE_URL,
        timeout: Environment.API_TIMEOUT,
        headers: getApiHeaders('OPENROUTER'),
      }));
    }

    // Anthropic Client
    if (Environment.ANTHROPIC.API_KEY) {
      this.clients.set('anthropic', axios.create({
        baseURL: Environment.ANTHROPIC.BASE_URL,
        timeout: Environment.API_TIMEOUT,
        headers: getApiHeaders('ANTHROPIC'),
      }));
    }

    // ElevenLabs Client
    if (Environment.ELEVENLABS.API_KEY) {
      this.clients.set('elevenlabs', axios.create({
        baseURL: Environment.ELEVENLABS.BASE_URL,
        timeout: Environment.API_TIMEOUT,
        headers: getApiHeaders('ELEVENLABS'),
      }));
    }

    // Brave Search Client
    if (Environment.BRAVE.API_KEY) {
      this.clients.set('brave', axios.create({
        baseURL: Environment.BRAVE.BASE_URL,
        timeout: Environment.API_TIMEOUT,
        headers: getApiHeaders('BRAVE'),
      }));
    }

    // GitHub Client
    if (Environment.GITHUB.API_KEY) {
      this.clients.set('github', axios.create({
        baseURL: Environment.GITHUB.BASE_URL,
        timeout: Environment.API_TIMEOUT,
        headers: getApiHeaders('GITHUB'),
      }));
    }
  }

  private async makeRequest<T>(
    clientName: string,
    endpoint: string,
    options: AxiosRequestConfig = {}
  ): Promise<APIResponse<T>> {
    try {
      const client = this.clients.get(clientName);
      if (!client) {
        return {
          success: false,
          error: `API client '${clientName}' not configured`,
        };
      }

      const response = await client.request({
        url: endpoint,
        ...options,
      });

      return {
        success: true,
        data: response.data,
        statusCode: response.status,
      };
    } catch (error: any) {
      console.error(`API Error (${clientName}):`, error);
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'Unknown error',
        statusCode: error.response?.status,
      };
    }
  }

  // OpenRouter AI Chat
  async chatWithAI(messages: any[], model = 'mistralai/mistral-7b-instruct'): Promise<APIResponse<any>> {
    return this.makeRequest('openrouter', '/chat/completions', {
      method: 'POST',
      data: {
        model,
        messages,
        temperature: 0.7,
        max_tokens: 1000,
      },
    });
  }

  // Anthropic Claude Chat
  async chatWithClaude(messages: any[]): Promise<APIResponse<any>> {
    const systemMessage = messages.find(m => m.role === 'system');
    const userMessages = messages.filter(m => m.role !== 'system');

    return this.makeRequest('anthropic', '/v1/messages', {
      method: 'POST',
      data: {
        model: 'claude-3-haiku-20240307',
        max_tokens: 1000,
        system: systemMessage?.content || '',
        messages: userMessages,
      },
    });
  }

  // DeepSeek Chat
  async chatWithDeepSeek(messages: any[]): Promise<APIResponse<any>> {
    try {
      const response = await axios.post(
        `${Environment.DEEPSEEK.BASE_URL}/chat/completions`,
        {
          model: 'deepseek-chat',
          messages,
          temperature: 0.7,
          max_tokens: 1000,
          stream: false,
        },
        {
          headers: {
            'Authorization': `Bearer ${Environment.DEEPSEEK.API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: Environment.API_TIMEOUT,
        }
      );

      return {
        success: true,
        data: response.data,
        statusCode: response.status,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.message || error.message || 'DeepSeek API failed',
        statusCode: error.response?.status,
      };
    }
  }

  // ElevenLabs Text-to-Speech
  async generateVoice(options: VoiceGenerationOptions): Promise<APIResponse<ArrayBuffer>> {
    const voiceId = options.voiceId || 'EXAVITQu4vr4xnSDxMaL'; // Default voice

    return this.makeRequest('elevenlabs', `/text-to-speech/${voiceId}`, {
      method: 'POST',
      data: {
        text: options.text,
        voice_settings: {
          stability: options.stability || 0.5,
          similarity_boost: options.similarityBoost || 0.5,
        },
      },
      responseType: 'arraybuffer',
    });
  }

  // Brave Search
  async searchWeb(query: string, count = 10): Promise<APIResponse<SearchResult[]>> {
    const response = await this.makeRequest('brave', '/web/search', {
      method: 'GET',
      params: {
        q: query,
        count,
        search_lang: 'en',
        country: 'US',
        safesearch: 'moderate',
      },
    });

    if (response.success && response.data?.web?.results) {
      const results: SearchResult[] = response.data.web.results.map((result: any) => ({
        title: result.title,
        url: result.url,
        snippet: result.description,
        thumbnail: result.thumbnail?.src,
      }));

      return {
        success: true,
        data: results,
      };
    }

    return response;
  }

  // Cloudinary Image Upload
  async uploadImage(options: ImageUploadOptions): Promise<APIResponse<any>> {
    try {
      const formData = new FormData();
      formData.append('file', options.file);
      formData.append('upload_preset', 'gaming_hub'); // You'll need to create this preset
      
      if (options.folder) {
        formData.append('folder', options.folder);
      }
      
      if (options.publicId) {
        formData.append('public_id', options.publicId);
      }

      const response = await axios.post(
        `${Environment.CLOUDINARY.BASE_URL}/${Environment.CLOUDINARY.CLOUD_NAME}/image/upload`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
          timeout: Environment.API_TIMEOUT,
        }
      );

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // GitHub Repository Information
  async getRepositoryInfo(owner: string, repo: string): Promise<APIResponse<any>> {
    return this.makeRequest('github', `/repos/${owner}/${repo}`, {
      method: 'GET',
    });
  }

  // Firecrawl Web Scraping
  async scrapeWebsite(url: string): Promise<APIResponse<any>> {
    try {
      const response = await axios.post(
        `${Environment.FIRECRAWL.BASE_URL}/scrape`,
        {
          url,
          pageOptions: {
            onlyMainContent: true,
          },
        },
        {
          headers: {
            'Authorization': `Bearer ${Environment.FIRECRAWL.API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: Environment.API_TIMEOUT,
        }
      );

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Exa Search
  async searchWithExa(query: string, options: any = {}): Promise<APIResponse<any>> {
    try {
      const response = await axios.post(
        `${Environment.EXA.BASE_URL}/search`,
        {
          query,
          type: 'neural',
          useAutoprompt: true,
          numResults: options.numResults || 10,
          contents: {
            text: true,
            highlights: true,
          },
          ...options,
        },
        {
          headers: {
            'Authorization': `Bearer ${Environment.EXA.API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: Environment.API_TIMEOUT,
        }
      );

      return {
        success: true,
        data: response.data,
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.message,
      };
    }
  }

  // Health check for all services
  async checkServiceHealth(): Promise<{ [key: string]: boolean }> {
    const services = ['openrouter', 'anthropic', 'elevenlabs', 'brave', 'github'];
    const healthStatus: { [key: string]: boolean } = {};

    await Promise.all(
      services.map(async (service) => {
        try {
          const client = this.clients.get(service);
          if (!client) {
            healthStatus[service] = false;
            return;
          }

          // Simple health check - try to make a basic request
          await client.get('/', { timeout: 5000 });
          healthStatus[service] = true;
        } catch (error) {
          healthStatus[service] = false;
        }
      })
    );

    return healthStatus;
  }
}

export const apiManager = new APIManager();
export default apiManager;

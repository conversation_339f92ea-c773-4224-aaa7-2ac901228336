# Findz Gaming Hub - VS Code Extension

AI-powered game development extension that integrates with the Findz Gaming Hub platform.

## Features

### 🎮 Game Development
- **Project Templates**: Create games from AI-powered templates
- **Smart Scaffolding**: HTML5, Phaser.js, React game boilerplates
- **Instant Setup**: One-click project creation with best practices

### 🤖 AI Integration
- **Code Assistance**: AI-powered coding help and suggestions
- **Bug Detection**: Intelligent error analysis and fixes
- **Optimization Tips**: Performance improvement recommendations

### 🔗 Hub Integration
- **Direct Connection**: Seamless integration with Gaming Hub
- **Real-time Sync**: Automatic project synchronization
- **One-click Deploy**: Publish games directly to the hub

## Installation

### Method 1: VS Code Marketplace (Recommended)
1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Findz Gaming Hub"
4. Click Install

### Method 2: Manual Installation
1. Download the .vsix file from releases
2. Open VS Code
3. Press Ctrl+Shift+P
4. Type "Extensions: Install from VSIX"
5. Select the downloaded file

## Setup

### 1. Configure Gaming Hub Connection
```json
{
  "findz.hubUrl": "http://localhost:8082",
  "findz.aiEnabled": true,
  "findz.autoSync": true
}
```

### 2. Start Gaming Hub
Make sure your Gaming Hub is running:
```bash
cd gaming-hub
npm start
```

### 3. Verify Connection
- Open Command Palette (Ctrl+Shift+P)
- Run "Findz Gaming Hub: Open Gaming Hub"
- Extension should connect automatically

## Usage

### Creating a New Game Project

1. **Command Palette Method**:
   - Press Ctrl+Shift+P
   - Type "Findz Gaming Hub: Create Game Project"
   - Select a template
   - Enter project name

2. **Explorer Context Menu**:
   - Right-click in Explorer
   - Select "Create Game Project"
   - Follow the prompts

3. **Available Templates**:
   - **HTML5 Canvas Game**: Basic game loop with canvas rendering
   - **Phaser.js Platformer**: Physics-based side-scrolling game
   - **React Game Component**: Component-based interactive game

### AI Code Assistance

1. Select code in the editor
2. Right-click and choose "AI Game Development Assistant"
3. Get intelligent suggestions and improvements

### Deploying to Gaming Hub

1. Open your game project
2. Press Ctrl+Shift+P
3. Run "Findz Gaming Hub: Deploy to Gaming Hub"
4. Your game will be published to the hub

## Commands

| Command | Description | Shortcut |
|---------|-------------|----------|
| `findz.createGameProject` | Create a new game project | - |
| `findz.openGameHub` | Open Gaming Hub in browser | - |
| `findz.aiAssist` | AI code assistance | - |
| `findz.deployToHub` | Deploy project to hub | - |

## Configuration

### Extension Settings

- `findz.hubUrl`: URL of your Gaming Hub instance
- `findz.aiEnabled`: Enable/disable AI features
- `findz.autoSync`: Automatically sync projects

### Example Configuration
```json
{
  "findz.hubUrl": "http://localhost:8082",
  "findz.aiEnabled": true,
  "findz.autoSync": true
}
```

## Game Templates

### HTML5 Canvas Game
- Basic game loop implementation
- Keyboard input handling
- Canvas rendering setup
- Player movement mechanics

### Phaser.js Platformer
- Physics engine integration
- Sprite management
- Collision detection
- Level design basics

### React Game Component
- Component-based architecture
- State management
- Event handling
- Responsive design

## Troubleshooting

### Extension Not Connecting
1. Verify Gaming Hub is running on correct port
2. Check firewall settings
3. Restart VS Code
4. Check extension logs in Output panel

### Templates Not Loading
1. Ensure internet connection
2. Check Gaming Hub API status
3. Verify extension permissions
4. Try reloading VS Code window

### AI Features Not Working
1. Check AI provider configuration in Gaming Hub
2. Verify API keys are set
3. Test AI connection in Gaming Hub first
4. Check extension logs for errors

## Development

### Building the Extension
```bash
cd vscode-extension
npm install
npm run compile
```

### Packaging
```bash
npm install -g vsce
vsce package
```

### Testing
```bash
# Open extension development host
F5 in VS Code
```

## API Integration

The extension communicates with Gaming Hub through:
- **Local Server**: Port 3001 for VS Code ↔ Hub communication
- **REST API**: HTTP endpoints for project management
- **WebSocket**: Real-time updates and notifications

### API Endpoints
- `GET /health` - Extension health check
- `POST /create-project` - Create new game project
- `POST /open-project` - Open project in VS Code
- `GET /projects` - List recent projects
- `POST /execute-command` - Execute VS Code commands

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

MIT License - see LICENSE file for details

## Support

- **Documentation**: [Gaming Hub Docs](../AI_INTEGRATION_GUIDE.md)
- **Issues**: Report bugs on GitHub
- **Discord**: Join our gaming community
- **Email**: <EMAIL>

---

**Transform your game development workflow with AI-powered tools and seamless Gaming Hub integration!** 🎮✨

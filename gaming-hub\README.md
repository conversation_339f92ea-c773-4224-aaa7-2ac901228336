# 🎮 Findz Gaming Hub

A comprehensive React Native/Expo gaming platform that allows users to play HTML5 games, track scores, compete on leaderboards, and earn achievements.

## 🌟 Features

- **Cross-Platform**: Built with React Native/Expo for iOS, Android, and Web
- **Game Integration**: WebView-based system for embedding HTML5/Phaser.js games
- **User Authentication**: Supabase-powered auth with profiles and progress tracking
- **Leaderboards**: Global and per-game leaderboards with real-time updates
- **Achievements**: Unlock achievements based on gameplay milestones
- **Modern UI**: Beautiful dark theme with gradients and animations
- **Real-time Communication**: Bidirectional communication between games and hub

## 🚀 Quick Start

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g expo-cli`)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Installation

1. **Clone and setup the project:**
   ```bash
   cd gaming-hub
   npm install
   ```

2. **Configure Supabase:**
   - Create a new project at [supabase.com](https://supabase.com)
   - Update `src/config/supabase.ts` with your project URL and anon key
   - Run the SQL scripts in `database/` folder to set up the schema

3. **Start the development server:**
   ```bash
   npm start
   ```

4. **Run on your preferred platform:**
   - **Web**: Press `w` in the terminal or visit `http://localhost:8081`
   - **Android**: Press `a` (requires Android emulator or device)
   - **iOS**: Press `i` (requires iOS simulator, macOS only)

## 🏗️ Project Structure

```
gaming-hub/
├── src/
│   ├── components/          # Reusable UI components
│   ├── config/             # Configuration files (Supabase, etc.)
│   ├── contexts/           # React contexts (Auth, etc.)
│   ├── navigation/         # Navigation setup
│   ├── screens/            # Main app screens
│   ├── theme/              # Colors, typography, styling
│   └── types/              # TypeScript type definitions
├── database/               # Supabase SQL schema and sample data
├── game-templates/         # Example game templates
└── assets/                 # Images, icons, fonts
```

## 🎯 Core Screens

### 1. Authentication Screen
- Sign up / Sign in with email and password
- Guest mode for testing
- Automatic profile creation

### 2. Home Screen
- Welcome message with user stats
- Featured games carousel
- Recent games section
- Quick navigation to all games

### 3. Games Screen
- Grid view of all available games
- Search and filter by category
- Game difficulty indicators
- Play count and average scores

### 4. Game Screen (WebView)
- Full-screen game experience
- Real-time score tracking
- Communication bridge with hub
- Game controls (refresh, menu, exit)

### 5. Leaderboard Screen
- Global and per-game leaderboards
- User rankings with avatars
- Score achievements and dates
- Filter by game

### 6. Profile Screen
- User statistics and achievements
- Achievement progress tracking
- Account management
- Sign out functionality

## 🎮 Game Integration

### WebView Communication

Games communicate with the hub using `window.ReactNativeWebView.postMessage()`:

```javascript
// Update score in real-time
window.ReactNativeWebView.postMessage(JSON.stringify({
    type: 'GAME_SCORE',
    score: currentScore
}));

// Complete game session
window.ReactNativeWebView.postMessage(JSON.stringify({
    type: 'GAME_COMPLETED',
    score: finalScore,
    duration: sessionDuration
}));

// Navigate back to hub
window.ReactNativeWebView.postMessage(JSON.stringify({
    type: 'NAVIGATE_BACK'
}));
```

### Game Template

The included Phaser.js clicker game template demonstrates:
- Score tracking and communication
- Particle effects and animations
- Milestone achievements
- Responsive design
- Hub integration

## 🗄️ Database Schema

### Core Tables

- **users**: User profiles and statistics
- **games**: Game catalog with metadata
- **game_sessions**: Individual gameplay sessions
- **achievements**: Available achievements
- **user_achievements**: Unlocked user achievements
- **leaderboards**: High scores and rankings

### Key Features

- Row Level Security (RLS) for data protection
- Automatic user profile creation
- Real-time leaderboard updates
- Achievement tracking system
- Performance optimized with indexes

## 🎨 Theming

The app uses a modern dark theme with:
- **Primary**: Purple gradient (#6C5CE7 to #A29BFE)
- **Secondary**: Teal gradient (#00CEC9 to #55EFC4)
- **Background**: Dark navy (#1A1A2E, #16213E, #0F3460)
- **Text**: White with secondary gray variants
- **Status Colors**: Success, warning, error, info variants

## 🔧 Configuration

### Environment Variables

Create a `.env` file with:
```
SUPABASE_URL=your_supabase_url
SUPABASE_ANON_KEY=your_supabase_anon_key
```

### App Configuration

Update `app.json` for:
- App name and bundle identifiers
- Icon and splash screen
- Platform-specific settings

## 📱 Platform Support

- **iOS**: Full native support with Expo
- **Android**: Full native support with Expo
- **Web**: React Native Web with responsive design

## 🚀 Deployment

### Development Build
```bash
eas build --platform android --profile development
eas build --platform ios --profile development
```

### Production Build
```bash
eas build --platform android --profile production
eas build --platform ios --profile production
```

### Web Deployment
```bash
expo export:web
# Deploy the web-build folder to your hosting service
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Check the documentation
- Open an issue on GitHub
- Contact the development team

---

**Happy Gaming! 🎮**

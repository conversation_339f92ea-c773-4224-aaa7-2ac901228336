import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';

import ChatInterface from '../components/AIAssistant/ChatInterface';
import { GameContext } from '../services/aiService';
import { theme, colors } from '../theme';
import { useAuth } from '../contexts/AuthContext';

interface AIAssistantScreenProps {
  gameContext?: GameContext;
}

export default function AIAssistantScreen() {
  const route = useRoute();
  const navigation = useNavigation();
  const { user } = useAuth();
  const [showChat, setShowChat] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown');

  // Get game context from route params if available
  const gameContext = (route.params as any)?.gameContext as GameContext | undefined;

  useEffect(() => {
    checkAIConnection();
  }, []);

  const checkAIConnection = async () => {
    setIsConnecting(true);
    try {
      const { aiService } = await import('../services/aiService');
      const isConnected = await aiService.checkConnection();
      setConnectionStatus(isConnected ? 'connected' : 'disconnected');
    } catch (error) {
      console.error('Error checking AI connection:', error);
      setConnectionStatus('disconnected');
    } finally {
      setIsConnecting(false);
    }
  };

  const handleStartChat = (initialMessage?: string) => {
    if (connectionStatus === 'disconnected') {
      Alert.alert(
        'AI Assistant Unavailable',
        'The AI assistant is currently unavailable. Please check your connection and try again.',
        [
          { text: 'Retry', onPress: checkAIConnection },
          { text: 'Cancel', style: 'cancel' },
        ]
      );
      return;
    }
    
    setShowChat(true);
  };

  const quickActions = [
    {
      id: 'game-tips',
      title: 'Get Game Tips',
      description: 'Ask for specific tips about your current game',
      icon: 'bulb',
      message: gameContext 
        ? `Give me tips for playing ${gameContext.gameTitle} better`
        : 'Give me general gaming tips',
    },
    {
      id: 'strategy-help',
      title: 'Strategy Help',
      description: 'Get strategic advice for better gameplay',
      icon: 'chess',
      message: 'Help me develop better gaming strategies',
    },
    {
      id: 'score-improvement',
      title: 'Improve Score',
      description: 'Learn how to achieve higher scores',
      icon: 'trending-up',
      message: gameContext 
        ? `How can I improve my score in ${gameContext.gameTitle}? My current score is ${gameContext.currentScore}`
        : 'How can I improve my gaming scores?',
    },
    {
      id: 'general-chat',
      title: 'General Chat',
      description: 'Have a conversation about gaming',
      icon: 'chatbubbles',
      message: undefined,
    },
  ];

  if (showChat) {
    return (
      <ChatInterface
        gameContext={gameContext}
        onClose={() => setShowChat(false)}
      />
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={colors.gradientPrimary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerTop}>
            <View style={styles.headerLeft}>
              <Ionicons name="sparkles" size={48} color={colors.text} />
            </View>
            <TouchableOpacity
              style={styles.setupButton}
              onPress={() => navigation.navigate('OllamaSetup' as never)}
            >
              <Ionicons name="settings" size={24} color={colors.text} />
            </TouchableOpacity>
          </View>
          <Text style={styles.headerTitle}>AI Gaming Assistant</Text>
          <Text style={styles.headerSubtitle}>
            Your personal gaming coach powered by AI
          </Text>
        </View>
        
        {/* Connection Status */}
        <View style={styles.statusContainer}>
          <View style={[
            styles.statusIndicator,
            { backgroundColor: connectionStatus === 'connected' ? colors.success : colors.error }
          ]} />
          <Text style={styles.statusText}>
            {isConnecting ? 'Connecting...' : 
             connectionStatus === 'connected' ? 'AI Assistant Ready' : 'AI Assistant Offline'}
          </Text>
          {isConnecting && <ActivityIndicator size="small" color={colors.text} style={{ marginLeft: 8 }} />}
        </View>
      </LinearGradient>

      {/* Game Context */}
      {gameContext && (
        <View style={styles.gameContextCard}>
          <LinearGradient
            colors={colors.gradientSecondary}
            style={styles.gameContextGradient}
          >
            <View style={styles.gameContextContent}>
              <Ionicons name="game-controller" size={24} color={colors.text} />
              <View style={styles.gameContextInfo}>
                <Text style={styles.gameContextTitle}>{gameContext.gameTitle}</Text>
                <Text style={styles.gameContextDetails}>
                  Score: {gameContext.currentScore} • {gameContext.difficulty} difficulty
                </Text>
              </View>
            </View>
          </LinearGradient>
        </View>
      )}

      {/* Welcome Message */}
      <View style={styles.welcomeSection}>
        <Text style={styles.welcomeTitle}>
          Welcome{user ? `, ${user.username}` : ''}! 👋
        </Text>
        <Text style={styles.welcomeText}>
          I'm here to help you become a better gamer. I can provide tips, strategies, 
          and personalized advice based on your gameplay. What would you like to explore?
        </Text>
      </View>

      {/* Quick Actions */}
      <View style={styles.actionsSection}>
        <Text style={styles.sectionTitle}>Quick Actions</Text>
        {quickActions.map((action) => (
          <TouchableOpacity
            key={action.id}
            style={styles.actionCard}
            onPress={() => handleStartChat(action.message)}
          >
            <View style={styles.actionIcon}>
              <Ionicons name={action.icon as any} size={24} color={colors.primary} />
            </View>
            <View style={styles.actionContent}>
              <Text style={styles.actionTitle}>{action.title}</Text>
              <Text style={styles.actionDescription}>{action.description}</Text>
            </View>
            <Ionicons name="chevron-forward" size={20} color={colors.textSecondary} />
          </TouchableOpacity>
        ))}
      </View>

      {/* Features */}
      <View style={styles.featuresSection}>
        <Text style={styles.sectionTitle}>What I Can Help With</Text>
        
        <View style={styles.featureGrid}>
          <View style={styles.featureCard}>
            <Ionicons name="school" size={32} color={colors.secondary} />
            <Text style={styles.featureTitle}>Learning</Text>
            <Text style={styles.featureText}>
              Game mechanics, controls, and basic strategies
            </Text>
          </View>
          
          <View style={styles.featureCard}>
            <Ionicons name="analytics" size={32} color={colors.warning} />
            <Text style={styles.featureTitle}>Analysis</Text>
            <Text style={styles.featureText}>
              Performance analysis and improvement suggestions
            </Text>
          </View>
          
          <View style={styles.featureCard}>
            <Ionicons name="trophy" size={32} color={colors.gold} />
            <Text style={styles.featureTitle}>Achievement</Text>
            <Text style={styles.featureText}>
              Tips for unlocking achievements and high scores
            </Text>
          </View>
          
          <View style={styles.featureCard}>
            <Ionicons name="heart" size={32} color={colors.error} />
            <Text style={styles.featureTitle}>Motivation</Text>
            <Text style={styles.featureText}>
              Encouragement and positive gaming mindset
            </Text>
          </View>
        </View>
      </View>

      {/* Start Chat Button */}
      <View style={styles.startChatSection}>
        <TouchableOpacity
          style={styles.startChatButton}
          onPress={() => handleStartChat()}
        >
          <LinearGradient
            colors={colors.gradientPrimary}
            style={styles.startChatGradient}
          >
            <Ionicons name="chatbubbles" size={24} color={colors.text} />
            <Text style={styles.startChatText}>Start Conversation</Text>
          </LinearGradient>
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    padding: theme.spacing.lg,
    paddingTop: theme.spacing.xl,
    alignItems: 'center',
  },
  headerContent: {
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    width: '100%',
  },
  headerTop: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
    marginBottom: theme.spacing.md,
  },
  headerLeft: {
    flex: 1,
    alignItems: 'center',
  },
  setupButton: {
    padding: theme.spacing.sm,
    backgroundColor: colors.cardOverlay,
    borderRadius: theme.borderRadius.lg,
  },
  headerTitle: {
    ...theme.typography.textStyles.h2,
    color: colors.text,
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.sm,
    textAlign: 'center',
  },
  headerSubtitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    textAlign: 'center',
    opacity: 0.9,
  },
  statusContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.cardOverlay,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
  },
  statusIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: theme.spacing.sm,
  },
  statusText: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
  },
  gameContextCard: {
    margin: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  gameContextGradient: {
    padding: theme.spacing.md,
  },
  gameContextContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  gameContextInfo: {
    marginLeft: theme.spacing.md,
    flex: 1,
  },
  gameContextTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginBottom: theme.spacing.xs,
  },
  gameContextDetails: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
    opacity: 0.8,
  },
  welcomeSection: {
    padding: theme.spacing.lg,
  },
  welcomeTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginBottom: theme.spacing.md,
  },
  welcomeText: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    lineHeight: 24,
  },
  actionsSection: {
    padding: theme.spacing.lg,
    paddingTop: 0,
  },
  sectionTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginBottom: theme.spacing.md,
  },
  actionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  actionContent: {
    flex: 1,
  },
  actionTitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  actionDescription: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
  },
  featuresSection: {
    padding: theme.spacing.lg,
    paddingTop: 0,
  },
  featureGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  featureCard: {
    width: '48%',
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  featureTitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  featureText: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    textAlign: 'center',
    lineHeight: 16,
  },
  startChatSection: {
    padding: theme.spacing.lg,
  },
  startChatButton: {
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    ...theme.shadows.md,
  },
  startChatGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.lg,
  },
  startChatText: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginLeft: theme.spacing.md,
    fontWeight: '600',
  },
});

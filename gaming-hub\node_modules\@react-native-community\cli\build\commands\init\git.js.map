{"version": 3, "names": ["checkGitInstallation", "execa", "stdio", "checkIfFolderIsGitRepo", "folder", "cwd", "createGitRepository", "loader", "<PERSON><PERSON><PERSON><PERSON>", "start", "version", "JSON", "parse", "fs", "readFileSync", "path", "join", "succeed", "e", "stop", "logger", "debug"], "sources": ["../../../src/commands/init/git.ts"], "sourcesContent": ["import {getLoader, logger} from '@react-native-community/cli-tools';\nimport execa from 'execa';\nimport fs from 'fs';\nimport path from 'path';\n\nexport const checkGitInstallation = async (): Promise<boolean> => {\n  try {\n    await execa('git', ['--version'], {stdio: 'ignore'});\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const checkIfFolderIsGitRepo = async (\n  folder: string,\n): Promise<boolean> => {\n  try {\n    await execa('git', ['rev-parse', '--is-inside-work-tree'], {\n      stdio: 'ignore',\n      cwd: folder,\n    });\n    return true;\n  } catch {\n    return false;\n  }\n};\n\nexport const createGitRepository = async (folder: string) => {\n  const loader = getLoader();\n\n  loader.start('Initializing Git repository');\n\n  let version;\n\n  try {\n    version = JSON.parse(\n      fs.readFileSync(\n        path.join('node_modules/react-native/package.json'),\n        'utf8',\n      ),\n    ).version;\n  } catch {}\n\n  try {\n    await execa('git', ['init'], {cwd: folder});\n    await execa('git', ['branch', '-M', 'main'], {cwd: folder});\n    await execa('git', ['add', '.'], {cwd: folder});\n    await execa(\n      'git',\n      [\n        'commit',\n        '-m',\n        `Initial commit\\n\\n${\n          version ? 'Generated by react-native@' + version : ''\n        }`,\n      ],\n      {\n        cwd: folder,\n      },\n    );\n    loader.succeed();\n  } catch (e) {\n    loader.stop();\n    logger.debug(\n      'Could not create an empty Git repository, error: ',\n      e as string,\n    );\n  }\n};\n"], "mappings": ";;;;;;AAAA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAwB;AAEjB,MAAMA,oBAAoB,GAAG,YAA8B;EAChE,IAAI;IACF,MAAM,IAAAC,gBAAK,EAAC,KAAK,EAAE,CAAC,WAAW,CAAC,EAAE;MAACC,KAAK,EAAE;IAAQ,CAAC,CAAC;IACpD,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF,CAAC;AAAC;AAEK,MAAMC,sBAAsB,GAAG,MACpCC,MAAc,IACO;EACrB,IAAI;IACF,MAAM,IAAAH,gBAAK,EAAC,KAAK,EAAE,CAAC,WAAW,EAAE,uBAAuB,CAAC,EAAE;MACzDC,KAAK,EAAE,QAAQ;MACfG,GAAG,EAAED;IACP,CAAC,CAAC;IACF,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF,CAAC;AAAC;AAEK,MAAME,mBAAmB,GAAG,MAAOF,MAAc,IAAK;EAC3D,MAAMG,MAAM,GAAG,IAAAC,qBAAS,GAAE;EAE1BD,MAAM,CAACE,KAAK,CAAC,6BAA6B,CAAC;EAE3C,IAAIC,OAAO;EAEX,IAAI;IACFA,OAAO,GAAGC,IAAI,CAACC,KAAK,CAClBC,aAAE,CAACC,YAAY,CACbC,eAAI,CAACC,IAAI,CAAC,wCAAwC,CAAC,EACnD,MAAM,CACP,CACF,CAACN,OAAO;EACX,CAAC,CAAC,MAAM,CAAC;EAET,IAAI;IACF,MAAM,IAAAT,gBAAK,EAAC,KAAK,EAAE,CAAC,MAAM,CAAC,EAAE;MAACI,GAAG,EAAED;IAAM,CAAC,CAAC;IAC3C,MAAM,IAAAH,gBAAK,EAAC,KAAK,EAAE,CAAC,QAAQ,EAAE,IAAI,EAAE,MAAM,CAAC,EAAE;MAACI,GAAG,EAAED;IAAM,CAAC,CAAC;IAC3D,MAAM,IAAAH,gBAAK,EAAC,KAAK,EAAE,CAAC,KAAK,EAAE,GAAG,CAAC,EAAE;MAACI,GAAG,EAAED;IAAM,CAAC,CAAC;IAC/C,MAAM,IAAAH,gBAAK,EACT,KAAK,EACL,CACE,QAAQ,EACR,IAAI,EACH,qBACCS,OAAO,GAAG,4BAA4B,GAAGA,OAAO,GAAG,EACpD,EAAC,CACH,EACD;MACEL,GAAG,EAAED;IACP,CAAC,CACF;IACDG,MAAM,CAACU,OAAO,EAAE;EAClB,CAAC,CAAC,OAAOC,CAAC,EAAE;IACVX,MAAM,CAACY,IAAI,EAAE;IACbC,kBAAM,CAACC,KAAK,CACV,mDAAmD,EACnDH,CAAC,CACF;EACH;AACF,CAAC;AAAC"}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clicker Master - Gaming Hub</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #game-container {
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        
        canvas {
            border-radius: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        
        .loading {
            color: #6C5CE7;
            font-size: 24px;
            text-align: center;
        }
        
        .ui-overlay {
            position: absolute;
            top: 20px;
            right: 20px;
            z-index: 1000;
        }
        
        .hub-button {
            background: linear-gradient(135deg, #6C5CE7, #A29BFE);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 2px 10px rgba(108, 92, 231, 0.3);
            transition: transform 0.2s;
        }
        
        .hub-button:hover {
            transform: translateY(-2px);
        }
        
        .score-display {
            background: rgba(255, 255, 255, 0.1);
            color: white;
            padding: 10px 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            font-size: 18px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="loading">Loading Clicker Master...</div>
    </div>
    
    <div class="ui-overlay">
        <div class="score-display" id="score-display">Score: 0</div>
        <button class="hub-button" onclick="goToHub()">Back to Hub</button>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        // Game variables
        let score = 0;
        let gameStartTime = Date.now();
        let clickTarget;
        let scoreText;
        let particleEmitter;
        
        // Gaming Hub integration
        const GameHub = {
            updateScore: function(newScore) {
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'GAME_SCORE',
                        score: newScore
                    }));
                }
                document.getElementById('score-display').textContent = `Score: ${newScore}`;
            },
            
            gameCompleted: function(finalScore) {
                const duration = Math.floor((Date.now() - gameStartTime) / 1000);
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'GAME_COMPLETED',
                        score: finalScore,
                        duration: duration
                    }));
                }
            },
            
            navigateBack: function() {
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'NAVIGATE_BACK'
                    }));
                } else {
                    // Fallback for web testing
                    alert(`Game completed! Final score: ${score}`);
                }
            }
        };
        
        function goToHub() {
            GameHub.gameCompleted(score);
            GameHub.navigateBack();
        }
        
        // Phaser game configuration
        const config = {
            type: Phaser.AUTO,
            width: Math.min(800, window.innerWidth - 40),
            height: Math.min(600, window.innerHeight - 100),
            parent: 'game-container',
            backgroundColor: '#0F3460',
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
            scene: {
                preload: preload,
                create: create,
                update: update
            }
        };
        
        function preload() {
            // Create simple colored rectangles as sprites
            this.add.graphics()
                .fillStyle(0x6C5CE7)
                .fillRect(0, 0, 100, 100)
                .generateTexture('clickTarget', 100, 100);
                
            this.add.graphics()
                .fillStyle(0x00CEC9)
                .fillCircle(5, 5, 5)
                .generateTexture('particle', 10, 10);
        }
        
        function create() {
            // Remove loading text
            document.querySelector('.loading').style.display = 'none';
            
            // Add background gradient effect
            const graphics = this.add.graphics();
            graphics.fillGradientStyle(0x1a1a2e, 0x1a1a2e, 0x16213e, 0x16213e);
            graphics.fillRect(0, 0, config.width, config.height);
            
            // Create click target
            clickTarget = this.add.sprite(config.width / 2, config.height / 2, 'clickTarget')
                .setInteractive({ useHandCursor: true })
                .setScale(1.5);
            
            // Add glow effect
            clickTarget.setTint(0x6C5CE7);
            
            // Create particle system
            particleEmitter = this.add.particles(0, 0, 'particle', {
                speed: { min: 100, max: 200 },
                scale: { start: 0.5, end: 0 },
                lifespan: 600,
                quantity: 5
            });
            particleEmitter.stop();
            
            // Handle click events
            clickTarget.on('pointerdown', handleClick, this);
            clickTarget.on('pointerover', () => {
                clickTarget.setScale(1.6);
                clickTarget.setTint(0xA29BFE);
            });
            clickTarget.on('pointerout', () => {
                clickTarget.setScale(1.5);
                clickTarget.setTint(0x6C5CE7);
            });
            
            // Add instructions
            this.add.text(config.width / 2, 100, 'Click the target to score points!', {
                fontSize: '24px',
                fill: '#FFFFFF',
                fontFamily: 'Arial'
            }).setOrigin(0.5);
            
            // Add score multiplier info
            this.add.text(config.width / 2, config.height - 50, 'Each click = 10 points', {
                fontSize: '16px',
                fill: '#B2B2B2',
                fontFamily: 'Arial'
            }).setOrigin(0.5);
        }
        
        function handleClick() {
            // Increase score
            score += 10;
            GameHub.updateScore(score);
            
            // Create click animation
            this.tweens.add({
                targets: clickTarget,
                scale: { from: 1.6, to: 1.8 },
                duration: 100,
                yoyo: true,
                ease: 'Power2'
            });
            
            // Emit particles
            particleEmitter.setPosition(clickTarget.x, clickTarget.y);
            particleEmitter.explode(8);
            
            // Add floating score text
            const floatingText = this.add.text(
                clickTarget.x + Phaser.Math.Between(-50, 50),
                clickTarget.y - 30,
                '+10',
                {
                    fontSize: '20px',
                    fill: '#00CEC9',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }
            ).setOrigin(0.5);
            
            this.tweens.add({
                targets: floatingText,
                y: floatingText.y - 50,
                alpha: 0,
                duration: 800,
                ease: 'Power2',
                onComplete: () => floatingText.destroy()
            });
            
            // Check for milestones
            if (score === 100) {
                showMilestone('First Century!', '#00B894');
            } else if (score === 500) {
                showMilestone('Half Thousand!', '#FDCB6E');
            } else if (score === 1000) {
                showMilestone('One Thousand!', '#E17055');
            }
        }
        
        function showMilestone(text, color) {
            const milestone = this.add.text(config.width / 2, config.height / 2 - 100, text, {
                fontSize: '32px',
                fill: color,
                fontFamily: 'Arial',
                fontStyle: 'bold'
            }).setOrigin(0.5).setAlpha(0);
            
            this.tweens.add({
                targets: milestone,
                alpha: 1,
                scale: { from: 0.5, to: 1.2 },
                duration: 500,
                ease: 'Back.easeOut',
                onComplete: () => {
                    this.tweens.add({
                        targets: milestone,
                        alpha: 0,
                        y: milestone.y - 50,
                        duration: 1000,
                        delay: 1000,
                        onComplete: () => milestone.destroy()
                    });
                }
            });
        }
        
        function update() {
            // Add subtle rotation to the click target
            if (clickTarget) {
                clickTarget.rotation += 0.01;
            }
        }
        
        // Start the game
        const game = new Phaser.Game(config);
        
        // Handle window resize
        window.addEventListener('resize', () => {
            game.scale.resize(
                Math.min(800, window.innerWidth - 40),
                Math.min(600, window.innerHeight - 100)
            );
        });
    </script>
</body>
</html>

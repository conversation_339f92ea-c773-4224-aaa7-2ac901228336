{"name": "findz-gaming-hub-extension", "displayName": "Findz Gaming Hub", "description": "AI-powered game development extension for the Findz Gaming Hub", "version": "1.0.0", "publisher": "findz", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Snippets", "Debuggers"], "keywords": ["gaming", "game development", "ai", "templates", "phaser", "html5"], "activationEvents": ["onCommand:findz.createGameProject", "onCommand:findz.openGameHub", "onCommand:findz.aiAssist"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "findz.createGameProject", "title": "Create Game Project", "category": "Findz Gaming Hub"}, {"command": "findz.openGameHub", "title": "Open Gaming Hub", "category": "Findz Gaming Hub"}, {"command": "findz.aiAssist", "title": "AI Game Development Assistant", "category": "Findz Gaming Hub"}, {"command": "findz.deployToHub", "title": "Deploy to Gaming Hub", "category": "Findz Gaming Hub"}], "menus": {"explorer/context": [{"command": "findz.createGameProject", "group": "navigation"}], "editor/context": [{"command": "findz.aiAssist", "group": "navigation"}]}, "configuration": {"title": "Findz Gaming Hub", "properties": {"findz.hubUrl": {"type": "string", "default": "http://localhost:8082", "description": "URL of the Findz Gaming Hub"}, "findz.aiEnabled": {"type": "boolean", "default": true, "description": "Enable AI assistance features"}, "findz.autoSync": {"type": "boolean", "default": true, "description": "Automatically sync projects with Gaming Hub"}}}, "snippets": [{"language": "javascript", "path": "./snippets/game-snippets.json"}, {"language": "html", "path": "./snippets/html5-game-snippets.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/express": "^4.17.23", "@types/node": "16.x", "@types/vscode": "^1.74.0", "typescript": "^4.9.4"}, "dependencies": {"axios": "^1.6.0", "express": "^4.18.0"}}
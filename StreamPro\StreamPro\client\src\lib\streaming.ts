export interface StreamConfig {
  video: {
    width: number;
    height: number;
    frameRate: number;
    bitrate: number;
    codec: string;
  };
  audio: {
    sampleRate: number;
    bitrate: number;
    codec: string;
  };
}

export interface StreamQuality {
  label: string;
  width: number;
  height: number;
  bitrate: number;
  frameRate: number;
}

export const STREAM_QUALITIES: StreamQuality[] = [
  {
    label: '1080p',
    width: 1920,
    height: 1080,
    bitrate: 5000,
    frameRate: 60,
  },
  {
    label: '720p',
    width: 1280,
    height: 720,
    bitrate: 2500,
    frameRate: 60,
  },
  {
    label: '480p',
    width: 854,
    height: 480,
    bitrate: 1000,
    frameRate: 30,
  },
  {
    label: '360p',
    width: 640,
    height: 360,
    bitrate: 500,
    frameRate: 30,
  },
];

export class StreamingService {
  private mediaRecorder: MediaRecorder | null = null;
  private recordedChunks: Blob[] = [];
  private isRecording = false;
  
  async getUserMedia(constraints: MediaStreamConstraints): Promise<MediaStream> {
    try {
      return await navigator.mediaDevices.getUserMedia(constraints);
    } catch (error) {
      throw new Error(`Failed to get user media: ${error}`);
    }
  }
  
  async getDisplayMedia(constraints: DisplayMediaStreamConstraints): Promise<MediaStream> {
    try {
      return await navigator.mediaDevices.getDisplayMedia(constraints);
    } catch (error) {
      throw new Error(`Failed to get display media: ${error}`);
    }
  }
  
  createOptimalConstraints(quality: StreamQuality): MediaStreamConstraints {
    return {
      video: {
        width: { exact: quality.width },
        height: { exact: quality.height },
        frameRate: { exact: quality.frameRate },
        facingMode: 'user',
      },
      audio: {
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true,
        sampleRate: 48000,
        channelCount: 2,
      },
    };
  }
  
  startRecording(stream: MediaStream, options: MediaRecorderOptions = {}): void {
    if (this.isRecording) {
      throw new Error('Recording is already in progress');
    }
    
    const defaultOptions: MediaRecorderOptions = {
      mimeType: 'video/webm;codecs=vp9,opus',
      videoBitsPerSecond: 2500000,
      audioBitsPerSecond: 128000,
    };
    
    this.mediaRecorder = new MediaRecorder(stream, { ...defaultOptions, ...options });
    this.recordedChunks = [];
    
    this.mediaRecorder.ondataavailable = (event) => {
      if (event.data.size > 0) {
        this.recordedChunks.push(event.data);
      }
    };
    
    this.mediaRecorder.onstop = () => {
      this.isRecording = false;
    };
    
    this.mediaRecorder.start(1000); // Collect data every second
    this.isRecording = true;
  }
  
  stopRecording(): Promise<Blob> {
    return new Promise((resolve, reject) => {
      if (!this.mediaRecorder || !this.isRecording) {
        reject(new Error('No recording in progress'));
        return;
      }
      
      this.mediaRecorder.onstop = () => {
        const blob = new Blob(this.recordedChunks, { type: 'video/webm' });
        this.recordedChunks = [];
        this.isRecording = false;
        resolve(blob);
      };
      
      this.mediaRecorder.stop();
    });
  }
  
  async uploadRecording(blob: Blob, filename: string): Promise<string> {
    const formData = new FormData();
    formData.append('recording', blob, filename);
    
    const response = await fetch('/api/upload/recording', {
      method: 'POST',
      body: formData,
    });
    
    if (!response.ok) {
      throw new Error('Failed to upload recording');
    }
    
    const result = await response.json();
    return result.url;
  }
  
  detectOptimalQuality(stream: MediaStream): StreamQuality {
    const videoTrack = stream.getVideoTracks()[0];
    if (!videoTrack) {
      return STREAM_QUALITIES[2]; // Default to 480p
    }
    
    const settings = videoTrack.getSettings();
    const width = settings.width || 640;
    const height = settings.height || 480;
    
    // Find the closest quality match
    const aspectRatio = width / height;
    const quality = STREAM_QUALITIES.find(q => {
      const qAspectRatio = q.width / q.height;
      return Math.abs(qAspectRatio - aspectRatio) < 0.1 && q.width <= width;
    });
    
    return quality || STREAM_QUALITIES[STREAM_QUALITIES.length - 1];
  }
  
  async testConnection(): Promise<{
    latency: number;
    bandwidth: number;
    quality: 'excellent' | 'good' | 'fair' | 'poor';
  }> {
    const startTime = Date.now();
    
    try {
      const response = await fetch('/api/test/connection', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ timestamp: startTime }),
      });
      
      const endTime = Date.now();
      const latency = endTime - startTime;
      
      const result = await response.json();
      const bandwidth = result.bandwidth || 1000; // Default bandwidth
      
      let quality: 'excellent' | 'good' | 'fair' | 'poor' = 'poor';
      if (latency < 100 && bandwidth > 5000) quality = 'excellent';
      else if (latency < 200 && bandwidth > 2500) quality = 'good';
      else if (latency < 500 && bandwidth > 1000) quality = 'fair';
      
      return { latency, bandwidth, quality };
    } catch (error) {
      return { latency: 999, bandwidth: 0, quality: 'poor' };
    }
  }
  
  getRecommendedQuality(bandwidth: number): StreamQuality {
    if (bandwidth > 5000) return STREAM_QUALITIES[0]; // 1080p
    if (bandwidth > 2500) return STREAM_QUALITIES[1]; // 720p
    if (bandwidth > 1000) return STREAM_QUALITIES[2]; // 480p
    return STREAM_QUALITIES[3]; // 360p
  }
  
  generateStreamKey(): string {
    return `sk_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }
  
  validateStreamKey(key: string): boolean {
    return /^sk_\d+_[a-z0-9]+$/.test(key);
  }
}

export const streamingService = new StreamingService();

-- Findz Gaming Streaming Platform - Database Initialization
-- This script sets up the initial database structure and data

-- Create extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Create gaming categories
INSERT INTO game_categories (name, description, icon, is_active) VALUES
('Game Development', 'Live coding and game development streams', 'code', true),
('Competitive Gaming', 'Esports and competitive gaming sessions', 'trophy', true),
('Casual Gaming', 'Relaxed gaming and entertainment', 'gamepad', true),
('Tutorial', 'Gaming tutorials and educational content', 'book', true),
('Speedrun', 'Speedrunning and challenge runs', 'timer', true),
('Indie Games', 'Independent and experimental games', 'star', true),
('Retro Gaming', 'Classic and vintage gaming', 'history', true),
('VR Gaming', 'Virtual reality gaming experiences', 'vr-headset', true)
ON CONFLICT (name) DO NOTHING;

-- Create default admin user (for demo purposes)
INSERT INTO users (username, email, display_name, avatar_url, is_verified, created_at) VALUES
('findz_admin', '<EMAIL>', 'Findz Gaming Admin', '/avatars/admin.png', true, NOW()),
('demo_streamer', '<EMAIL>', 'Demo Gaming Streamer', '/avatars/demo.png', true, NOW()),
('ai_assistant', '<EMAIL>', 'Findz AI Assistant', '/avatars/ai.png', true, NOW())
ON CONFLICT (email) DO NOTHING;

-- Create sample gaming streams
INSERT INTO gaming_streams (user_id, title, description, stream_key, status, game_category, game_title, quality, ai_assistance_enabled, created_at) VALUES
(1, 'Building HTML5 Games with AI Assistance', 'Live coding session creating games with AI help', 'demo_stream_001', 'inactive', 'game-dev', 'HTML5 Platformer', '720p', true, NOW()),
(2, 'Competitive Gaming with AI Coach', 'Professional gaming with AI performance analysis', 'demo_stream_002', 'inactive', 'competitive', 'Strategy Game', '1080p', true, NOW()),
(1, 'Indie Game Development Tutorial', 'Step-by-step game creation tutorial', 'demo_stream_003', 'inactive', 'tutorial', 'Puzzle Game', '720p', true, NOW())
ON CONFLICT (stream_key) DO NOTHING;

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_gaming_streams_status ON gaming_streams(status);
CREATE INDEX IF NOT EXISTS idx_gaming_streams_category ON gaming_streams(game_category);
CREATE INDEX IF NOT EXISTS idx_gaming_streams_user ON gaming_streams(user_id);
CREATE INDEX IF NOT EXISTS idx_gaming_streams_created ON gaming_streams(created_at);

CREATE INDEX IF NOT EXISTS idx_stream_analytics_stream ON stream_analytics(stream_id);
CREATE INDEX IF NOT EXISTS idx_stream_analytics_timestamp ON stream_analytics(timestamp);

CREATE INDEX IF NOT EXISTS idx_chat_messages_stream ON chat_messages(stream_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_timestamp ON chat_messages(timestamp);

CREATE INDEX IF NOT EXISTS idx_ai_assistance_stream ON ai_stream_assistance(stream_id);
CREATE INDEX IF NOT EXISTS idx_ai_assistance_type ON ai_stream_assistance(assistance_type);

-- Create functions for gaming-specific features
CREATE OR REPLACE FUNCTION update_stream_viewer_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE gaming_streams 
        SET viewer_count = viewer_count + 1,
            max_viewers = GREATEST(max_viewers, viewer_count + 1)
        WHERE id = NEW.stream_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE gaming_streams 
        SET viewer_count = GREATEST(0, viewer_count - 1)
        WHERE id = OLD.stream_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_update_viewer_count ON stream_viewers;
CREATE TRIGGER trigger_update_viewer_count
    AFTER INSERT OR DELETE ON stream_viewers
    FOR EACH ROW EXECUTE FUNCTION update_stream_viewer_count();

-- Create function to clean up old analytics data
CREATE OR REPLACE FUNCTION cleanup_old_analytics()
RETURNS void AS $$
BEGIN
    -- Keep only last 30 days of detailed analytics
    DELETE FROM stream_analytics 
    WHERE timestamp < NOW() - INTERVAL '30 days';
    
    -- Keep only last 7 days of performance metrics
    DELETE FROM stream_performance_metrics 
    WHERE timestamp < NOW() - INTERVAL '7 days';
    
    -- Archive old chat messages (keep last 90 days)
    DELETE FROM chat_messages 
    WHERE timestamp < NOW() - INTERVAL '90 days';
END;
$$ LANGUAGE plpgsql;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO findz_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO findz_user;
GRANT EXECUTE ON ALL FUNCTIONS IN SCHEMA public TO findz_user;

-- Insert success message
INSERT INTO chat_messages (stream_id, user_id, message, type, timestamp) VALUES
(1, 3, 'Welcome to Findz Gaming Streaming Platform! 🎮 Database initialized successfully!', 'system', NOW())
ON CONFLICT DO NOTHING;

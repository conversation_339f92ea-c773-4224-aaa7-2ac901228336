{"summary": {"totalTests": 7, "passedTests": 7, "failedTests": 0, "successRate": 100, "totalTime": 13683, "timestamp": "2025-07-08T09:13:49.726Z"}, "results": [{"service": "Environment", "test": "Configuration Check", "success": true, "message": "5/5 variables configured", "timestamp": "2025-07-08T09:13:36.043Z"}, {"service": "Ollama", "test": "Model Test", "success": true, "message": "deepseek-coder:6.7b-instruct responded in 2462ms", "timestamp": "2025-07-08T09:13:38.517Z"}, {"service": "Ollama", "test": "Connection", "success": true, "message": "Connected in 11ms", "timestamp": "2025-07-08T09:13:38.517Z"}, {"service": "DeepSeek", "test": "API Test", "success": true, "message": "Responded in 7692ms", "timestamp": "2025-07-08T09:13:46.211Z"}, {"service": "OpenRouter", "test": "API Test", "success": true, "message": "Responded in 1428ms", "timestamp": "2025-07-08T09:13:47.639Z"}, {"service": "Anthropic", "test": "API Test", "success": true, "message": "Responded in 1789ms", "timestamp": "2025-07-08T09:13:49.429Z"}, {"service": "ElevenLabs", "test": "API Test", "success": true, "message": "19 voices available", "timestamp": "2025-07-08T09:13:49.724Z"}]}
import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
  Dimensions,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { WebView } from 'react-native-webview';
import { theme, colors } from '../theme';
import { agentAIService } from '../services/agentAIService';
import { agoraService } from '../services/agoraService';

const { width, height } = Dimensions.get('window');

interface StreamingScreenProps {
  navigation: any;
}

interface StreamData {
  id: string;
  title: string;
  streamer: string;
  viewers: number;
  category: string;
  thumbnail: string;
  isLive: boolean;
}

const StreamingScreen: React.FC<StreamingScreenProps> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [streamingPlatformReady, setStreamingPlatformReady] = useState(false);
  const [showWebView, setShowWebView] = useState(false);
  const [featuredStreams, setFeaturedStreams] = useState<StreamData[]>([]);
  const [aiSuggestions, setAiSuggestions] = useState<string[]>([]);
  const [agoraReady, setAgoraReady] = useState(false);

  useEffect(() => {
    initializeStreaming();
    loadFeaturedStreams();
    getAIStreamingSuggestions();
  }, []);

  const initializeStreaming = async () => {
    try {
      // Check Agora configuration
      const agoraStatus = agoraService.getConfigStatus();
      if (agoraStatus.hasAppId) {
        const initResult = await agoraService.initialize();
        if (initResult.success) {
          setAgoraReady(true);
          console.log('✅ Agora service initialized successfully');
        } else {
          console.warn('⚠️ Agora initialization failed:', initResult.error);
        }
      } else {
        console.warn('⚠️ Agora App ID not configured');
      }

      // Check if streaming platform is available
      const response = await fetch('http://localhost:5001/api/health');
      if (response.ok) {
        setStreamingPlatformReady(true);
      }
    } catch (error) {
      console.log('Streaming platform not available, showing setup instructions');
    } finally {
      setIsLoading(false);
    }
  };

  const loadFeaturedStreams = () => {
    // Mock data for featured streams
    const mockStreams: StreamData[] = [
      {
        id: '1',
        title: 'AI-Assisted Game Development Live!',
        streamer: 'CodeMaster_AI',
        viewers: 1247,
        category: 'Game Development',
        thumbnail: 'https://via.placeholder.com/300x200',
        isLive: true,
      },
      {
        id: '2',
        title: 'Competitive Gaming with AI Coach',
        streamer: 'ProGamer_Plus',
        viewers: 892,
        category: 'Competitive Gaming',
        thumbnail: 'https://via.placeholder.com/300x200',
        isLive: true,
      },
      {
        id: '3',
        title: 'Building HTML5 Games Tutorial',
        streamer: 'GameDev_Guru',
        viewers: 634,
        category: 'Tutorial',
        thumbnail: 'https://via.placeholder.com/300x200',
        isLive: false,
      },
    ];
    setFeaturedStreams(mockStreams);
  };

  const getAIStreamingSuggestions = async () => {
    try {
      const response = await agentAIService.generateAgentResponse([
        {
          id: 'streaming-suggestions',
          role: 'user',
          content: 'Give me 3 creative ideas for gaming streams that would be engaging for viewers.',
          timestamp: new Date(),
        }
      ], {
        gameContext: undefined,
        userSkillLevel: 'intermediate',
        previousInteractions: [],
        currentGoals: ['streaming', 'content creation'],
        capabilities: {
          codeAnalysis: true,
          gameStrategy: true,
          debugging: true,
          optimization: true,
          learning: true,
        },
      });

      // Parse AI suggestions
      const suggestions = response.content.split('\n')
        .filter(line => line.trim().length > 10)
        .slice(0, 3);
      
      setAiSuggestions(suggestions);
    } catch (error) {
      console.error('Error getting AI suggestions:', error);
      setAiSuggestions([
        'Stream your game development process with live AI assistance',
        'Host interactive gaming sessions with AI coaching',
        'Create tutorial streams with AI-generated explanations',
      ]);
    }
  };

  const handleStartStreaming = () => {
    if (streamingPlatformReady) {
      setShowWebView(true);
    } else {
      Alert.alert(
        'Streaming Platform Setup',
        'The streaming platform needs to be set up first. Would you like to see setup instructions?',
        [
          { text: 'Cancel', style: 'cancel' },
          { text: 'Setup Instructions', onPress: showSetupInstructions },
        ]
      );
    }
  };

  const showSetupInstructions = () => {
    Alert.alert(
      'Streaming Platform Setup',
      'To enable streaming:\n\n1. Open terminal in StreamPro folder\n2. Run: npm run dev\n3. Platform will be available on port 5000\n4. Return here to start streaming!',
      [{ text: 'Got it!' }]
    );
  };

  const handleOpenExternalStreaming = async () => {
    const url = 'http://localhost:5001';
    const supported = await Linking.canOpenURL(url);
    
    if (supported) {
      await Linking.openURL(url);
    } else {
      Alert.alert('Error', 'Cannot open streaming platform');
    }
  };

  const renderStreamCard = (stream: StreamData) => (
    <TouchableOpacity key={stream.id} style={styles.streamCard}>
      <View style={styles.streamThumbnail}>
        <View style={styles.thumbnailPlaceholder}>
          <Ionicons name="videocam" size={40} color={colors.textSecondary} />
        </View>
        {stream.isLive && (
          <View style={styles.liveIndicator}>
            <Text style={styles.liveText}>LIVE</Text>
          </View>
        )}
        <View style={styles.viewerCount}>
          <Ionicons name="eye" size={12} color={colors.text} />
          <Text style={styles.viewerText}>{stream.viewers}</Text>
        </View>
      </View>
      <View style={styles.streamInfo}>
        <Text style={styles.streamTitle} numberOfLines={2}>
          {stream.title}
        </Text>
        <Text style={styles.streamerName}>{stream.streamer}</Text>
        <Text style={styles.streamCategory}>{stream.category}</Text>
      </View>
    </TouchableOpacity>
  );

  if (showWebView && streamingPlatformReady) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.webViewHeader}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => setShowWebView(false)}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
            <Text style={styles.backText}>Back to Hub</Text>
          </TouchableOpacity>
          <Text style={styles.webViewTitle}>Findz Gaming Streaming</Text>
        </View>
        <WebView
          source={{ uri: 'http://localhost:5001' }}
          style={styles.webView}
          onError={() => {
            Alert.alert('Error', 'Failed to load streaming platform');
            setShowWebView(false);
          }}
        />
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Header */}
        <View style={styles.header}>
          <View>
            <Text style={styles.title}>Live Streaming</Text>
            <Text style={styles.subtitle}>
              Professional gaming streams with AI assistance
            </Text>
          </View>
          <TouchableOpacity style={styles.settingsButton}>
            <Ionicons name="settings-outline" size={24} color={colors.text} />
          </TouchableOpacity>
        </View>

        {/* Quick Actions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.actionGrid}>
            <TouchableOpacity style={styles.actionCard} onPress={handleStartStreaming}>
              <Ionicons name="videocam" size={32} color={colors.primary} />
              <Text style={styles.actionTitle}>Start Streaming</Text>
              <Text style={styles.actionSubtitle}>Go live with AI assistance</Text>
            </TouchableOpacity>
            
            <TouchableOpacity style={styles.actionCard} onPress={handleOpenExternalStreaming}>
              <Ionicons name="desktop" size={32} color={colors.secondary} />
              <Text style={styles.actionTitle}>Full Platform</Text>
              <Text style={styles.actionSubtitle}>Open in browser</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Platform Status */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Platform Status</Text>
          <View style={styles.statusCard}>
            <View style={styles.statusIndicator}>
              <View style={[
                styles.statusDot,
                { backgroundColor: streamingPlatformReady ? colors.success : colors.error }
              ]} />
              <Text style={styles.statusText}>
                Streaming Platform: {streamingPlatformReady ? 'Ready' : 'Setup Required'}
              </Text>
            </View>
            <View style={styles.statusIndicator}>
              <View style={[
                styles.statusDot,
                { backgroundColor: agoraReady ? colors.success : colors.warning }
              ]} />
              <Text style={styles.statusText}>
                Agora Video: {agoraReady ? 'Ready' : agoraService.isConfigured() ? 'Configured' : 'Not Configured'}
              </Text>
            </View>
            {!streamingPlatformReady && (
              <TouchableOpacity style={styles.setupButton} onPress={showSetupInstructions}>
                <Text style={styles.setupButtonText}>Setup Instructions</Text>
              </TouchableOpacity>
            )}
          </View>
        </View>

        {/* AI Suggestions */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>AI Stream Ideas</Text>
          {aiSuggestions.map((suggestion, index) => (
            <View key={index} style={styles.suggestionCard}>
              <Ionicons name="bulb" size={20} color={colors.primary} />
              <Text style={styles.suggestionText}>{suggestion}</Text>
            </View>
          ))}
        </View>

        {/* Featured Streams */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Featured Streams</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <View style={styles.streamGrid}>
              {featuredStreams.map(renderStreamCard)}
            </View>
          </ScrollView>
        </View>

        {/* Features */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Platform Features</Text>
          <View style={styles.featureList}>
            <View style={styles.featureItem}>
              <Ionicons name="videocam" size={20} color={colors.primary} />
              <Text style={styles.featureText}>WebRTC Live Streaming</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="chatbubbles" size={20} color={colors.primary} />
              <Text style={styles.featureText}>Real-time Chat</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="analytics" size={20} color={colors.primary} />
              <Text style={styles.featureText}>Stream Analytics</Text>
            </View>
            <View style={styles.featureItem}>
              <Ionicons name="hardware-chip" size={20} color={colors.primary} />
              <Text style={styles.featureText}>AI-Powered Assistance</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: theme.spacing.lg,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  title: {
    ...theme.typography.textStyles.h1,
    color: colors.text,
    marginBottom: theme.spacing.xs,
  },
  subtitle: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
  },
  settingsButton: {
    padding: theme.spacing.sm,
  },
  section: {
    padding: theme.spacing.lg,
  },
  sectionTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginBottom: theme.spacing.md,
  },
  actionGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  actionCard: {
    flex: 1,
    backgroundColor: colors.surface,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: colors.border,
  },
  actionTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  actionSubtitle: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
    textAlign: 'center',
  },
  statusCard: {
    backgroundColor: colors.surface,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    borderWidth: 1,
    borderColor: colors.border,
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: theme.spacing.sm,
  },
  statusText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
  },
  setupButton: {
    backgroundColor: colors.primary,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    alignItems: 'center',
  },
  setupButtonText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
  },
  suggestionCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.sm,
    borderWidth: 1,
    borderColor: colors.border,
  },
  suggestionText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginLeft: theme.spacing.sm,
    flex: 1,
  },
  streamGrid: {
    flexDirection: 'row',
    gap: theme.spacing.md,
  },
  streamCard: {
    width: 200,
    backgroundColor: colors.surface,
    borderRadius: theme.borderRadius.lg,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: colors.border,
  },
  streamThumbnail: {
    height: 120,
    position: 'relative',
  },
  thumbnailPlaceholder: {
    flex: 1,
    backgroundColor: colors.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  liveIndicator: {
    position: 'absolute',
    top: theme.spacing.sm,
    left: theme.spacing.sm,
    backgroundColor: colors.error,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  liveText: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
    fontWeight: 'bold',
  },
  viewerCount: {
    position: 'absolute',
    bottom: theme.spacing.sm,
    right: theme.spacing.sm,
    backgroundColor: 'rgba(0,0,0,0.7)',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  viewerText: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
    marginLeft: theme.spacing.xs,
  },
  streamInfo: {
    padding: theme.spacing.md,
  },
  streamTitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
    marginBottom: theme.spacing.xs,
  },
  streamerName: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  streamCategory: {
    ...theme.typography.textStyles.caption,
    color: colors.primary,
  },
  featureList: {
    gap: theme.spacing.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  featureText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginLeft: theme.spacing.md,
  },
  webViewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: theme.spacing.md,
    backgroundColor: colors.surface,
    borderBottomWidth: 1,
    borderBottomColor: colors.border,
  },
  backButton: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginLeft: theme.spacing.sm,
  },
  webViewTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    flex: 1,
    textAlign: 'center',
    marginRight: theme.spacing.xl,
  },
  webView: {
    flex: 1,
  },
});

export default StreamingScreen;

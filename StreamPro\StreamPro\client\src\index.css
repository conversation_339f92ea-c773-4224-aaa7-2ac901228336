@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 3.7%, 15.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
  
  /* StreamEngine specific colors */
  --dark: hsl(240, 10%, 7.1%);
  --dark-surface: hsl(240, 3.7%, 11.8%);
  --dark-card: hsl(240, 3.7%, 17.6%);
  --success: hsl(142, 76%, 36%);
  --warning: hsl(38, 92%, 50%);
  --danger: hsl(0, 84%, 60%);
}

.dark {
  --background: hsl(240, 10%, 3.9%);
  --foreground: hsl(0, 0%, 98%);
  --muted: hsl(240, 3.7%, 15.9%);
  --muted-foreground: hsl(240, 5%, 64.9%);
  --popover: hsl(240, 10%, 3.9%);
  --popover-foreground: hsl(0, 0%, 98%);
  --card: hsl(240, 3.7%, 15.9%);
  --card-foreground: hsl(0, 0%, 98%);
  --border: hsl(240, 3.7%, 15.9%);
  --input: hsl(240, 3.7%, 15.9%);
  --primary: hsl(207, 90%, 54%);
  --primary-foreground: hsl(211, 100%, 99%);
  --secondary: hsl(240, 3.7%, 15.9%);
  --secondary-foreground: hsl(0, 0%, 98%);
  --accent: hsl(240, 3.7%, 15.9%);
  --accent-foreground: hsl(0, 0%, 98%);
  --destructive: hsl(0, 62.8%, 30.6%);
  --destructive-foreground: hsl(0, 0%, 98%);
  --ring: hsl(240, 4.9%, 83.9%);
  --radius: 0.5rem;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  html {
    @apply dark;
  }
}

@layer components {
  .sidebar-nav-item {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors;
  }
  
  .sidebar-nav-item.active {
    @apply bg-primary text-primary-foreground;
  }
  
  .sidebar-nav-item:not(.active) {
    @apply text-muted-foreground hover:bg-accent hover:text-accent-foreground;
  }
  
  .stream-player-overlay {
    @apply absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 transition-opacity duration-300;
  }
  
  .performance-metric {
    @apply flex items-center justify-between mb-2;
  }
  
  .metric-progress {
    @apply w-full bg-muted rounded-full h-2;
  }
  
  .metric-progress-bar {
    @apply h-2 rounded-full transition-all duration-300;
  }
  
  .metric-progress-bar.success {
    @apply bg-green-500;
  }
  
  .metric-progress-bar.warning {
    @apply bg-yellow-500;
  }
  
  .metric-progress-bar.primary {
    @apply bg-primary;
  }
  
  .metric-progress-bar.danger {
    @apply bg-red-500;
  }
  
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-indicator.live {
    @apply bg-red-500/20 text-red-400;
  }
  
  .status-indicator.scheduled {
    @apply bg-yellow-500/20 text-yellow-400;
  }
  
  .status-indicator.inactive {
    @apply bg-gray-500/20 text-gray-400;
  }
  
  .status-indicator.connected {
    @apply bg-green-500/20 text-green-400;
  }
  
  .pulse-dot {
    @apply w-2 h-2 rounded-full mr-1 animate-pulse;
  }
  
  .pulse-dot.red {
    @apply bg-red-500;
  }
  
  .pulse-dot.green {
    @apply bg-green-500;
  }
  
  .integration-card {
    @apply bg-card/50 rounded-lg p-4 border border-border;
  }
  
  .chat-message {
    @apply flex items-start space-x-3;
  }
  
  .chat-avatar {
    @apply w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 text-white text-xs font-medium;
  }
  
  .chat-avatar.variant-1 {
    @apply bg-red-500;
  }
  
  .chat-avatar.variant-2 {
    @apply bg-blue-500;
  }
  
  .chat-avatar.variant-3 {
    @apply bg-green-500;
  }
  
  .chat-avatar.variant-4 {
    @apply bg-yellow-500;
  }
  
  .chat-avatar.variant-5 {
    @apply bg-purple-500;
  }
  
  .chat-avatar.variant-6 {
    @apply bg-pink-500;
  }
  
  .chat-avatar.variant-7 {
    @apply bg-indigo-500;
  }
  
  .chat-avatar.variant-8 {
    @apply bg-teal-500;
  }
  
  .stream-control-button {
    @apply w-10 h-10 bg-white/20 hover:bg-white/30 text-white rounded-full flex items-center justify-center transition-all;
  }
  
  .stream-quality-selector {
    @apply bg-black/50 border-white/30 text-white;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full border-b-2 border-primary;
  }
  
  .glassmorphism {
    @apply bg-white/10 backdrop-blur-sm border border-white/20;
  }
  
  .metric-card {
    @apply bg-card/50 p-4 rounded-lg border border-border;
  }
  
  .metric-header {
    @apply flex items-center justify-between mb-2;
  }
  
  .metric-icon {
    @apply w-4 h-4 text-muted-foreground;
  }
  
  .metric-value {
    @apply text-lg font-semibold;
  }
  
  .metric-change {
    @apply text-xs text-muted-foreground;
  }
  
  .metric-change.positive {
    @apply text-green-400;
  }
  
  .metric-change.negative {
    @apply text-red-400;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-primary to-blue-400 bg-clip-text text-transparent;
  }
  
  .border-gradient {
    @apply border border-transparent bg-gradient-to-r from-primary/20 to-blue-400/20 bg-clip-border;
  }
  
  .shadow-glow {
    box-shadow: 0 0 20px rgba(32, 129, 226, 0.3);
  }
  
  .shadow-glow-success {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
  }
  
  .shadow-glow-warning {
    box-shadow: 0 0 20px rgba(245, 158, 11, 0.3);
  }
  
  .shadow-glow-danger {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.3);
  }
  
  .backdrop-blur-xs {
    backdrop-filter: blur(2px);
  }
  
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: hsl(240, 3.7%, 25%) transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb {
    background-color: hsl(240, 3.7%, 25%);
    border-radius: 3px;
  }
  
  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background-color: hsl(240, 3.7%, 35%);
  }
  
  .aspect-video {
    aspect-ratio: 16 / 9;
  }
  
  .font-mono {
    font-family: 'JetBrains Mono', 'Fira Code', 'Monaco', 'Cascadia Code', 'Roboto Mono', monospace;
  }
}

/* Custom scrollbar styles */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: hsl(240, 3.7%, 15.9%);
}

::-webkit-scrollbar-thumb {
  background: hsl(240, 3.7%, 25%);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: hsl(240, 3.7%, 35%);
}

/* Animation keyframes */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
  }
  50% {
    box-shadow: 0 0 20px rgba(239, 68, 68, 0.8);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s infinite;
}

/* Live indicator */
.live-indicator {
  position: relative;
}

.live-indicator::before {
  content: '';
  position: absolute;
  top: 50%;
  left: -8px;
  width: 6px;
  height: 6px;
  background: #ef4444;
  border-radius: 50%;
  transform: translateY(-50%);
  animation: pulse 2s infinite;
}

/* Enhanced focus states */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 focus:ring-offset-background;
}

/* Enhanced button variants */
.btn-gradient {
  background: linear-gradient(135deg, hsl(207, 90%, 54%) 0%, hsl(207, 90%, 64%) 100%);
  transition: all 0.2s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, hsl(207, 90%, 64%) 0%, hsl(207, 90%, 74%) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(32, 129, 226, 0.3);
}

/* Enhanced card styles */
.card-elevated {
  @apply bg-card border border-border shadow-lg;
  background: linear-gradient(135deg, hsl(240, 3.7%, 15.9%) 0%, hsl(240, 3.7%, 17.6%) 100%);
}

.card-elevated:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Status badges */
.badge-live {
  @apply bg-red-500/20 text-red-400 border border-red-500/30;
}

.badge-scheduled {
  @apply bg-yellow-500/20 text-yellow-400 border border-yellow-500/30;
}

.badge-connected {
  @apply bg-green-500/20 text-green-400 border border-green-500/30;
}

.badge-inactive {
  @apply bg-gray-500/20 text-gray-400 border border-gray-500/30;
}

/* Loading states */
.skeleton {
  @apply bg-muted animate-pulse rounded;
}

/* Grid auto-fit utilities */
.grid-auto-fit-sm {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}

.grid-auto-fit-md {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.grid-auto-fit-lg {
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
}

#!/usr/bin/env node

/**
 * AI Integration Test Script
 * Comprehensive testing of all AI services and providers
 */

const axios = require('axios');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config({ path: path.join(__dirname, '../.env') });

const OLLAMA_BASE_URL = process.env.EXPO_PUBLIC_OLLAMA_BASE_URL || 'http://127.0.0.1:11434';
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const OPENROUTER_API_KEY = process.env.OPENROUTER_API_KEY;
const ANTHROPIC_API_KEY = process.env.ANTHROPIC_API_KEY;
const ELEVENLABS_API_KEY = process.env.ELEVENLABS_API_KEY;

class AIIntegrationTester {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
  }

  async runAllTests() {
    console.log('🧪 AI Integration Test Suite');
    console.log('============================\n');

    // Test environment configuration
    await this.testEnvironmentConfig();

    // Test Ollama
    await this.testOllamaIntegration();

    // Test cloud providers
    await this.testDeepSeekIntegration();
    await this.testOpenRouterIntegration();
    await this.testAnthropicIntegration();

    // Test voice services
    await this.testElevenLabsIntegration();

    // Generate report
    this.generateReport();
  }

  async testEnvironmentConfig() {
    console.log('🔧 Testing Environment Configuration...');
    
    const requiredVars = [
      'EXPO_PUBLIC_OLLAMA_BASE_URL',
      'DEEPSEEK_API_KEY',
      'OPENROUTER_API_KEY',
      'ANTHROPIC_API_KEY',
      'ELEVENLABS_API_KEY',
    ];

    const configResults = [];
    
    for (const varName of requiredVars) {
      const value = process.env[varName];
      const isConfigured = value && value.length > 0;
      
      configResults.push({
        variable: varName,
        configured: isConfigured,
        masked: isConfigured ? `${value.substring(0, 8)}...` : 'Not set',
      });
      
      console.log(`   ${isConfigured ? '✅' : '❌'} ${varName}: ${isConfigured ? 'Configured' : 'Missing'}`);
    }

    this.addResult('Environment', 'Configuration Check', configResults.every(r => r.configured), 
      `${configResults.filter(r => r.configured).length}/${configResults.length} variables configured`);
    
    console.log('');
  }

  async testOllamaIntegration() {
    console.log('🤖 Testing Ollama Integration...');
    
    try {
      // Test connection
      const startTime = Date.now();
      const response = await axios.get(`${OLLAMA_BASE_URL}/api/tags`, { timeout: 5000 });
      const connectionTime = Date.now() - startTime;
      
      if (response.status === 200) {
        console.log(`   ✅ Connection successful (${connectionTime}ms)`);
        
        const models = response.data.models || [];
        console.log(`   📦 Available models: ${models.length}`);
        
        if (models.length > 0) {
          // Test first available model
          const testModel = models[0].name;
          console.log(`   🧪 Testing model: ${testModel}`);
          
          const testStartTime = Date.now();
          const testResponse = await axios.post(
            `${OLLAMA_BASE_URL}/api/generate`,
            {
              model: testModel,
              prompt: 'Hello! Give me a quick gaming tip.',
              stream: false,
            },
            { timeout: 30000 }
          );
          
          const testTime = Date.now() - testStartTime;
          
          if (testResponse.data.response) {
            console.log(`   ✅ Model test successful (${testTime}ms)`);
            console.log(`   💬 Response: "${testResponse.data.response.substring(0, 100)}..."`);
            this.addResult('Ollama', 'Model Test', true, `${testModel} responded in ${testTime}ms`);
          } else {
            console.log(`   ❌ Model test failed: Empty response`);
            this.addResult('Ollama', 'Model Test', false, 'Empty response from model');
          }
        } else {
          console.log(`   ⚠️  No models installed`);
          this.addResult('Ollama', 'Model Test', false, 'No models available');
        }
        
        this.addResult('Ollama', 'Connection', true, `Connected in ${connectionTime}ms`);
      } else {
        throw new Error(`HTTP ${response.status}`);
      }
    } catch (error) {
      console.log(`   ❌ Connection failed: ${error.message}`);
      this.addResult('Ollama', 'Connection', false, error.message);
    }
    
    console.log('');
  }

  async testDeepSeekIntegration() {
    console.log('🔮 Testing DeepSeek Integration...');
    
    if (!DEEPSEEK_API_KEY) {
      console.log('   ⚠️  API key not configured');
      this.addResult('DeepSeek', 'Configuration', false, 'API key missing');
      console.log('');
      return;
    }

    try {
      const startTime = Date.now();
      const response = await axios.post(
        'https://api.deepseek.com/chat/completions',
        {
          model: 'deepseek-chat',
          messages: [
            { role: 'user', content: 'Hello! Give me a quick gaming tip.' }
          ],
          max_tokens: 100,
        },
        {
          headers: {
            'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      const responseTime = Date.now() - startTime;
      
      if (response.data.choices && response.data.choices[0]) {
        console.log(`   ✅ API test successful (${responseTime}ms)`);
        console.log(`   💬 Response: "${response.data.choices[0].message.content.substring(0, 100)}..."`);
        this.addResult('DeepSeek', 'API Test', true, `Responded in ${responseTime}ms`);
      } else {
        console.log(`   ❌ API test failed: Invalid response format`);
        this.addResult('DeepSeek', 'API Test', false, 'Invalid response format');
      }
    } catch (error) {
      console.log(`   ❌ API test failed: ${error.response?.data?.message || error.message}`);
      this.addResult('DeepSeek', 'API Test', false, error.response?.data?.message || error.message);
    }
    
    console.log('');
  }

  async testOpenRouterIntegration() {
    console.log('🌐 Testing OpenRouter Integration...');
    
    if (!OPENROUTER_API_KEY) {
      console.log('   ⚠️  API key not configured');
      this.addResult('OpenRouter', 'Configuration', false, 'API key missing');
      console.log('');
      return;
    }

    try {
      const startTime = Date.now();
      const response = await axios.post(
        'https://openrouter.ai/api/v1/chat/completions',
        {
          model: 'mistralai/mistral-7b-instruct',
          messages: [
            { role: 'user', content: 'Hello! Give me a quick gaming tip.' }
          ],
          max_tokens: 100,
        },
        {
          headers: {
            'Authorization': `Bearer ${OPENROUTER_API_KEY}`,
            'Content-Type': 'application/json',
          },
          timeout: 30000,
        }
      );

      const responseTime = Date.now() - startTime;
      
      if (response.data.choices && response.data.choices[0]) {
        console.log(`   ✅ API test successful (${responseTime}ms)`);
        console.log(`   💬 Response: "${response.data.choices[0].message.content.substring(0, 100)}..."`);
        this.addResult('OpenRouter', 'API Test', true, `Responded in ${responseTime}ms`);
      } else {
        console.log(`   ❌ API test failed: Invalid response format`);
        this.addResult('OpenRouter', 'API Test', false, 'Invalid response format');
      }
    } catch (error) {
      console.log(`   ❌ API test failed: ${error.response?.data?.error?.message || error.message}`);
      this.addResult('OpenRouter', 'API Test', false, error.response?.data?.error?.message || error.message);
    }
    
    console.log('');
  }

  async testAnthropicIntegration() {
    console.log('🧠 Testing Anthropic Integration...');
    
    if (!ANTHROPIC_API_KEY) {
      console.log('   ⚠️  API key not configured');
      this.addResult('Anthropic', 'Configuration', false, 'API key missing');
      console.log('');
      return;
    }

    try {
      const startTime = Date.now();
      const response = await axios.post(
        'https://api.anthropic.com/v1/messages',
        {
          model: 'claude-3-haiku-20240307',
          max_tokens: 100,
          messages: [
            { role: 'user', content: 'Hello! Give me a quick gaming tip.' }
          ],
        },
        {
          headers: {
            'x-api-key': ANTHROPIC_API_KEY,
            'Content-Type': 'application/json',
            'anthropic-version': '2023-06-01',
          },
          timeout: 30000,
        }
      );

      const responseTime = Date.now() - startTime;
      
      if (response.data.content && response.data.content[0]) {
        console.log(`   ✅ API test successful (${responseTime}ms)`);
        console.log(`   💬 Response: "${response.data.content[0].text.substring(0, 100)}..."`);
        this.addResult('Anthropic', 'API Test', true, `Responded in ${responseTime}ms`);
      } else {
        console.log(`   ❌ API test failed: Invalid response format`);
        this.addResult('Anthropic', 'API Test', false, 'Invalid response format');
      }
    } catch (error) {
      console.log(`   ❌ API test failed: ${error.response?.data?.error?.message || error.message}`);
      this.addResult('Anthropic', 'API Test', false, error.response?.data?.error?.message || error.message);
    }
    
    console.log('');
  }

  async testElevenLabsIntegration() {
    console.log('🔊 Testing ElevenLabs Integration...');
    
    if (!ELEVENLABS_API_KEY) {
      console.log('   ⚠️  API key not configured');
      this.addResult('ElevenLabs', 'Configuration', false, 'API key missing');
      console.log('');
      return;
    }

    try {
      // Test API key by getting voices
      const startTime = Date.now();
      const response = await axios.get(
        'https://api.elevenlabs.io/v1/voices',
        {
          headers: {
            'xi-api-key': ELEVENLABS_API_KEY,
          },
          timeout: 10000,
        }
      );

      const responseTime = Date.now() - startTime;
      
      if (response.data.voices && response.data.voices.length > 0) {
        console.log(`   ✅ API test successful (${responseTime}ms)`);
        console.log(`   🎤 Available voices: ${response.data.voices.length}`);
        this.addResult('ElevenLabs', 'API Test', true, `${response.data.voices.length} voices available`);
      } else {
        console.log(`   ❌ API test failed: No voices available`);
        this.addResult('ElevenLabs', 'API Test', false, 'No voices available');
      }
    } catch (error) {
      console.log(`   ❌ API test failed: ${error.response?.data?.detail?.message || error.message}`);
      this.addResult('ElevenLabs', 'API Test', false, error.response?.data?.detail?.message || error.message);
    }
    
    console.log('');
  }

  addResult(service, test, success, message) {
    this.results.push({
      service,
      test,
      success,
      message,
      timestamp: new Date().toISOString(),
    });
  }

  generateReport() {
    const totalTime = Date.now() - this.startTime;
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const successRate = Math.round((passedTests / totalTests) * 100);

    console.log('📊 Test Results Summary');
    console.log('=======================');
    console.log(`Total Tests: ${totalTests}`);
    console.log(`Passed: ${passedTests}`);
    console.log(`Failed: ${totalTests - passedTests}`);
    console.log(`Success Rate: ${successRate}%`);
    console.log(`Total Time: ${totalTime}ms\n`);

    // Group results by service
    const serviceResults = {};
    this.results.forEach(result => {
      if (!serviceResults[result.service]) {
        serviceResults[result.service] = [];
      }
      serviceResults[result.service].push(result);
    });

    // Print detailed results
    Object.entries(serviceResults).forEach(([service, tests]) => {
      console.log(`🔧 ${service}:`);
      tests.forEach(test => {
        const status = test.success ? '✅' : '❌';
        console.log(`  ${status} ${test.test}: ${test.message}`);
      });
      console.log('');
    });

    // Recommendations
    console.log('💡 Recommendations:');
    console.log('===================');
    
    if (successRate === 100) {
      console.log('🎉 Perfect! All AI services are working correctly.');
      console.log('   Your Gaming Hub is ready for production use.');
    } else if (successRate >= 75) {
      console.log('✅ Good! Most AI services are working.');
      console.log('   Consider fixing the failed services for optimal experience.');
    } else if (successRate >= 50) {
      console.log('⚠️  Some AI services need attention.');
      console.log('   The Gaming Hub will work but with limited AI features.');
    } else {
      console.log('❌ Major issues detected with AI services.');
      console.log('   Please review configuration and API keys.');
    }

    // Save detailed report
    const reportPath = path.join(__dirname, '../ai-test-report.json');
    fs.writeFileSync(reportPath, JSON.stringify({
      summary: {
        totalTests,
        passedTests,
        failedTests: totalTests - passedTests,
        successRate,
        totalTime,
        timestamp: new Date().toISOString(),
      },
      results: this.results,
    }, null, 2));

    console.log(`\n📄 Detailed report saved to: ${reportPath}`);
  }
}

// Run the tests
if (require.main === module) {
  const tester = new AIIntegrationTester();
  tester.runAllTests().catch(console.error);
}

module.exports = AIIntegrationTester;

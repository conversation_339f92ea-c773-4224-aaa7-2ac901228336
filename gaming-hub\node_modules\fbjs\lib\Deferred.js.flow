/**
 * Copyright (c) 2013-present, Facebook, Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 *
 * @providesModule Deferred
 * @typechecks
 * @flow
 */

/**
 * Deferred provides a Promise-like API that exposes methods to resolve and
 * reject the Promise. It is most useful when converting non-Promise code to use
 * Promises.
 *
 * If you want to export the Promise without exposing access to the resolve and
 * reject methods, you should export `getPromise` which returns a Promise with
 * the same semantics excluding those methods.
 */
class Deferred<Tvalue, Treason> {
  _settled: boolean;
  _promise: Promise<any>;
  _resolve: (value: Tvalue) => void;
  _reject: (reason: Treason) => void;

  constructor() {
    this._settled = false;
    this._promise = new Promise((resolve, reject) => {
      this._resolve = (resolve: any);
      this._reject = (reject: any);
    });
  }

  getPromise(): Promise<any> {
    return this._promise;
  }

  resolve(value: Tvalue): void {
    this._settled = true;

    this._resolve(value);
  }

  reject(reason: Treason): void {
    this._settled = true;

    this._reject(reason);
  }

  catch(onReject?: ?(error: any) => mixed): Promise<any> {
    return Promise.prototype.catch.apply(this._promise, arguments);
  }

  then(onFulfill?: ?(value: any) => mixed, onReject?: ?(error: any) => mixed): Promise<any> {
    return Promise.prototype.then.apply(this._promise, arguments);
  }

  done(onFulfill?: ?(value: any) => mixed, onReject?: ?(error: any) => mixed): void {
    // Embed the polyfill for the non-standard Promise.prototype.done so that
    // users of the open source fbjs don't need a custom lib for Promise
    const promise = arguments.length ? this._promise.then.apply(this._promise, arguments) : this._promise;
    promise.then(undefined, function (err) {
      setTimeout(function () {
        throw err;
      }, 0);
    });
  }

  isSettled(): boolean {
    return this._settled;
  }

}

module.exports = Deferred;
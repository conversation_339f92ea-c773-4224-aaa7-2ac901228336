import { aiService, GameContext } from './aiService';
import { supabase } from '../config/supabase';

export interface GameAnalysis {
  strengths: string[];
  weaknesses: string[];
  recommendations: string[];
  nextGoals: string[];
  difficultyAdjustment?: 'easier' | 'harder' | 'maintain';
}

export interface PlayerProfile {
  userId: string;
  preferredGenres: string[];
  skillLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  playStyle: 'casual' | 'competitive' | 'explorer' | 'achiever';
  strengths: string[];
  improvementAreas: string[];
  lastAnalysis: Date;
}

export interface GameTip {
  id: string;
  gameId: string;
  category: 'strategy' | 'mechanics' | 'optimization' | 'mindset';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  title: string;
  content: string;
  relevanceScore: number;
  aiGenerated: boolean;
}

class GameCoachService {
  private playerProfiles: Map<string, PlayerProfile> = new Map();

  async analyzeGameSession(
    userId: string,
    gameContext: GameContext,
    sessionData: {
      score: number;
      duration: number;
      mistakes?: number;
      achievements?: string[];
      previousBestScore?: number;
    }
  ): Promise<GameAnalysis> {
    try {
      // Get player profile
      const profile = await this.getPlayerProfile(userId);
      
      // Analyze performance
      const performanceAnalysis = this.analyzePerformance(sessionData, gameContext);
      
      // Generate AI-powered insights
      const aiInsights = await this.generateAIInsights(
        gameContext,
        sessionData,
        profile,
        performanceAnalysis
      );

      // Combine analysis
      const analysis: GameAnalysis = {
        strengths: [...performanceAnalysis.strengths, ...aiInsights.strengths],
        weaknesses: [...performanceAnalysis.weaknesses, ...aiInsights.weaknesses],
        recommendations: [...performanceAnalysis.recommendations, ...aiInsights.recommendations],
        nextGoals: aiInsights.nextGoals,
        difficultyAdjustment: this.suggestDifficultyAdjustment(sessionData, gameContext),
      };

      // Update player profile based on analysis
      await this.updatePlayerProfile(userId, analysis, gameContext);

      return analysis;
    } catch (error) {
      console.error('Error analyzing game session:', error);
      return this.getFallbackAnalysis(sessionData, gameContext);
    }
  }

  private analyzePerformance(
    sessionData: any,
    gameContext: GameContext
  ): Partial<GameAnalysis> {
    const analysis: Partial<GameAnalysis> = {
      strengths: [],
      weaknesses: [],
      recommendations: [],
    };

    // Score analysis
    if (sessionData.previousBestScore) {
      const improvement = sessionData.score - sessionData.previousBestScore;
      const improvementPercent = (improvement / sessionData.previousBestScore) * 100;

      if (improvementPercent > 10) {
        analysis.strengths!.push('Significant score improvement');
        analysis.recommendations!.push('Keep practicing this strategy - it\'s working well!');
      } else if (improvementPercent < -10) {
        analysis.weaknesses!.push('Score declined from previous best');
        analysis.recommendations!.push('Review your recent gameplay changes');
      }
    }

    // Duration analysis
    const avgDuration = this.getAverageSessionDuration(gameContext.gameId);
    if (sessionData.duration < avgDuration * 0.7) {
      analysis.strengths!.push('Efficient gameplay - completed quickly');
    } else if (sessionData.duration > avgDuration * 1.5) {
      analysis.recommendations!.push('Try to be more decisive in your moves');
    }

    // Achievement analysis
    if (sessionData.achievements && sessionData.achievements.length > 0) {
      analysis.strengths!.push(`Unlocked ${sessionData.achievements.length} achievement(s)`);
    }

    return analysis;
  }

  private async generateAIInsights(
    gameContext: GameContext,
    sessionData: any,
    profile: PlayerProfile,
    performanceAnalysis: Partial<GameAnalysis>
  ): Promise<Partial<GameAnalysis>> {
    try {
      const prompt = this.buildAnalysisPrompt(gameContext, sessionData, profile, performanceAnalysis);
      
      const response = await aiService.generateResponse([
        { id: 'analysis', role: 'user', content: prompt, timestamp: new Date() }
      ], gameContext);

      return this.parseAIAnalysis(response.content);
    } catch (error) {
      console.error('Error generating AI insights:', error);
      return {
        strengths: ['Completed the game session'],
        weaknesses: [],
        recommendations: ['Keep practicing to improve your skills'],
        nextGoals: ['Focus on consistency'],
      };
    }
  }

  private buildAnalysisPrompt(
    gameContext: GameContext,
    sessionData: any,
    profile: PlayerProfile,
    performanceAnalysis: Partial<GameAnalysis>
  ): string {
    return `Analyze this gaming session and provide coaching insights:

Game: ${gameContext.gameTitle}
Difficulty: ${gameContext.difficulty}
Player Level: ${profile.skillLevel}
Play Style: ${profile.playStyle}

Session Results:
- Score: ${sessionData.score}
- Duration: ${Math.floor(sessionData.duration / 60)} minutes
- Previous Best: ${sessionData.previousBestScore || 'N/A'}
- Achievements: ${sessionData.achievements?.join(', ') || 'None'}

Current Analysis:
- Strengths: ${performanceAnalysis.strengths?.join(', ') || 'None identified'}
- Areas for improvement: ${performanceAnalysis.weaknesses?.join(', ') || 'None identified'}

Please provide:
1. Additional strengths (2-3 items)
2. Areas for improvement (2-3 items)  
3. Specific recommendations (3-4 actionable tips)
4. Next goals (2-3 achievable objectives)

Format your response as:
STRENGTHS: [list]
WEAKNESSES: [list]
RECOMMENDATIONS: [list]
GOALS: [list]`;
  }

  private parseAIAnalysis(content: string): Partial<GameAnalysis> {
    const sections = {
      strengths: [] as string[],
      weaknesses: [] as string[],
      recommendations: [] as string[],
      nextGoals: [] as string[],
    };

    const lines = content.split('\n');
    let currentSection = '';

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.startsWith('STRENGTHS:')) {
        currentSection = 'strengths';
        continue;
      } else if (trimmed.startsWith('WEAKNESSES:')) {
        currentSection = 'weaknesses';
        continue;
      } else if (trimmed.startsWith('RECOMMENDATIONS:')) {
        currentSection = 'recommendations';
        continue;
      } else if (trimmed.startsWith('GOALS:')) {
        currentSection = 'nextGoals';
        continue;
      }

      if (currentSection && trimmed.length > 0 && !trimmed.startsWith('-')) {
        const cleanLine = trimmed.replace(/^[-•*]\s*/, '').trim();
        if (cleanLine.length > 0) {
          (sections as any)[currentSection].push(cleanLine);
        }
      }
    }

    return sections;
  }

  private suggestDifficultyAdjustment(
    sessionData: any,
    gameContext: GameContext
  ): 'easier' | 'harder' | 'maintain' {
    if (!sessionData.previousBestScore) return 'maintain';

    const improvement = sessionData.score - sessionData.previousBestScore;
    const improvementPercent = (improvement / sessionData.previousBestScore) * 100;

    if (improvementPercent > 25 && gameContext.difficulty !== 'hard') {
      return 'harder';
    } else if (improvementPercent < -25 && gameContext.difficulty !== 'easy') {
      return 'easier';
    }

    return 'maintain';
  }

  async getPlayerProfile(userId: string): Promise<PlayerProfile> {
    try {
      // Check cache first
      if (this.playerProfiles.has(userId)) {
        return this.playerProfiles.get(userId)!;
      }

      // Load from database
      const { data: profile } = await supabase
        .from('player_profiles')
        .select('*')
        .eq('user_id', userId)
        .single();

      if (profile) {
        const playerProfile: PlayerProfile = {
          userId: profile.user_id,
          preferredGenres: profile.preferred_genres || [],
          skillLevel: profile.skill_level || 'beginner',
          playStyle: profile.play_style || 'casual',
          strengths: profile.strengths || [],
          improvementAreas: profile.improvement_areas || [],
          lastAnalysis: new Date(profile.last_analysis || Date.now()),
        };

        this.playerProfiles.set(userId, playerProfile);
        return playerProfile;
      }

      // Create default profile
      return this.createDefaultProfile(userId);
    } catch (error) {
      console.error('Error loading player profile:', error);
      return this.createDefaultProfile(userId);
    }
  }

  private createDefaultProfile(userId: string): PlayerProfile {
    return {
      userId,
      preferredGenres: [],
      skillLevel: 'beginner',
      playStyle: 'casual',
      strengths: [],
      improvementAreas: [],
      lastAnalysis: new Date(),
    };
  }

  private async updatePlayerProfile(
    userId: string,
    analysis: GameAnalysis,
    gameContext: GameContext
  ): Promise<void> {
    try {
      const profile = await this.getPlayerProfile(userId);
      
      // Update strengths and improvement areas
      const updatedStrengths = [...new Set([...profile.strengths, ...analysis.strengths])];
      const updatedImprovementAreas = [...new Set([...profile.improvementAreas, ...analysis.weaknesses])];

      // Update preferred genres
      const updatedGenres = [...new Set([...profile.preferredGenres, gameContext.gameTitle])];

      const updatedProfile: PlayerProfile = {
        ...profile,
        strengths: updatedStrengths.slice(-10), // Keep last 10
        improvementAreas: updatedImprovementAreas.slice(-10), // Keep last 10
        preferredGenres: updatedGenres.slice(-5), // Keep last 5
        lastAnalysis: new Date(),
      };

      // Save to database
      await supabase
        .from('player_profiles')
        .upsert({
          user_id: userId,
          preferred_genres: updatedProfile.preferredGenres,
          skill_level: updatedProfile.skillLevel,
          play_style: updatedProfile.playStyle,
          strengths: updatedProfile.strengths,
          improvement_areas: updatedProfile.improvementAreas,
          last_analysis: updatedProfile.lastAnalysis.toISOString(),
        });

      // Update cache
      this.playerProfiles.set(userId, updatedProfile);
    } catch (error) {
      console.error('Error updating player profile:', error);
    }
  }

  private getAverageSessionDuration(gameId: string): number {
    // This would typically come from analytics data
    // For now, return a default based on game type
    return 300; // 5 minutes default
  }

  private getFallbackAnalysis(sessionData: any, gameContext: GameContext): GameAnalysis {
    return {
      strengths: ['Completed the game session', 'Showed persistence'],
      weaknesses: ['Could benefit from more practice'],
      recommendations: [
        'Focus on understanding the game mechanics',
        'Practice regularly to build consistency',
        'Try different strategies to find what works best',
      ],
      nextGoals: [
        'Improve your personal best score',
        'Complete the game more efficiently',
        'Unlock new achievements',
      ],
      difficultyAdjustment: 'maintain',
    };
  }

  async generatePersonalizedTips(
    userId: string,
    gameId: string,
    count: number = 5
  ): Promise<GameTip[]> {
    try {
      const profile = await this.getPlayerProfile(userId);
      
      // Generate AI-powered tips based on player profile
      const tips = await aiService.generateGameTips(gameId, profile.skillLevel);
      
      return tips.map((tip, index) => ({
        id: `tip_${Date.now()}_${index}`,
        gameId,
        category: this.categorizeTip(tip),
        difficulty: profile.skillLevel,
        title: this.extractTipTitle(tip),
        content: tip,
        relevanceScore: this.calculateRelevanceScore(tip, profile),
        aiGenerated: true,
      }));
    } catch (error) {
      console.error('Error generating personalized tips:', error);
      return this.getFallbackTips(gameId, count);
    }
  }

  private categorizeTip(tip: string): GameTip['category'] {
    const lowerTip = tip.toLowerCase();
    
    if (lowerTip.includes('strategy') || lowerTip.includes('plan')) return 'strategy';
    if (lowerTip.includes('control') || lowerTip.includes('mechanic')) return 'mechanics';
    if (lowerTip.includes('optimize') || lowerTip.includes('efficient')) return 'optimization';
    if (lowerTip.includes('focus') || lowerTip.includes('mindset')) return 'mindset';
    
    return 'strategy';
  }

  private extractTipTitle(tip: string): string {
    const sentences = tip.split('.');
    return sentences[0].substring(0, 50) + (sentences[0].length > 50 ? '...' : '');
  }

  private calculateRelevanceScore(tip: string, profile: PlayerProfile): number {
    let score = 0.5; // Base score
    
    // Boost score based on profile match
    profile.improvementAreas.forEach(area => {
      if (tip.toLowerCase().includes(area.toLowerCase())) {
        score += 0.2;
      }
    });

    return Math.min(score, 1.0);
  }

  private getFallbackTips(gameId: string, count: number): GameTip[] {
    const fallbackTips = [
      'Practice regularly to build muscle memory and improve your reaction time',
      'Focus on understanding the core game mechanics before attempting advanced strategies',
      'Take breaks to avoid fatigue and maintain peak performance',
      'Learn from your mistakes by reviewing what went wrong in each session',
      'Set small, achievable goals to maintain motivation and track progress',
    ];

    return fallbackTips.slice(0, count).map((tip, index) => ({
      id: `fallback_tip_${index}`,
      gameId,
      category: 'strategy' as const,
      difficulty: 'beginner' as const,
      title: this.extractTipTitle(tip),
      content: tip,
      relevanceScore: 0.5,
      aiGenerated: false,
    }));
  }
}

export const gameCoachService = new GameCoachService();
export default gameCoachService;

# Findz Gaming Streaming Platform - Docker Configuration
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for streaming and gaming
RUN apk add --no-cache \
    python3 \
    make \
    g++ \
    cairo-dev \
    jpeg-dev \
    pango-dev \
    musl-dev \
    giflib-dev \
    pixman-dev \
    pangomm-dev \
    libjpeg-turbo-dev \
    freetype-dev

# Copy package files
COPY package*.json ./

# Install all dependencies (including dev dependencies for build)
RUN npm ci

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Keep all dependencies for now (some are needed at runtime)
RUN npm cache clean --force

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs
RUN adduser -S findz -u 1001

# Create directories for uploads and streams
RUN mkdir -p /app/uploads /app/streams /app/recordings
RUN chown -R findz:nodejs /app

# Switch to non-root user
USER findz

# Expose port
EXPOSE 5001

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:5001/api/health || exit 1

# Start the application
ENV NODE_ENV=production
CMD ["node", "dist/index.js"]

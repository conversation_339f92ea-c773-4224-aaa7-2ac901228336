import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  FlatList,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import Markdown from 'react-native-markdown-display';

import { ChatMessage, AIResponse, GameContext } from '../../services/aiService';
import { voiceService } from '../../services/voiceService';
import { Environment } from '../../config/environment';
import { theme, colors } from '../../theme';

interface ChatInterfaceProps {
  gameContext?: GameContext;
  onClose?: () => void;
  initialMessage?: string;
}

export default function ChatInterface({ gameContext, onClose, initialMessage }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isVoiceEnabled, setIsVoiceEnabled] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // Check voice availability
    checkVoiceAvailability();

    // Add welcome message
    const welcomeMessage: ChatMessage = {
      id: 'welcome',
      role: 'assistant',
      content: gameContext
        ? `Hi! I'm your AI gaming assistant. I see you're playing ${gameContext.gameTitle}. How can I help you improve your game?`
        : "Hi! I'm your AI gaming assistant. I'm here to help you with tips, strategies, and any gaming questions you have!",
      timestamp: new Date(),
    };

    setMessages([welcomeMessage]);

    // Set initial suggestions
    setSuggestions([
      'Give me tips for this game',
      'How can I improve my score?',
      'What strategy should I use?',
      'Help me with difficult parts',
    ]);

    // Add initial message if provided
    if (initialMessage) {
      setTimeout(() => {
        handleSendMessage(initialMessage);
      }, 500);
    }
  }, [gameContext, initialMessage]);

  const checkVoiceAvailability = async () => {
    try {
      const voiceEnabled = await voiceService.isVoiceEnabled();
      setIsVoiceEnabled(voiceEnabled);
    } catch (error) {
      console.error('Error checking voice availability:', error);
      setIsVoiceEnabled(false);
    }
  };

  const handleSendMessage = async (text?: string) => {
    const messageText = text || inputText.trim();
    if (!messageText || isLoading) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      role: 'user',
      content: messageText,
      timestamp: new Date(),
      metadata: gameContext ? {
        gameContext: gameContext.gameId,
        userScore: gameContext.currentScore,
        difficulty: gameContext.difficulty,
      } : undefined,
    };

    setMessages(prev => [...prev, userMessage]);
    setInputText('');
    setIsLoading(true);

    try {
      // Import AI service dynamically to avoid circular dependencies
      const { aiService } = await import('../../services/aiService');
      
      const response: AIResponse = await aiService.generateResponse(
        [...messages, userMessage],
        gameContext
      );

      const assistantMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        metadata: {
          suggestions: response.suggestions,
        },
      };

      setMessages(prev => [...prev, assistantMessage]);

      if (response.suggestions) {
        setSuggestions(response.suggestions);
      }

      // Speak the response if voice is enabled
      if (isVoiceEnabled && response.content) {
        handleSpeakMessage(response.content);
      }
    } catch (error) {
      console.error('Error getting AI response:', error);
      
      const errorMessage: ChatMessage = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: "I'm sorry, I'm having trouble connecting right now. Please try again in a moment, or ask me something else!",
        timestamp: new Date(),
      };
      
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSuggestionPress = (suggestion: string) => {
    setInputText(suggestion);
  };

  const handleSpeakMessage = async (text: string) => {
    if (!isVoiceEnabled || isSpeaking) return;

    try {
      setIsSpeaking(true);
      const result = await voiceService.speakAIResponse(text, 'gaming');

      if (!result.success) {
        console.warn('Voice synthesis failed:', result.error);
      }
    } catch (error) {
      console.error('Error speaking message:', error);
    } finally {
      setIsSpeaking(false);
    }
  };

  const handleStopSpeaking = async () => {
    try {
      await voiceService.stopAudio();
      setIsSpeaking(false);
    } catch (error) {
      console.error('Error stopping speech:', error);
    }
  };

  const renderMessage = ({ item }: { item: ChatMessage }) => (
    <View style={[
      styles.messageContainer,
      item.role === 'user' ? styles.userMessage : styles.assistantMessage
    ]}>
      {item.role === 'assistant' && (
        <View style={styles.assistantAvatar}>
          <Ionicons name="sparkles" size={16} color={colors.primary} />
        </View>
      )}
      
      <View style={[
        styles.messageBubble,
        item.role === 'user' ? styles.userBubble : styles.assistantBubble
      ]}>
        {item.role === 'assistant' ? (
          <Markdown style={markdownStyles}>
            {item.content}
          </Markdown>
        ) : (
          <Text style={[
            styles.messageText,
            item.role === 'user' ? styles.userText : styles.assistantText
          ]}>
            {item.content}
          </Text>
        )}
        
        <View style={styles.messageFooter}>
          <Text style={styles.timestamp}>
            {item.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
          </Text>

          {item.role === 'assistant' && isVoiceEnabled && (
            <TouchableOpacity
              style={styles.speakButton}
              onPress={() => handleSpeakMessage(item.content)}
              disabled={isSpeaking}
            >
              <Ionicons
                name={isSpeaking ? "stop" : "volume-high"}
                size={14}
                color={isSpeaking ? colors.error : colors.primary}
              />
            </TouchableOpacity>
          )}
        </View>
      </View>
      
      {item.role === 'user' && (
        <View style={styles.userAvatar}>
          <Ionicons name="person" size={16} color={colors.text} />
        </View>
      )}
    </View>
  );

  const renderSuggestion = ({ item }: { item: string }) => (
    <TouchableOpacity
      style={styles.suggestionButton}
      onPress={() => handleSuggestionPress(item)}
    >
      <Text style={styles.suggestionText}>{item}</Text>
    </TouchableOpacity>
  );

  return (
    <KeyboardAvoidingView 
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* Header */}
      <LinearGradient
        colors={colors.gradientPrimary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerLeft}>
            <Ionicons name="sparkles" size={24} color={colors.text} />
            <Text style={styles.headerTitle}>AI Assistant</Text>
          </View>

          <View style={styles.headerRight}>
            {isVoiceEnabled && (
              <TouchableOpacity
                onPress={isSpeaking ? handleStopSpeaking : undefined}
                style={[styles.voiceButton, isSpeaking && styles.voiceButtonActive]}
              >
                <Ionicons
                  name={isSpeaking ? "stop" : "volume-high"}
                  size={20}
                  color={colors.text}
                />
              </TouchableOpacity>
            )}

            {onClose && (
              <TouchableOpacity onPress={onClose} style={styles.closeButton}>
                <Ionicons name="close" size={24} color={colors.text} />
              </TouchableOpacity>
            )}
          </View>
        </View>
        
        {gameContext && (
          <View style={styles.gameContext}>
            <Text style={styles.gameContextText}>
              Playing: {gameContext.gameTitle} • Score: {gameContext.currentScore}
            </Text>
          </View>
        )}
      </LinearGradient>

      {/* Messages */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        style={styles.messagesList}
        contentContainerStyle={styles.messagesContent}
        onContentSizeChange={() => flatListRef.current?.scrollToEnd()}
        showsVerticalScrollIndicator={false}
      />

      {/* Loading indicator */}
      {isLoading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={colors.primary} />
          <Text style={styles.loadingText}>AI is thinking...</Text>
        </View>
      )}

      {/* Suggestions */}
      {suggestions.length > 0 && (
        <View style={styles.suggestionsContainer}>
          <FlatList
            horizontal
            data={suggestions}
            renderItem={renderSuggestion}
            keyExtractor={(item, index) => index.toString()}
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.suggestionsList}
          />
        </View>
      )}

      {/* Input */}
      <View style={styles.inputContainer}>
        <TextInput
          style={styles.textInput}
          value={inputText}
          onChangeText={setInputText}
          placeholder="Ask me anything about gaming..."
          placeholderTextColor={colors.textSecondary}
          multiline
          maxLength={500}
          editable={!isLoading}
        />
        <TouchableOpacity
          style={[styles.sendButton, (!inputText.trim() || isLoading) && styles.sendButtonDisabled]}
          onPress={() => handleSendMessage()}
          disabled={!inputText.trim() || isLoading}
        >
          <Ionicons 
            name="send" 
            size={20} 
            color={(!inputText.trim() || isLoading) ? colors.textSecondary : colors.text} 
          />
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.md,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    marginLeft: theme.spacing.sm,
  },
  voiceButton: {
    padding: theme.spacing.xs,
    marginRight: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
    backgroundColor: colors.cardOverlay,
  },
  voiceButtonActive: {
    backgroundColor: colors.error,
  },
  closeButton: {
    padding: theme.spacing.xs,
  },
  gameContext: {
    marginTop: theme.spacing.sm,
    backgroundColor: colors.cardOverlay,
    padding: theme.spacing.sm,
    borderRadius: theme.borderRadius.md,
  },
  gameContextText: {
    ...theme.typography.textStyles.caption,
    color: colors.text,
  },
  messagesList: {
    flex: 1,
  },
  messagesContent: {
    padding: theme.spacing.lg,
  },
  messageContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.md,
    alignItems: 'flex-end',
  },
  userMessage: {
    justifyContent: 'flex-end',
  },
  assistantMessage: {
    justifyContent: 'flex-start',
  },
  assistantAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.backgroundCard,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: theme.spacing.sm,
  },
  userAvatar: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: theme.spacing.sm,
  },
  messageBubble: {
    maxWidth: '75%',
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  userBubble: {
    backgroundColor: colors.primary,
    borderBottomRightRadius: theme.borderRadius.sm,
  },
  assistantBubble: {
    backgroundColor: colors.backgroundCard,
    borderBottomLeftRadius: theme.borderRadius.sm,
  },
  messageText: {
    ...theme.typography.textStyles.body,
    lineHeight: 20,
  },
  userText: {
    color: colors.text,
  },
  assistantText: {
    color: colors.text,
  },
  messageFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: theme.spacing.xs,
  },
  timestamp: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    fontSize: 10,
  },
  speakButton: {
    padding: 4,
    borderRadius: theme.borderRadius.sm,
    backgroundColor: colors.cardOverlay,
    marginLeft: theme.spacing.sm,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: theme.spacing.md,
  },
  loadingText: {
    ...theme.typography.textStyles.caption,
    color: colors.textSecondary,
    marginLeft: theme.spacing.sm,
  },
  suggestionsContainer: {
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.sm,
  },
  suggestionsList: {
    paddingRight: theme.spacing.lg,
  },
  suggestionButton: {
    backgroundColor: colors.backgroundCard,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    marginRight: theme.spacing.sm,
    borderWidth: 1,
    borderColor: colors.primary,
  },
  suggestionText: {
    ...theme.typography.textStyles.caption,
    color: colors.primary,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: theme.spacing.lg,
    alignItems: 'flex-end',
    backgroundColor: colors.backgroundCard,
  },
  textInput: {
    flex: 1,
    backgroundColor: colors.background,
    borderRadius: theme.borderRadius.lg,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    color: colors.text,
    fontSize: theme.typography.fontSize.base,
    maxHeight: 100,
    marginRight: theme.spacing.sm,
  },
  sendButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  sendButtonDisabled: {
    backgroundColor: colors.backgroundCard,
  },
});

const markdownStyles = {
  body: {
    color: colors.text,
    fontSize: theme.typography.fontSize.base,
  },
  paragraph: {
    marginBottom: theme.spacing.sm,
  },
  strong: {
    fontWeight: 'bold',
    color: colors.primary,
  },
  em: {
    fontStyle: 'italic',
    color: colors.textSecondary,
  },
  code_inline: {
    backgroundColor: colors.background,
    color: colors.secondary,
    paddingHorizontal: 4,
    borderRadius: 4,
  },
  bullet_list: {
    marginBottom: theme.spacing.sm,
  },
  ordered_list: {
    marginBottom: theme.spacing.sm,
  },
  list_item: {
    marginBottom: 2,
  },
};

export interface User {
  id: string;
  email: string;
  username: string;
  avatar_url?: string;
  created_at: string;
  total_score: number;
  games_played: number;
}

export interface Game {
  id: string;
  title: string;
  description: string;
  thumbnail_url: string;
  game_url: string;
  category: string;
  difficulty: 'easy' | 'medium' | 'hard';
  is_active: boolean;
  created_at: string;
  play_count: number;
  average_score: number;
}

export interface GameSession {
  id: string;
  user_id: string;
  game_id: string;
  score: number;
  duration: number;
  completed_at: string;
  achievements?: string[];
}

export interface Leaderboard {
  id: string;
  game_id: string;
  user_id: string;
  username: string;
  avatar_url?: string;
  score: number;
  rank: number;
  achieved_at: string;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  points: number;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
}

export interface UserAchievement {
  id: string;
  user_id: string;
  achievement_id: string;
  unlocked_at: string;
}

export type RootStackParamList = {
  Main: undefined;
  Game: { gameId: string; gameUrl: string; gameTitle: string };
  GameCoach: { gameContext?: any; sessionData?: any };
  OllamaSetup: undefined;
  Profile: undefined;
  Leaderboard: { gameId?: string };
  Auth: undefined;
};

export type MainTabParamList = {
  Home: undefined;
  Games: undefined;
  'AI Assistant': undefined;
  Leaderboard: undefined;
  Profile: undefined;
};

# 🔐 Authentication Troubleshooting Guide

## 🎯 Test Credentials

Use these credentials to sign in to the Findz Gaming Hub:

```
Email: <EMAIL>
Password: gaming123
Username: FindzGamer
```

## 📱 How to Sign In

1. **Open the gaming hub app** in your Android emulator
2. **Make sure you're in "Sign In" mode** (not "Sign Up")
   - If you see "Create your account", tap "Already have an account? Sign In"
3. **Enter the test credentials:**
   - Email: `<EMAIL>`
   - Password: `gaming123`
4. **Tap "Sign In"**

## 🆕 Creating New Users

### Option 1: Use the App
1. **Switch to Sign Up mode** by tapping "Don't have an account? Sign Up"
2. **Fill in the form:**
   - Email: Use a valid email format
   - Password: At least 6 characters
   - Username: Choose a unique username
3. **Tap "Sign Up"**

### Option 2: Use the Script
Run the user creation script with custom credentials:

```bash
cd gaming-hub
node scripts/create-test-user.js
```

## 🔧 Common Issues & Solutions

### "Invalid login credentials"
- ✅ **Solution**: Use the test credentials provided above
- ✅ **Check**: Make sure you're in "Sign In" mode, not "Sign Up"
- ✅ **Verify**: Email and password are entered correctly

### "User already registered"
- ✅ **Solution**: Switch to "Sign In" mode and use existing credentials
- ✅ **Alternative**: Use a different email address for sign up

### "Network Error"
- ✅ **Check**: Internet connection is working
- ✅ **Verify**: Supabase service is accessible
- ✅ **Test**: Try refreshing the app

### App Stuck on Loading
- ✅ **Solution**: Restart the development server
- ✅ **Check**: Metro bundler is running properly
- ✅ **Clear**: React Native cache if needed

## 🛠️ Development Commands

### Restart Development Server
```bash
cd gaming-hub
npm start -- --reset-cache
```

### Create Additional Test Users
```bash
cd gaming-hub
node scripts/create-test-user.js
```

### Check Supabase Connection
```bash
cd gaming-hub
node -e "
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(
  'https://opgyuyeuczddftaqkvdt.supabase.co/',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9wZ3l1eWV1Y3pkZGZ0YXFrdmR0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDI2NjA0MzQsImV4cCI6MjA1ODIzNjQzNH0.s7cPyvnAe32_YkB3a2bFifid3eIgkeLTllmvgZ96wQ8'
);
supabase.auth.getSession().then(console.log);
"
```

## 📞 Support

If you continue to experience issues:

1. **Check the console logs** in the development server
2. **Verify Supabase configuration** in `src/config/supabase.ts`
3. **Test with different credentials** or create a new user
4. **Restart the development environment** completely

## 🎮 Next Steps

Once signed in successfully, you'll have access to:
- 🏠 **Gaming Hub Dashboard**
- 🎯 **Games Library**
- 🤖 **AI Assistant**
- 📺 **Live Streaming**
- 🏆 **Leaderboards**
- 👤 **User Profile**

Happy gaming! 🎮✨

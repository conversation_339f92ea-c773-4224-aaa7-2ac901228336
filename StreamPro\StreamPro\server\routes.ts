import type { Express } from "express";
import { createServer, type Server } from "http";
import { WebSocketServer, WebSocket } from "ws";
import { storage } from "./storage";
import { insertStreamSchema, insertChatMessageSchema, insertStreamAnalyticsSchema } from "@shared/schema";
import { generateStreamKey } from "../lib/utils";

export async function registerRoutes(app: Express): Promise<Server> {
  const httpServer = createServer(app);
  
  // WebSocket server for real-time communication
  const wss = new WebSocketServer({ server: httpServer, path: '/ws' });
  
  // Store active connections
  const connections = new Map<string, WebSocket>();
  const streamConnections = new Map<number, Set<string>>();
  
  wss.on('connection', (ws, req) => {
    const connectionId = Math.random().toString(36).substring(7);
    connections.set(connectionId, ws);
    
    ws.on('message', async (message) => {
      try {
        const data = JSON.parse(message.toString());
        
        switch (data.type) {
          case 'join_stream':
            const streamId = data.streamId;
            if (!streamConnections.has(streamId)) {
              streamConnections.set(streamId, new Set());
            }
            streamConnections.get(streamId)?.add(connectionId);
            
            // Add viewer to database
            await storage.addViewer(streamId, data.userId, connectionId);
            
            // Broadcast viewer count update
            broadcastToStream(streamId, {
              type: 'viewer_count_update',
              count: streamConnections.get(streamId)?.size || 0
            });
            break;
            
          case 'leave_stream':
            const leaveStreamId = data.streamId;
            streamConnections.get(leaveStreamId)?.delete(connectionId);
            
            // Remove viewer from database
            await storage.removeViewer(leaveStreamId, connectionId);
            
            // Broadcast viewer count update
            broadcastToStream(leaveStreamId, {
              type: 'viewer_count_update',
              count: streamConnections.get(leaveStreamId)?.size || 0
            });
            break;
            
          case 'chat_message':
            const chatMessage = await storage.addChatMessage({
              streamId: data.streamId,
              userId: data.userId,
              message: data.message,
              type: data.messageType || 'message'
            });
            
            // Broadcast chat message to all viewers
            broadcastToStream(data.streamId, {
              type: 'chat_message',
              message: chatMessage
            });
            break;
            
          case 'webrtc_offer':
          case 'webrtc_answer':
          case 'webrtc_ice_candidate':
            // Forward WebRTC signaling messages
            const targetConnectionId = data.targetConnectionId;
            const targetWs = connections.get(targetConnectionId);
            if (targetWs && targetWs.readyState === WebSocket.OPEN) {
              targetWs.send(JSON.stringify(data));
            }
            break;
        }
      } catch (error) {
        console.error('WebSocket message error:', error);
      }
    });
    
    ws.on('close', () => {
      connections.delete(connectionId);
      // Remove from all stream connections
      streamConnections.forEach((connectionSet, streamId) => {
        if (connectionSet.has(connectionId)) {
          connectionSet.delete(connectionId);
          // Update viewer count
          broadcastToStream(streamId, {
            type: 'viewer_count_update',
            count: connectionSet.size
          });
        }
      });
    });
  });
  
  function broadcastToStream(streamId: number, message: any) {
    const streamConnectionSet = streamConnections.get(streamId);
    if (streamConnectionSet) {
      streamConnectionSet.forEach(connectionId => {
        const ws = connections.get(connectionId);
        if (ws && ws.readyState === WebSocket.OPEN) {
          ws.send(JSON.stringify(message));
        }
      });
    }
  }
  
  // API Routes
  
  // Get all streams
  app.get('/api/streams', async (req, res) => {
    try {
      const streams = await storage.getStreams();
      res.json(streams);
    } catch (error) {
      console.error('Error fetching streams:', error);
      res.status(500).json({ message: 'Failed to fetch streams' });
    }
  });
  
  // Get single stream
  app.get('/api/streams/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const stream = await storage.getStream(id);
      if (!stream) {
        return res.status(404).json({ message: 'Stream not found' });
      }
      res.json(stream);
    } catch (error) {
      console.error('Error fetching stream:', error);
      res.status(500).json({ message: 'Failed to fetch stream' });
    }
  });
  
  // Create stream
  app.post('/api/streams', async (req, res) => {
    try {
      const streamData = insertStreamSchema.parse(req.body);
      const streamKey = generateStreamKey();
      
      const stream = await storage.createStream({
        ...streamData,
        streamKey
      });
      
      res.status(201).json(stream);
    } catch (error) {
      console.error('Error creating stream:', error);
      res.status(500).json({ message: 'Failed to create stream' });
    }
  });
  
  // Update stream
  app.put('/api/streams/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const updates = req.body;
      
      const stream = await storage.updateStream(id, updates);
      if (!stream) {
        return res.status(404).json({ message: 'Stream not found' });
      }
      
      res.json(stream);
    } catch (error) {
      console.error('Error updating stream:', error);
      res.status(500).json({ message: 'Failed to update stream' });
    }
  });
  
  // Delete stream
  app.delete('/api/streams/:id', async (req, res) => {
    try {
      const id = parseInt(req.params.id);
      const success = await storage.deleteStream(id);
      
      if (!success) {
        return res.status(404).json({ message: 'Stream not found' });
      }
      
      res.status(204).send();
    } catch (error) {
      console.error('Error deleting stream:', error);
      res.status(500).json({ message: 'Failed to delete stream' });
    }
  });
  
  // Get chat messages
  app.get('/api/streams/:id/chat', async (req, res) => {
    try {
      const streamId = parseInt(req.params.id);
      const limit = parseInt(req.query.limit as string) || 50;
      
      const messages = await storage.getChatMessages(streamId, limit);
      res.json(messages);
    } catch (error) {
      console.error('Error fetching chat messages:', error);
      res.status(500).json({ message: 'Failed to fetch chat messages' });
    }
  });
  
  // Get stream analytics
  app.get('/api/streams/:id/analytics', async (req, res) => {
    try {
      const streamId = parseInt(req.params.id);
      const hours = parseInt(req.query.hours as string) || 24;
      
      const analytics = await storage.getStreamAnalytics(streamId, hours);
      res.json(analytics);
    } catch (error) {
      console.error('Error fetching analytics:', error);
      res.status(500).json({ message: 'Failed to fetch analytics' });
    }
  });
  
  // Add analytics data
  app.post('/api/streams/:id/analytics', async (req, res) => {
    try {
      const streamId = parseInt(req.params.id);
      const analyticsData = insertStreamAnalyticsSchema.parse({
        ...req.body,
        streamId
      });
      
      const analytics = await storage.addAnalytics(analyticsData);
      res.status(201).json(analytics);
    } catch (error) {
      console.error('Error adding analytics:', error);
      res.status(500).json({ message: 'Failed to add analytics' });
    }
  });
  
  // Get system stats
  app.get('/api/stats', async (req, res) => {
    try {
      const streams = await storage.getStreams();
      const activeStreams = streams.filter(s => s.status === 'live');
      const totalViewers = activeStreams.reduce((sum, s) => sum + (s.viewerCount || 0), 0);
      
      res.json({
        activeStreams: activeStreams.length,
        totalViewers,
        totalStreams: streams.length,
        avgLatency: '2.3s',
        serverLoad: '67%'
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
      res.status(500).json({ message: 'Failed to fetch stats' });
    }
  });
  
  return httpServer;
}

function generateStreamKey(): string {
  return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
}

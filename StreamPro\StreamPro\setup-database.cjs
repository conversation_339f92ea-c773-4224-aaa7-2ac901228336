#!/usr/bin/env node

/**
 * Findz Gaming Streaming Platform - Database Setup
 * Sets up database for the gaming streaming platform
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

class FindzDatabaseSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async run() {
    console.log('🎮 Findz Gaming Streaming Platform - Database Setup');
    console.log('===================================================\n');

    try {
      // For Windows development, we'll use SQLite for simplicity
      console.log('🔧 Setting up SQLite database for development...');
      await this.setupSQLiteDatabase();

      // Create environment file
      await this.createEnvironmentFile();

      console.log('\n🎉 Database Setup Complete!');
      console.log('============================');
      console.log('Your Findz Gaming Streaming Platform database is ready!');
      console.log('\n💡 Next steps:');
      console.log('   1. Build the platform: npm run build');
      console.log('   2. Start the platform: npm run dev:win');
      console.log('   3. Open http://localhost:5000');
      console.log('   4. Start streaming your gaming content!');

    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      console.log('\n🔄 Fallback: Creating demo configuration...');
      await this.createDemoConfig();
    } finally {
      this.rl.close();
    }
  }

  async setupSQLiteDatabase() {
    console.log('📦 Installing SQLite database driver...');
    
    try {
      // Install better-sqlite3 for local development
      await this.executeCommand('npm', ['install', 'better-sqlite3', '--save']);
      console.log('✅ SQLite driver installed!');
      
      // Create SQLite database file
      const dbPath = path.join(__dirname, 'findz_gaming_streaming.db');
      if (!fs.existsSync(dbPath)) {
        fs.writeFileSync(dbPath, '');
        console.log('✅ SQLite database file created!');
      }
      
      this.databaseUrl = `file:${dbPath}`;
      
    } catch (error) {
      console.log('⚠️ Could not install SQLite. Using demo configuration.');
      await this.createDemoConfig();
    }
  }

  async createDemoConfig() {
    // Create demo configuration with mock database
    this.databaseUrl = 'postgresql://demo:demo@localhost:5432/findz_gaming_streaming_demo';
    console.log('✅ Demo database configuration created!');
  }

  async createEnvironmentFile() {
    console.log('\n📝 Creating environment configuration...');
    
    const envContent = `# Findz Gaming Streaming Platform Environment Variables
# Generated on ${new Date().toISOString()}

# Database Configuration
DATABASE_URL="${this.databaseUrl}"

# Server Configuration
NODE_ENV=development
PORT=5000

# Gaming Streaming Configuration
STREAM_QUALITY_DEFAULT=720p
STREAM_BITRATE_DEFAULT=2500
MAX_VIEWERS_PER_STREAM=1000

# WebRTC Configuration
STUN_SERVER_URL="stun:stun.l.google.com:19302"

# Gaming Features
ENABLE_GAME_CATEGORIES=true
ENABLE_AI_MODERATION=true
ENABLE_STREAM_RECORDING=true

# Security
JWT_SECRET="${this.generateSecret()}"
CORS_ORIGIN="http://localhost:8082,http://localhost:3000"

# Gaming Hub Integration
GAMING_HUB_URL="http://localhost:8082"
GAMING_HUB_API_KEY="findz-gaming-hub-integration"

# AI Integration (Optional - will use Gaming Hub AI services)
# OPENAI_API_KEY=""
# ANTHROPIC_API_KEY=""
`;

    fs.writeFileSync(path.join(__dirname, '.env'), envContent);
    console.log('✅ Environment file created!');
  }

  generateSecret() {
    return require('crypto').randomBytes(32).toString('hex');
  }

  async executeCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { 
        stdio: 'inherit',
        ...options
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Command failed with code ${code}`));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }
}

// Run the setup
if (require.main === module) {
  const setup = new FindzDatabaseSetup();
  setup.run().catch(console.error);
}

module.exports = FindzDatabaseSetup;

import { useState, useEffect, useRef, useCallback } from 'react';
import { useWebSocket } from './useWebSocket';

interface UseWebRTCOptions {
  streamId: number;
  isPublisher?: boolean;
}

export function useWebRTC(streamId: number, options: Partial<UseWebRTCOptions> = {}) {
  const [localStream, setLocalStream] = useState<MediaStream | null>(null);
  const [remoteStream, setRemoteStream] = useState<MediaStream | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [isPublishing, setIsPublishing] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const peerConnectionRef = useRef<RTCPeerConnection | null>(null);
  const localVideoRef = useRef<HTMLVideoElement | null>(null);
  const remoteVideoRef = useRef<HTMLVideoElement | null>(null);
  const connectionId = useRef<string | null>(null);
  
  const { socket, isConnected: wsConnected, sendMessage } = useWebSocket('/ws', {
    onMessage: handleWebSocketMessage,
  });
  
  const rtcConfig = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
    ],
  };
  
  const createPeerConnection = useCallback(() => {
    if (peerConnectionRef.current) {
      return peerConnectionRef.current;
    }
    
    const pc = new RTCPeerConnection(rtcConfig);
    peerConnectionRef.current = pc;
    
    pc.onicecandidate = (event) => {
      if (event.candidate && socket) {
        sendMessage({
          type: 'webrtc_ice_candidate',
          candidate: event.candidate,
          streamId,
          targetConnectionId: connectionId.current,
        });
      }
    };
    
    pc.ontrack = (event) => {
      const [stream] = event.streams;
      setRemoteStream(stream);
      if (remoteVideoRef.current) {
        remoteVideoRef.current.srcObject = stream;
      }
    };
    
    pc.onconnectionstatechange = () => {
      setIsConnected(pc.connectionState === 'connected');
      if (pc.connectionState === 'failed') {
        setError('WebRTC connection failed');
      }
    };
    
    return pc;
  }, [socket, sendMessage, streamId]);
  
  function handleWebSocketMessage(data: any) {
    if (data.streamId !== streamId) return;
    
    switch (data.type) {
      case 'webrtc_offer':
        handleOffer(data.offer);
        break;
      case 'webrtc_answer':
        handleAnswer(data.answer);
        break;
      case 'webrtc_ice_candidate':
        handleIceCandidate(data.candidate);
        break;
    }
  }
  
  const handleOffer = async (offer: RTCSessionDescriptionInit) => {
    try {
      const pc = createPeerConnection();
      await pc.setRemoteDescription(offer);
      
      const answer = await pc.createAnswer();
      await pc.setLocalDescription(answer);
      
      sendMessage({
        type: 'webrtc_answer',
        answer,
        streamId,
        targetConnectionId: connectionId.current,
      });
    } catch (err) {
      setError('Failed to handle offer');
      console.error('Handle offer error:', err);
    }
  };
  
  const handleAnswer = async (answer: RTCSessionDescriptionInit) => {
    try {
      const pc = peerConnectionRef.current;
      if (pc) {
        await pc.setRemoteDescription(answer);
      }
    } catch (err) {
      setError('Failed to handle answer');
      console.error('Handle answer error:', err);
    }
  };
  
  const handleIceCandidate = async (candidate: RTCIceCandidateInit) => {
    try {
      const pc = peerConnectionRef.current;
      if (pc) {
        await pc.addIceCandidate(candidate);
      }
    } catch (err) {
      console.error('Handle ICE candidate error:', err);
    }
  };
  
  const startStreaming = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: true,
      });
      
      setLocalStream(stream);
      setIsPublishing(true);
      
      if (localVideoRef.current) {
        localVideoRef.current.srcObject = stream;
      }
      
      const pc = createPeerConnection();
      stream.getTracks().forEach(track => {
        pc.addTrack(track, stream);
      });
      
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      
      sendMessage({
        type: 'webrtc_offer',
        offer,
        streamId,
        connectionType: 'publisher',
      });
      
    } catch (err) {
      setError('Failed to start streaming');
      console.error('Start streaming error:', err);
    }
  }, [createPeerConnection, sendMessage, streamId]);
  
  const stopStreaming = useCallback(() => {
    if (localStream) {
      localStream.getTracks().forEach(track => track.stop());
      setLocalStream(null);
    }
    
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    
    setIsPublishing(false);
    setIsConnected(false);
  }, [localStream]);
  
  const joinStream = useCallback(() => {
    if (!wsConnected) return;
    
    connectionId.current = Math.random().toString(36).substring(7);
    
    sendMessage({
      type: 'join_stream',
      streamId,
      connectionId: connectionId.current,
      connectionType: 'subscriber',
    });
    
    createPeerConnection();
  }, [wsConnected, sendMessage, streamId, createPeerConnection]);
  
  const leaveStream = useCallback(() => {
    if (peerConnectionRef.current) {
      peerConnectionRef.current.close();
      peerConnectionRef.current = null;
    }
    
    setRemoteStream(null);
    setIsConnected(false);
    
    sendMessage({
      type: 'leave_stream',
      streamId,
      connectionId: connectionId.current,
    });
  }, [sendMessage, streamId]);
  
  useEffect(() => {
    return () => {
      stopStreaming();
      leaveStream();
    };
  }, [stopStreaming, leaveStream]);
  
  return {
    localStream,
    remoteStream,
    isConnected,
    isPublishing,
    error,
    startStreaming,
    stopStreaming,
    joinStream,
    leaveStream,
    localVideoRef,
    remoteVideoRef,
  };
}

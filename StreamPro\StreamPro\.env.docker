# Findz Gaming Streaming Platform - Docker Environment
# This file contains all environment variables for Docker deployment

# Database Configuration
DATABASE_URL=****************************************************************/findz_gaming_streaming
REDIS_URL=redis://findz-redis:6379

# Server Configuration
NODE_ENV=production
PORT=5001
HOST=0.0.0.0

# Gaming Streaming Configuration
STREAM_QUALITY_DEFAULT=720p
STREAM_BITRATE_DEFAULT=2500
MAX_VIEWERS_PER_STREAM=1000
STREAM_TIMEOUT_MINUTES=180

# WebRTC Configuration
STUN_SERVER_URL=stun:stun.l.google.com:19302
TURN_SERVER_URL=
TURN_SERVER_USERNAME=
TURN_SERVER_CREDENTIAL=

# Gaming Features
ENABLE_GAME_CATEGORIES=true
ENABLE_AI_MODERATION=true
ENABLE_STREAM_RECORDING=true
ENABLE_CHAT_HISTORY=true
ENABLE_ANALYTICS=true

# Security Configuration
JWT_SECRET=findz_gaming_streaming_jwt_secret_2024_super_secure
JWT_EXPIRES_IN=7d
CORS_ORIGIN=http://localhost:8082,http://localhost:3000,http://host.docker.internal:8082

# Gaming Hub Integration
GAMING_HUB_URL=http://host.docker.internal:8082
GAMING_HUB_API_KEY=findz-gaming-hub-integration
GAMING_HUB_WEBHOOK_SECRET=findz-webhook-secret-2024

# File Upload Configuration
MAX_FILE_SIZE=100MB
UPLOAD_PATH=/app/uploads
STREAM_RECORDING_PATH=/app/recordings
THUMBNAIL_PATH=/app/thumbnails

# AI Integration (Optional)
OPENAI_API_KEY=
ANTHROPIC_API_KEY=
DEEPSEEK_API_KEY=

# Monitoring and Logging
LOG_LEVEL=info
ENABLE_REQUEST_LOGGING=true
ENABLE_PERFORMANCE_MONITORING=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Session Configuration
SESSION_SECRET=findz_session_secret_2024_ultra_secure
SESSION_MAX_AGE=86400000

# Email Configuration (Optional)
SMTP_HOST=
SMTP_PORT=587
SMTP_USER=
SMTP_PASS=
FROM_EMAIL=<EMAIL>

# Cloud Storage (Optional)
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_S3_BUCKET=findz-gaming-streams
AWS_REGION=us-east-1

# Analytics and Metrics
ENABLE_ANALYTICS=true
ANALYTICS_RETENTION_DAYS=30
ENABLE_REAL_TIME_METRICS=true

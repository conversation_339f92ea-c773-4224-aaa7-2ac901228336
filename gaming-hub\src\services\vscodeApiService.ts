import axios from 'axios';
import { Platform } from 'react-native';

export interface VSCodeProject {
  id: string;
  name: string;
  type: 'html5' | 'phaser' | 'react' | 'unity' | 'godot';
  description: string;
  path: string;
  lastModified: Date;
  status: 'active' | 'completed' | 'archived';
}

export interface GameTemplate {
  id: string;
  name: string;
  description: string;
  type: 'html5' | 'phaser' | 'react' | 'unity' | 'godot';
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  features: string[];
  files: TemplateFile[];
}

export interface TemplateFile {
  path: string;
  content: string;
  type: 'html' | 'css' | 'js' | 'json' | 'md';
}

export interface VSCodeCommand {
  command: string;
  args?: any[];
}

class VSCodeApiService {
  private baseUrl: string;
  private isConnected: boolean = false;

  constructor() {
    // VS Code extension will run a local server for communication
    this.baseUrl = 'http://localhost:3001';
  }

  async checkConnection(): Promise<boolean> {
    try {
      const response = await axios.get(`${this.baseUrl}/health`, { timeout: 3000 });
      this.isConnected = response.status === 200;
      return this.isConnected;
    } catch (error) {
      this.isConnected = false;
      return false;
    }
  }

  async getGameTemplates(): Promise<GameTemplate[]> {
    return [
      {
        id: 'html5-basic',
        name: 'Basic HTML5 Game',
        description: 'Simple HTML5 canvas game with basic controls',
        type: 'html5',
        difficulty: 'beginner',
        features: ['Canvas Rendering', 'Keyboard Input', 'Basic Physics'],
        files: [
          {
            path: 'index.html',
            type: 'html',
            content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Game</title>
    <style>
        body { margin: 0; padding: 0; background: #000; display: flex; justify-content: center; align-items: center; height: 100vh; }
        canvas { border: 1px solid #fff; }
    </style>
</head>
<body>
    <canvas id="gameCanvas" width="800" height="600"></canvas>
    <script src="game.js"></script>
</body>
</html>`
          },
          {
            path: 'game.js',
            type: 'js',
            content: `// Basic HTML5 Game Template
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Game state
const game = {
    player: { x: 400, y: 300, width: 50, height: 50, speed: 5 },
    keys: {},
    running: true
};

// Input handling
document.addEventListener('keydown', (e) => {
    game.keys[e.key] = true;
});

document.addEventListener('keyup', (e) => {
    game.keys[e.key] = false;
});

// Game loop
function update() {
    // Player movement
    if (game.keys['ArrowLeft'] || game.keys['a']) {
        game.player.x -= game.player.speed;
    }
    if (game.keys['ArrowRight'] || game.keys['d']) {
        game.player.x += game.player.speed;
    }
    if (game.keys['ArrowUp'] || game.keys['w']) {
        game.player.y -= game.player.speed;
    }
    if (game.keys['ArrowDown'] || game.keys['s']) {
        game.player.y += game.player.speed;
    }

    // Keep player in bounds
    game.player.x = Math.max(0, Math.min(canvas.width - game.player.width, game.player.x));
    game.player.y = Math.max(0, Math.min(canvas.height - game.player.height, game.player.y));
}

function render() {
    // Clear canvas
    ctx.fillStyle = '#000';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw player
    ctx.fillStyle = '#0f0';
    ctx.fillRect(game.player.x, game.player.y, game.player.width, game.player.height);

    // Draw instructions
    ctx.fillStyle = '#fff';
    ctx.font = '16px Arial';
    ctx.fillText('Use WASD or Arrow Keys to move', 10, 30);
}

function gameLoop() {
    if (game.running) {
        update();
        render();
        requestAnimationFrame(gameLoop);
    }
}

// Start the game
gameLoop();`
          }
        ]
      },
      {
        id: 'phaser-platformer',
        name: 'Phaser Platformer',
        description: 'Side-scrolling platformer game using Phaser.js',
        type: 'phaser',
        difficulty: 'intermediate',
        features: ['Physics Engine', 'Sprite Animation', 'Level Design'],
        files: [
          {
            path: 'index.html',
            type: 'html',
            content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phaser Platformer</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body { margin: 0; padding: 0; background: #2c3e50; display: flex; justify-content: center; align-items: center; height: 100vh; }
    </style>
</head>
<body>
    <script src="game.js"></script>
</body>
</html>`
          },
          {
            path: 'game.js',
            type: 'js',
            content: `// Phaser Platformer Game
class GameScene extends Phaser.Scene {
    constructor() {
        super({ key: 'GameScene' });
    }

    preload() {
        // Create simple colored rectangles as sprites
        this.add.graphics()
            .fillStyle(0x00ff00)
            .fillRect(0, 0, 32, 48)
            .generateTexture('player', 32, 48);
        
        this.add.graphics()
            .fillStyle(0x8B4513)
            .fillRect(0, 0, 64, 32)
            .generateTexture('ground', 64, 32);
    }

    create() {
        // Create platforms
        this.platforms = this.physics.add.staticGroup();
        this.platforms.create(400, 568, 'ground').setScale(12.5, 1).refreshBody();
        this.platforms.create(600, 400, 'ground');
        this.platforms.create(50, 250, 'ground');
        this.platforms.create(750, 220, 'ground');

        // Create player
        this.player = this.physics.add.sprite(100, 450, 'player');
        this.player.setBounce(0.2);
        this.player.setCollideWorldBounds(true);

        // Player physics
        this.physics.add.collider(this.player, this.platforms);

        // Controls
        this.cursors = this.input.keyboard.createCursorKeys();
        this.wasd = this.input.keyboard.addKeys('W,S,A,D');
    }

    update() {
        if (this.cursors.left.isDown || this.wasd.A.isDown) {
            this.player.setVelocityX(-160);
        } else if (this.cursors.right.isDown || this.wasd.D.isDown) {
            this.player.setVelocityX(160);
        } else {
            this.player.setVelocityX(0);
        }

        if ((this.cursors.up.isDown || this.wasd.W.isDown) && this.player.body.touching.down) {
            this.player.setVelocityY(-330);
        }
    }
}

const config = {
    type: Phaser.AUTO,
    width: 800,
    height: 600,
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 300 },
            debug: false
        }
    },
    scene: GameScene
};

const game = new Phaser.Game(config);`
          }
        ]
      },
      {
        id: 'react-game',
        name: 'React Game Component',
        description: 'Interactive game built with React components',
        type: 'react',
        difficulty: 'intermediate',
        features: ['Component Architecture', 'State Management', 'Event Handling'],
        files: [
          {
            path: 'package.json',
            type: 'json',
            content: `{
  "name": "react-game",
  "version": "1.0.0",
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0"
  },
  "scripts": {
    "start": "react-scripts start",
    "build": "react-scripts build"
  }
}`
          },
          {
            path: 'src/Game.js',
            type: 'js',
            content: `import React, { useState, useEffect, useCallback } from 'react';
import './Game.css';

const Game = () => {
  const [playerPos, setPlayerPos] = useState({ x: 50, y: 50 });
  const [score, setScore] = useState(0);
  const [gameSize] = useState({ width: 400, height: 300 });

  const handleKeyPress = useCallback((event) => {
    const speed = 10;
    setPlayerPos(prev => {
      let newX = prev.x;
      let newY = prev.y;

      switch(event.key) {
        case 'ArrowLeft':
        case 'a':
          newX = Math.max(0, prev.x - speed);
          break;
        case 'ArrowRight':
        case 'd':
          newX = Math.min(gameSize.width - 30, prev.x + speed);
          break;
        case 'ArrowUp':
        case 'w':
          newY = Math.max(0, prev.y - speed);
          break;
        case 'ArrowDown':
        case 's':
          newY = Math.min(gameSize.height - 30, prev.y + speed);
          break;
        default:
          return prev;
      }

      return { x: newX, y: newY };
    });
  }, [gameSize]);

  useEffect(() => {
    window.addEventListener('keydown', handleKeyPress);
    return () => window.removeEventListener('keydown', handleKeyPress);
  }, [handleKeyPress]);

  return (
    <div className="game-container">
      <div className="game-info">
        <h2>React Game</h2>
        <p>Score: {score}</p>
        <p>Use WASD or Arrow Keys to move</p>
      </div>
      <div 
        className="game-area"
        style={{ width: gameSize.width, height: gameSize.height }}
      >
        <div 
          className="player"
          style={{ 
            left: playerPos.x, 
            top: playerPos.y 
          }}
        />
      </div>
    </div>
  );
};

export default Game;`
          },
          {
            path: 'src/Game.css',
            type: 'css',
            content: `.game-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  font-family: Arial, sans-serif;
}

.game-info {
  text-align: center;
  margin-bottom: 20px;
}

.game-area {
  position: relative;
  border: 2px solid #333;
  background: linear-gradient(45deg, #1e3c72, #2a5298);
  overflow: hidden;
}

.player {
  position: absolute;
  width: 30px;
  height: 30px;
  background: #00ff00;
  border-radius: 50%;
  transition: all 0.1s ease;
  box-shadow: 0 0 10px rgba(0, 255, 0, 0.5);
}`
          }
        ]
      }
    ];
  }

  async createProject(template: GameTemplate, projectName: string): Promise<{ success: boolean; error?: string; projectPath?: string }> {
    if (!this.isConnected) {
      return { success: false, error: 'VS Code extension not connected' };
    }

    try {
      const response = await axios.post(`${this.baseUrl}/create-project`, {
        template,
        projectName,
        timestamp: new Date().toISOString()
      });

      return {
        success: true,
        projectPath: response.data.projectPath
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to create project'
      };
    }
  }

  async openProject(projectPath: string): Promise<{ success: boolean; error?: string }> {
    if (!this.isConnected) {
      return { success: false, error: 'VS Code extension not connected' };
    }

    try {
      await axios.post(`${this.baseUrl}/open-project`, { projectPath });
      return { success: true };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Failed to open project'
      };
    }
  }

  async getProjects(): Promise<VSCodeProject[]> {
    if (!this.isConnected) {
      return [];
    }

    try {
      const response = await axios.get(`${this.baseUrl}/projects`);
      return response.data.projects || [];
    } catch (error) {
      console.error('Error fetching projects:', error);
      return [];
    }
  }

  async executeCommand(command: VSCodeCommand): Promise<{ success: boolean; result?: any; error?: string }> {
    if (!this.isConnected) {
      return { success: false, error: 'VS Code extension not connected' };
    }

    try {
      const response = await axios.post(`${this.baseUrl}/execute-command`, command);
      return {
        success: true,
        result: response.data.result
      };
    } catch (error: any) {
      return {
        success: false,
        error: error.response?.data?.error || error.message || 'Command execution failed'
      };
    }
  }

  async installExtension(): Promise<{ success: boolean; error?: string; installUrl?: string }> {
    // For now, provide instructions for manual installation
    // In the future, this could trigger automatic installation
    return {
      success: false,
      error: 'Manual installation required',
      installUrl: 'https://marketplace.visualstudio.com/items?itemName=findz.gaming-hub-extension'
    };
  }

  getInstallationInstructions(): string {
    return `
# VS Code Extension Installation

## Automatic Installation (Recommended)
1. Open VS Code
2. Go to Extensions (Ctrl+Shift+X)
3. Search for "Findz Gaming Hub"
4. Click Install

## Manual Installation
1. Download the extension from the marketplace
2. Open VS Code
3. Press Ctrl+Shift+P
4. Type "Extensions: Install from VSIX"
5. Select the downloaded .vsix file

## Setup
1. After installation, restart VS Code
2. The extension will start a local server on port 3001
3. Return to the Gaming Hub and test the connection

## Features
- Create game projects from templates
- Open projects directly in VS Code
- AI-powered code assistance
- Integrated debugging and testing
- Direct deployment to Gaming Hub
`;
  }
}

export const vscodeApiService = new VSCodeApiService();
export default vscodeApiService;

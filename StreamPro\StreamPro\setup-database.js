#!/usr/bin/env node

/**
 * Findz Gaming Streaming Platform - Database Setup
 * Sets up PostgreSQL database for the gaming streaming platform
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const readline = require('readline');

class FindzDatabaseSetup {
  constructor() {
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  async run() {
    console.log('🎮 Findz Gaming Streaming Platform - Database Setup');
    console.log('===================================================\n');

    try {
      // Check if PostgreSQL is available
      const hasPostgres = await this.checkPostgreSQL();
      
      if (!hasPostgres) {
        console.log('❌ PostgreSQL not found. Let\'s set up an alternative database.');
        await this.setupAlternativeDatabase();
      } else {
        console.log('✅ PostgreSQL found!');
        await this.setupPostgreSQL();
      }

      // Create environment file
      await this.createEnvironmentFile();

      // Initialize database schema
      await this.initializeSchema();

      console.log('\n🎉 Database Setup Complete!');
      console.log('============================');
      console.log('Your Findz Gaming Streaming Platform database is ready!');
      console.log('\n💡 Next steps:');
      console.log('   1. Start the platform: npm run dev:win');
      console.log('   2. Open http://localhost:5000');
      console.log('   3. Start streaming your gaming content!');

    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    } finally {
      this.rl.close();
    }
  }

  async checkPostgreSQL() {
    return new Promise((resolve) => {
      exec('psql --version', (error) => {
        resolve(!error);
      });
    });
  }

  async setupAlternativeDatabase() {
    console.log('\n🔧 Setting up SQLite database for development...');
    
    // Install better-sqlite3 for local development
    console.log('📦 Installing SQLite database driver...');
    
    try {
      await this.executeCommand('npm', ['install', 'better-sqlite3', 'drizzle-orm']);
      console.log('✅ SQLite driver installed!');
      
      // Create SQLite-compatible database configuration
      await this.createSQLiteConfig();
      
    } catch (error) {
      console.log('⚠️ Could not install SQLite. Using in-memory database for demo.');
      await this.createDemoConfig();
    }
  }

  async setupPostgreSQL() {
    const dbChoice = await this.askQuestion(
      'Choose PostgreSQL setup:\n' +
      '1. Local PostgreSQL instance\n' +
      '2. Cloud database (Neon, Supabase, etc.)\n' +
      '3. Docker PostgreSQL\n' +
      'Enter your choice (1-3): '
    );

    switch (dbChoice.trim()) {
      case '1':
        await this.setupLocalPostgreSQL();
        break;
      case '2':
        await this.setupCloudDatabase();
        break;
      case '3':
        await this.setupDockerPostgreSQL();
        break;
      default:
        console.log('❌ Invalid choice. Using SQLite fallback.');
        await this.setupAlternativeDatabase();
    }
  }

  async setupLocalPostgreSQL() {
    console.log('\n🔧 Setting up local PostgreSQL...');
    
    const dbName = 'findz_gaming_streaming';
    const dbUser = 'findz_user';
    const dbPassword = await this.askQuestion('Enter database password (or press Enter for default): ') || 'findz_password';

    try {
      // Create database and user
      console.log('📊 Creating database and user...');
      
      const createDbCommand = `createdb ${dbName}`;
      const createUserCommand = `psql -c "CREATE USER ${dbUser} WITH PASSWORD '${dbPassword}'; GRANT ALL PRIVILEGES ON DATABASE ${dbName} TO ${dbUser};"`;
      
      await this.executeCommand('sh', ['-c', createDbCommand]);
      await this.executeCommand('sh', ['-c', createUserCommand]);
      
      this.databaseUrl = `postgresql://${dbUser}:${dbPassword}@localhost:5432/${dbName}`;
      console.log('✅ Local PostgreSQL database created!');
      
    } catch (error) {
      console.log('⚠️ Could not create local database. Using alternative setup.');
      await this.setupAlternativeDatabase();
    }
  }

  async setupCloudDatabase() {
    console.log('\n☁️ Setting up cloud database...');
    
    const dbUrl = await this.askQuestion(
      'Enter your cloud database URL\n' +
      '(e.g., ********************************/dbname): '
    );

    if (dbUrl && dbUrl.startsWith('postgresql://')) {
      this.databaseUrl = dbUrl;
      console.log('✅ Cloud database URL configured!');
    } else {
      console.log('❌ Invalid database URL. Using SQLite fallback.');
      await this.setupAlternativeDatabase();
    }
  }

  async setupDockerPostgreSQL() {
    console.log('\n🐳 Setting up Docker PostgreSQL...');
    
    try {
      // Check if Docker is available
      await this.executeCommand('docker', ['--version']);
      
      console.log('📦 Starting PostgreSQL container...');
      
      const containerName = 'findz-postgres';
      const dbPassword = 'findz_password';
      
      // Start PostgreSQL container
      await this.executeCommand('docker', [
        'run', '--name', containerName,
        '-e', `POSTGRES_PASSWORD=${dbPassword}`,
        '-e', 'POSTGRES_DB=findz_gaming_streaming',
        '-e', 'POSTGRES_USER=findz_user',
        '-p', '5432:5432',
        '-d', 'postgres:15'
      ]);
      
      this.databaseUrl = `postgresql://findz_user:${dbPassword}@localhost:5432/findz_gaming_streaming`;
      console.log('✅ Docker PostgreSQL container started!');
      
    } catch (error) {
      console.log('⚠️ Docker not available. Using SQLite fallback.');
      await this.setupAlternativeDatabase();
    }
  }

  async createSQLiteConfig() {
    // Create SQLite database configuration
    this.databaseUrl = 'file:./findz_gaming_streaming.db';
    
    // Create SQLite-compatible database file
    const dbPath = path.join(__dirname, 'findz_gaming_streaming.db');
    if (!fs.existsSync(dbPath)) {
      fs.writeFileSync(dbPath, '');
    }
    
    console.log('✅ SQLite database configured!');
  }

  async createDemoConfig() {
    // Create demo configuration with mock database
    this.databaseUrl = 'postgresql://demo:demo@localhost:5432/findz_gaming_streaming_demo';
    console.log('✅ Demo database configuration created!');
  }

  async createEnvironmentFile() {
    console.log('\n📝 Creating environment configuration...');
    
    const envContent = `# Findz Gaming Streaming Platform Environment Variables
# Generated on ${new Date().toISOString()}

# Database Configuration
DATABASE_URL="${this.databaseUrl}"

# Server Configuration
NODE_ENV=development
PORT=5000

# Gaming Streaming Configuration
STREAM_QUALITY_DEFAULT=720p
STREAM_BITRATE_DEFAULT=2500
MAX_VIEWERS_PER_STREAM=1000

# WebRTC Configuration
STUN_SERVER_URL="stun:stun.l.google.com:19302"
TURN_SERVER_URL=""
TURN_SERVER_USERNAME=""
TURN_SERVER_CREDENTIAL=""

# Gaming Features
ENABLE_GAME_CATEGORIES=true
ENABLE_AI_MODERATION=true
ENABLE_STREAM_RECORDING=true

# Security
JWT_SECRET="${this.generateSecret()}"
CORS_ORIGIN="http://localhost:8082,http://localhost:3000"

# Optional: Cloud Storage for Recordings
# AWS_ACCESS_KEY_ID=""
# AWS_SECRET_ACCESS_KEY=""
# AWS_S3_BUCKET=""

# Optional: AI Integration
# OPENAI_API_KEY=""
# ANTHROPIC_API_KEY=""
`;

    fs.writeFileSync(path.join(__dirname, '.env'), envContent);
    console.log('✅ Environment file created!');
  }

  async initializeSchema() {
    console.log('\n🗄️ Initializing database schema...');
    
    try {
      // Run database migrations
      await this.executeCommand('npm', ['run', 'db:push']);
      console.log('✅ Database schema initialized!');
    } catch (error) {
      console.log('⚠️ Schema initialization will be done on first run.');
    }
  }

  generateSecret() {
    return require('crypto').randomBytes(32).toString('hex');
  }

  async executeCommand(command, args = [], options = {}) {
    return new Promise((resolve, reject) => {
      const process = spawn(command, args, { 
        stdio: 'inherit',
        ...options
      });

      process.on('close', (code) => {
        if (code === 0) {
          resolve();
        } else {
          reject(new Error(`Command failed with code ${code}`));
        }
      });

      process.on('error', (error) => {
        reject(error);
      });
    });
  }

  askQuestion(question) {
    return new Promise((resolve) => {
      this.rl.question(question, resolve);
    });
  }
}

// Run the setup
if (require.main === module) {
  const setup = new FindzDatabaseSetup();
  setup.run().catch(console.error);
}

module.exports = FindzDatabaseSetup;

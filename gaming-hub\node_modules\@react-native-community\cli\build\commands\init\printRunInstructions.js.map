{"version": 3, "names": ["printRunInstructions", "projectDir", "projectName", "options", "showPodsInstructions", "iosInstructions", "desktopInstructions", "process", "platform", "iosProjectDir", "path", "resolve", "iosPodsFile", "isUsingPods", "fs", "existsSync", "relativeXcodeProjectPath", "relative", "chalk", "cyan", "bold", "dim", "magenta", "underline", "androidInstructions", "green", "logger", "log"], "sources": ["../../../src/commands/init/printRunInstructions.ts"], "sourcesContent": ["/**\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * @format\n */\n\nimport path from 'path';\nimport fs from 'fs';\nimport process from 'process';\nimport chalk from 'chalk';\nimport {logger} from '@react-native-community/cli-tools';\n\ninterface Options {\n  showPodsInstructions?: boolean;\n}\n\nfunction printRunInstructions(\n  projectDir: string,\n  projectName: string,\n  options: Options = {\n    showPodsInstructions: false,\n  },\n) {\n  let iosInstructions = '';\n  let desktopInstructions = '';\n\n  if (process.platform === 'darwin') {\n    const iosProjectDir = path.resolve(projectDir, 'ios');\n    const iosPodsFile = path.resolve(\n      iosProjectDir,\n      `${projectName}.xcworkspace`,\n    );\n    const isUsingPods = fs.existsSync(iosPodsFile);\n\n    const relativeXcodeProjectPath = path.relative(\n      '..',\n      isUsingPods\n        ? iosPodsFile\n        : path.resolve(iosProjectDir, `${projectName}.xcodeproj`),\n    );\n\n    iosInstructions = `\n  ${chalk.cyan(`Run instructions for ${chalk.bold('iOS')}`)}:\n    • cd \"${projectDir}${options.showPodsInstructions ? '/ios' : ''}\"\n    ${\n      options.showPodsInstructions\n        ? `\n    • Install Cocoapods\n      • bundle install # you need to run this only once in your project.\n      • bundle exec pod install\n      • cd ..\n    `\n        : ''\n    }\n    • npx react-native run-ios\n    ${chalk.dim('- or -')}\n    • Open ${relativeXcodeProjectPath} in Xcode or run \"xed -b ios\"\n    • Hit the Run button\n    `;\n\n    desktopInstructions = `\n  ${chalk.magenta(`Run instructions for ${chalk.bold('macOS')}`)}:\n    • See ${chalk.underline(\n      'https://aka.ms/ReactNativeGuideMacOS',\n    )} for the latest up-to-date instructions.\n    `;\n  }\n\n  if (process.platform === 'win32') {\n    desktopInstructions = `\n  ${chalk.cyan(`Run instructions for ${chalk.bold('Windows')}`)}:\n    • See ${chalk.underline(\n      'https://aka.ms/ReactNativeGuideWindows',\n    )} for the latest up-to-date instructions.\n    `;\n  }\n\n  const androidInstructions = `\n  ${chalk.green(`Run instructions for ${chalk.bold('Android')}`)}:\n    • Have an Android emulator running (quickest way to get started), or a device connected.\n    • cd \"${projectDir}\" && npx react-native run-android\n  `;\n\n  logger.log(`\n  ${androidInstructions}${iosInstructions}${desktopInstructions}\n  `);\n}\n\nexport default printRunInstructions;\n"], "mappings": ";;;;;;AASA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AACA;EAAA;EAAA;IAAA;EAAA;EAAA;AAAA;AAAyD;AAbzD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAYA,SAASA,oBAAoB,CAC3BC,UAAkB,EAClBC,WAAmB,EACnBC,OAAgB,GAAG;EACjBC,oBAAoB,EAAE;AACxB,CAAC,EACD;EACA,IAAIC,eAAe,GAAG,EAAE;EACxB,IAAIC,mBAAmB,GAAG,EAAE;EAE5B,IAAIC,kBAAO,CAACC,QAAQ,KAAK,QAAQ,EAAE;IACjC,MAAMC,aAAa,GAAGC,eAAI,CAACC,OAAO,CAACV,UAAU,EAAE,KAAK,CAAC;IACrD,MAAMW,WAAW,GAAGF,eAAI,CAACC,OAAO,CAC9BF,aAAa,EACZ,GAAEP,WAAY,cAAa,CAC7B;IACD,MAAMW,WAAW,GAAGC,aAAE,CAACC,UAAU,CAACH,WAAW,CAAC;IAE9C,MAAMI,wBAAwB,GAAGN,eAAI,CAACO,QAAQ,CAC5C,IAAI,EACJJ,WAAW,GACPD,WAAW,GACXF,eAAI,CAACC,OAAO,CAACF,aAAa,EAAG,GAAEP,WAAY,YAAW,CAAC,CAC5D;IAEDG,eAAe,GAAI;AACvB,IAAIa,gBAAK,CAACC,IAAI,CAAE,wBAAuBD,gBAAK,CAACE,IAAI,CAAC,KAAK,CAAE,EAAC,CAAE;AAC5D,YAAYnB,UAAW,GAAEE,OAAO,CAACC,oBAAoB,GAAG,MAAM,GAAG,EAAG;AACpE,MACMD,OAAO,CAACC,oBAAoB,GACvB;AACX;AACA;AACA;AACA;AACA,KAAK,GACK,EACL;AACL;AACA,MAAMc,gBAAK,CAACG,GAAG,CAAC,QAAQ,CAAE;AAC1B,aAAaL,wBAAyB;AACtC;AACA,KAAK;IAEDV,mBAAmB,GAAI;AAC3B,IAAIY,gBAAK,CAACI,OAAO,CAAE,wBAAuBJ,gBAAK,CAACE,IAAI,CAAC,OAAO,CAAE,EAAC,CAAE;AACjE,YAAYF,gBAAK,CAACK,SAAS,CACrB,sCAAsC,CACtC;AACN,KAAK;EACH;EAEA,IAAIhB,kBAAO,CAACC,QAAQ,KAAK,OAAO,EAAE;IAChCF,mBAAmB,GAAI;AAC3B,IAAIY,gBAAK,CAACC,IAAI,CAAE,wBAAuBD,gBAAK,CAACE,IAAI,CAAC,SAAS,CAAE,EAAC,CAAE;AAChE,YAAYF,gBAAK,CAACK,SAAS,CACrB,wCAAwC,CACxC;AACN,KAAK;EACH;EAEA,MAAMC,mBAAmB,GAAI;AAC/B,IAAIN,gBAAK,CAACO,KAAK,CAAE,wBAAuBP,gBAAK,CAACE,IAAI,CAAC,SAAS,CAAE,EAAC,CAAE;AACjE;AACA,YAAYnB,UAAW;AACvB,GAAG;EAEDyB,kBAAM,CAACC,GAAG,CAAE;AACd,IAAIH,mBAAoB,GAAEnB,eAAgB,GAAEC,mBAAoB;AAChE,GAAG,CAAC;AACJ;AAAC,eAEcN,oBAAoB;AAAA"}
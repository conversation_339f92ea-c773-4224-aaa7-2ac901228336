# @react-native-community/cli-doctor

This package is part of the [React Native CLI](../../README.md). It contains commands for diagnosing and fixing common Node.js, iOS, Android & React Native issues.

## Installation

```sh
yarn add @react-native-community/cli-doctor
```

## Commands

### `doctor`

Usage:

```sh
npx react-native doctor
```

Diagnose and fix common Node.js, iOS, Android & React Native issues.

### `info`

Usage:

```sh
npx react-native info
```

Get relevant version info about OS, toolchain and libraries. Useful when sending bug reports.

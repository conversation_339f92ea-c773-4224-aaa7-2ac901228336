export class WebRTCManager {
  private peerConnection: RTCPeerConnection | null = null;
  private localStream: MediaStream | null = null;
  private remoteStream: MediaStream | null = null;
  private socket: WebSocket | null = null;
  private streamId: number | null = null;
  
  private rtcConfig: RTCConfiguration = {
    iceServers: [
      { urls: 'stun:stun.l.google.com:19302' },
      { urls: 'stun:stun1.l.google.com:19302' },
      { urls: 'stun:stun2.l.google.com:19302' },
    ],
  };
  
  constructor(streamId: number) {
    this.streamId = streamId;
    this.initializeWebSocket();
  }
  
  private initializeWebSocket() {
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws`;
    
    this.socket = new WebSocket(wsUrl);
    
    this.socket.onopen = () => {
      console.log('WebSocket connected');
    };
    
    this.socket.onmessage = (event) => {
      const data = JSON.parse(event.data);
      this.handleSignalingMessage(data);
    };
    
    this.socket.onclose = () => {
      console.log('WebSocket disconnected');
    };
    
    this.socket.onerror = (error) => {
      console.error('WebSocket error:', error);
    };
  }
  
  private async handleSignalingMessage(data: any) {
    if (data.streamId !== this.streamId) return;
    
    switch (data.type) {
      case 'webrtc_offer':
        await this.handleOffer(data.offer);
        break;
      case 'webrtc_answer':
        await this.handleAnswer(data.answer);
        break;
      case 'webrtc_ice_candidate':
        await this.handleIceCandidate(data.candidate);
        break;
    }
  }
  
  private createPeerConnection(): RTCPeerConnection {
    if (this.peerConnection) {
      return this.peerConnection;
    }
    
    this.peerConnection = new RTCPeerConnection(this.rtcConfig);
    
    this.peerConnection.onicecandidate = (event) => {
      if (event.candidate && this.socket) {
        this.socket.send(JSON.stringify({
          type: 'webrtc_ice_candidate',
          candidate: event.candidate,
          streamId: this.streamId,
        }));
      }
    };
    
    this.peerConnection.ontrack = (event) => {
      const [stream] = event.streams;
      this.remoteStream = stream;
      this.onRemoteStream?.(stream);
    };
    
    this.peerConnection.onconnectionstatechange = () => {
      console.log('Connection state:', this.peerConnection?.connectionState);
      this.onConnectionStateChange?.(this.peerConnection?.connectionState || 'closed');
    };
    
    return this.peerConnection;
  }
  
  private async handleOffer(offer: RTCSessionDescriptionInit) {
    const pc = this.createPeerConnection();
    await pc.setRemoteDescription(offer);
    
    const answer = await pc.createAnswer();
    await pc.setLocalDescription(answer);
    
    if (this.socket) {
      this.socket.send(JSON.stringify({
        type: 'webrtc_answer',
        answer,
        streamId: this.streamId,
      }));
    }
  }
  
  private async handleAnswer(answer: RTCSessionDescriptionInit) {
    if (this.peerConnection) {
      await this.peerConnection.setRemoteDescription(answer);
    }
  }
  
  private async handleIceCandidate(candidate: RTCIceCandidateInit) {
    if (this.peerConnection) {
      await this.peerConnection.addIceCandidate(candidate);
    }
  }
  
  async startPublishing(constraints: MediaStreamConstraints = { video: true, audio: true }) {
    try {
      this.localStream = await navigator.mediaDevices.getUserMedia(constraints);
      
      const pc = this.createPeerConnection();
      this.localStream.getTracks().forEach(track => {
        pc.addTrack(track, this.localStream!);
      });
      
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      
      if (this.socket) {
        this.socket.send(JSON.stringify({
          type: 'webrtc_offer',
          offer,
          streamId: this.streamId,
          connectionType: 'publisher',
        }));
      }
      
      return this.localStream;
    } catch (error) {
      console.error('Failed to start publishing:', error);
      throw error;
    }
  }
  
  async startViewing() {
    try {
      const pc = this.createPeerConnection();
      
      if (this.socket) {
        this.socket.send(JSON.stringify({
          type: 'join_stream',
          streamId: this.streamId,
          connectionType: 'subscriber',
        }));
      }
      
    } catch (error) {
      console.error('Failed to start viewing:', error);
      throw error;
    }
  }
  
  stopPublishing() {
    if (this.localStream) {
      this.localStream.getTracks().forEach(track => track.stop());
      this.localStream = null;
    }
    
    this.closePeerConnection();
  }
  
  stopViewing() {
    this.closePeerConnection();
  }
  
  private closePeerConnection() {
    if (this.peerConnection) {
      this.peerConnection.close();
      this.peerConnection = null;
    }
  }
  
  destroy() {
    this.stopPublishing();
    this.stopViewing();
    
    if (this.socket) {
      this.socket.close();
      this.socket = null;
    }
  }
  
  // Event callbacks
  onRemoteStream?: (stream: MediaStream) => void;
  onConnectionStateChange?: (state: RTCPeerConnectionState) => void;
  onError?: (error: Error) => void;
}

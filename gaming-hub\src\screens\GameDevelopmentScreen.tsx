import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Alert,
  TextInput,
  Modal,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

import { vscodeApiService, GameTemplate, VSCodeProject } from '../services/vscodeApiService';
import { theme, colors } from '../theme';

export default function GameDevelopmentScreen() {
  const navigation = useNavigation();
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(true);
  const [templates, setTemplates] = useState<GameTemplate[]>([]);
  const [projects, setProjects] = useState<VSCodeProject[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<GameTemplate | null>(null);
  const [projectName, setProjectName] = useState('');
  const [creating, setCreating] = useState(false);

  useEffect(() => {
    checkConnection();
  }, []);

  const checkConnection = async () => {
    setLoading(true);
    try {
      const connected = await vscodeApiService.checkConnection();
      setIsConnected(connected);
      
      if (connected) {
        const [templatesData, projectsData] = await Promise.all([
          vscodeApiService.getGameTemplates(),
          vscodeApiService.getProjects()
        ]);
        setTemplates(templatesData);
        setProjects(projectsData);
      } else {
        setTemplates(await vscodeApiService.getGameTemplates());
      }
    } catch (error) {
      console.error('Error checking VS Code connection:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProject = async () => {
    if (!selectedTemplate || !projectName.trim()) {
      Alert.alert('Error', 'Please select a template and enter a project name');
      return;
    }

    setCreating(true);
    try {
      const result = await vscodeApiService.createProject(selectedTemplate, projectName.trim());
      
      if (result.success) {
        Alert.alert(
          'Success',
          `Project "${projectName}" created successfully!`,
          [
            { text: 'OK', onPress: () => {
              setShowCreateModal(false);
              setProjectName('');
              setSelectedTemplate(null);
              checkConnection(); // Refresh projects list
            }}
          ]
        );
      } else {
        Alert.alert('Error', result.error || 'Failed to create project');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to create project');
    } finally {
      setCreating(false);
    }
  };

  const handleOpenProject = async (project: VSCodeProject) => {
    try {
      const result = await vscodeApiService.openProject(project.path);
      if (result.success) {
        Alert.alert('Success', `Opened "${project.name}" in VS Code`);
      } else {
        Alert.alert('Error', result.error || 'Failed to open project');
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to open project');
    }
  };

  const handleInstallExtension = () => {
    const instructions = vscodeApiService.getInstallationInstructions();
    Alert.alert(
      'Install VS Code Extension',
      'To use game development features, you need to install the Findz Gaming Hub extension for VS Code.',
      [
        { text: 'Show Instructions', onPress: () => showInstallationInstructions(instructions) },
        { text: 'Cancel', style: 'cancel' }
      ]
    );
  };

  const showInstallationInstructions = (instructions: string) => {
    Alert.alert(
      'Installation Instructions',
      instructions,
      [{ text: 'OK' }],
      { cancelable: true }
    );
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return colors.success;
      case 'intermediate': return colors.warning;
      case 'advanced': return colors.error;
      default: return colors.textSecondary;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'html5': return 'code';
      case 'phaser': return 'game-controller';
      case 'react': return 'logo-react';
      case 'unity': return 'cube';
      case 'godot': return 'rocket';
      default: return 'document';
    }
  };

  const renderTemplate = (template: GameTemplate) => (
    <TouchableOpacity
      key={template.id}
      style={styles.templateCard}
      onPress={() => {
        setSelectedTemplate(template);
        setShowCreateModal(true);
      }}
    >
      <View style={styles.templateHeader}>
        <View style={styles.templateInfo}>
          <Ionicons 
            name={getTypeIcon(template.type) as any} 
            size={24} 
            color={colors.primary} 
          />
          <Text style={styles.templateName}>{template.name}</Text>
        </View>
        <View style={[styles.difficultyBadge, { backgroundColor: getDifficultyColor(template.difficulty) }]}>
          <Text style={styles.difficultyText}>{template.difficulty}</Text>
        </View>
      </View>
      
      <Text style={styles.templateDescription}>{template.description}</Text>
      
      <View style={styles.featuresContainer}>
        {template.features.slice(0, 3).map((feature, index) => (
          <View key={index} style={styles.featureBadge}>
            <Text style={styles.featureText}>{feature}</Text>
          </View>
        ))}
        {template.features.length > 3 && (
          <Text style={styles.moreFeatures}>+{template.features.length - 3} more</Text>
        )}
      </View>
    </TouchableOpacity>
  );

  const renderProject = (project: VSCodeProject) => (
    <TouchableOpacity
      key={project.id}
      style={styles.projectCard}
      onPress={() => handleOpenProject(project)}
    >
      <View style={styles.projectHeader}>
        <View style={styles.projectInfo}>
          <Ionicons 
            name={getTypeIcon(project.type) as any} 
            size={20} 
            color={colors.primary} 
          />
          <Text style={styles.projectName}>{project.name}</Text>
        </View>
        <View style={[styles.statusBadge, { backgroundColor: project.status === 'active' ? colors.success : colors.textSecondary }]}>
          <Text style={styles.statusText}>{project.status}</Text>
        </View>
      </View>
      
      <Text style={styles.projectDescription}>{project.description}</Text>
      <Text style={styles.projectPath}>{project.path}</Text>
      <Text style={styles.projectDate}>
        Last modified: {new Date(project.lastModified).toLocaleDateString()}
      </Text>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={colors.primary} />
        <Text style={styles.loadingText}>Checking VS Code connection...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <LinearGradient
        colors={colors.gradientPrimary}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Ionicons name="arrow-back" size={24} color={colors.text} />
          </TouchableOpacity>
          
          <View style={styles.headerInfo}>
            <Ionicons name="code-slash" size={32} color={colors.text} />
            <Text style={styles.headerTitle}>Game Development</Text>
            <Text style={styles.headerSubtitle}>
              Create games with AI assistance
            </Text>
          </View>
        </View>
      </LinearGradient>

      {/* Connection Status */}
      <View style={styles.section}>
        <View style={styles.connectionCard}>
          <View style={styles.connectionHeader}>
            <Ionicons 
              name={isConnected ? 'checkmark-circle' : 'close-circle'} 
              size={24} 
              color={isConnected ? colors.success : colors.error} 
            />
            <Text style={styles.connectionTitle}>
              VS Code Extension: {isConnected ? 'Connected' : 'Not Connected'}
            </Text>
          </View>
          
          {!isConnected && (
            <TouchableOpacity 
              style={styles.installButton}
              onPress={handleInstallExtension}
            >
              <Text style={styles.installButtonText}>Install Extension</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Game Templates */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Game Templates</Text>
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={checkConnection}
          >
            <Ionicons name="refresh" size={20} color={colors.primary} />
          </TouchableOpacity>
        </View>
        {templates.map(renderTemplate)}
      </View>

      {/* Recent Projects */}
      {isConnected && projects.length > 0 && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Recent Projects</Text>
          {projects.map(renderProject)}
        </View>
      )}

      {/* Create Project Modal */}
      <Modal
        visible={showCreateModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCreateModal(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Create New Project</Text>
            
            {selectedTemplate && (
              <View style={styles.selectedTemplate}>
                <Text style={styles.selectedTemplateTitle}>{selectedTemplate.name}</Text>
                <Text style={styles.selectedTemplateDesc}>{selectedTemplate.description}</Text>
              </View>
            )}
            
            <TextInput
              style={styles.projectNameInput}
              placeholder="Enter project name"
              placeholderTextColor={colors.textSecondary}
              value={projectName}
              onChangeText={setProjectName}
              autoFocus
            />
            
            <View style={styles.modalButtons}>
              <TouchableOpacity 
                style={[styles.modalButton, styles.cancelButton]}
                onPress={() => setShowCreateModal(false)}
              >
                <Text style={styles.cancelButtonText}>Cancel</Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.modalButton, styles.createButton]}
                onPress={handleCreateProject}
                disabled={creating}
              >
                {creating ? (
                  <ActivityIndicator size="small" color={colors.text} />
                ) : (
                  <Text style={styles.createButtonText}>Create</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
  },
  loadingText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginTop: theme.spacing.md,
  },
  header: {
    paddingTop: theme.spacing.xl,
    paddingHorizontal: theme.spacing.lg,
    paddingBottom: theme.spacing.lg,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  backButton: {
    padding: theme.spacing.xs,
    marginRight: theme.spacing.md,
  },
  headerInfo: {
    flex: 1,
    alignItems: 'center',
  },
  headerTitle: {
    ...theme.typography.textStyles.h2,
    color: colors.text,
    marginTop: theme.spacing.sm,
    marginBottom: theme.spacing.xs,
  },
  headerSubtitle: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    opacity: 0.9,
    textAlign: 'center',
  },
  section: {
    padding: theme.spacing.lg,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  sectionTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
  },
  refreshButton: {
    padding: theme.spacing.xs,
  },
  connectionCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.sm,
  },
  connectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  connectionTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginLeft: theme.spacing.sm,
  },
  installButton: {
    backgroundColor: colors.primary,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
  },
  installButtonText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
  },
  templateCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  templateHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  templateInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  templateName: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginLeft: theme.spacing.sm,
  },
  difficultyBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  difficultyText: {
    ...theme.typography.textStyles.small,
    color: colors.text,
    fontSize: 10,
    fontWeight: '600',
  },
  templateDescription: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    marginBottom: theme.spacing.md,
    lineHeight: 20,
  },
  featuresContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    alignItems: 'center',
  },
  featureBadge: {
    backgroundColor: colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
    marginRight: theme.spacing.xs,
    marginBottom: theme.spacing.xs,
  },
  featureText: {
    ...theme.typography.textStyles.small,
    color: colors.text,
    fontSize: 10,
  },
  moreFeatures: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    fontSize: 10,
  },
  projectCard: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    ...theme.shadows.sm,
  },
  projectHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  projectInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  projectName: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginLeft: theme.spacing.sm,
  },
  statusBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: 2,
    borderRadius: theme.borderRadius.sm,
  },
  statusText: {
    ...theme.typography.textStyles.small,
    color: colors.text,
    fontSize: 10,
    fontWeight: '600',
  },
  projectDescription: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  projectPath: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
    fontFamily: 'monospace',
    marginBottom: theme.spacing.xs,
  },
  projectDate: {
    ...theme.typography.textStyles.small,
    color: colors.textSecondary,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: colors.backgroundCard,
    padding: theme.spacing.lg,
    borderRadius: theme.borderRadius.lg,
    width: '90%',
    maxWidth: 400,
  },
  modalTitle: {
    ...theme.typography.textStyles.h3,
    color: colors.text,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  selectedTemplate: {
    backgroundColor: colors.background,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.lg,
  },
  selectedTemplateTitle: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    marginBottom: theme.spacing.xs,
  },
  selectedTemplateDesc: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
  },
  projectNameInput: {
    backgroundColor: colors.background,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    color: colors.text,
    fontSize: 16,
    marginBottom: theme.spacing.lg,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  modalButton: {
    flex: 1,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
    alignItems: 'center',
  },
  cancelButton: {
    backgroundColor: colors.textSecondary,
    marginRight: theme.spacing.sm,
  },
  createButton: {
    backgroundColor: colors.primary,
    marginLeft: theme.spacing.sm,
  },
  cancelButtonText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
  },
  createButtonText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
  },
});

import { useState, useEffect, useRef } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Send, Users } from "lucide-react";
import { useWebSocket } from "@/hooks/useWebSocket";
import { apiRequest } from "@/lib/queryClient";
import { cn } from "@/lib/utils";

interface ChatProps {
  streamId: number;
}

interface ChatMessage {
  id: number;
  streamId: number;
  userId: number;
  message: string;
  timestamp: string;
  type: 'message' | 'system' | 'moderator';
  user?: {
    id: number;
    username: string;
    profileImageUrl?: string;
  };
}

export function Chat({ streamId }: ChatProps) {
  const [message, setMessage] = useState("");
  const [viewerCount, setViewerCount] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const queryClient = useQueryClient();
  
  const { data: messages = [], isLoading } = useQuery({
    queryKey: [`/api/streams/${streamId}/chat`],
    enabled: !!streamId,
  });
  
  const { socket, isConnected } = useWebSocket('/ws', {
    onMessage: (data) => {
      if (data.type === 'chat_message') {
        // Add new message to the chat
        queryClient.setQueryData(
          [`/api/streams/${streamId}/chat`],
          (old: ChatMessage[]) => [...(old || []), data.message]
        );
        scrollToBottom();
      } else if (data.type === 'viewer_count_update') {
        setViewerCount(data.count);
      }
    },
  });
  
  const sendMessageMutation = useMutation({
    mutationFn: async (messageText: string) => {
      if (socket && isConnected) {
        socket.send(JSON.stringify({
          type: 'chat_message',
          streamId,
          userId: 1, // TODO: Get actual user ID
          message: messageText,
          messageType: 'message'
        }));
      }
    },
    onSuccess: () => {
      setMessage("");
    },
  });
  
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };
  
  useEffect(() => {
    scrollToBottom();
  }, [messages]);
  
  useEffect(() => {
    if (socket && isConnected) {
      // Join the stream chat
      socket.send(JSON.stringify({
        type: 'join_stream',
        streamId,
        userId: 1 // TODO: Get actual user ID
      }));
    }
    
    return () => {
      if (socket && isConnected) {
        socket.send(JSON.stringify({
          type: 'leave_stream',
          streamId
        }));
      }
    };
  }, [socket, isConnected, streamId]);
  
  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      sendMessageMutation.mutate(message.trim());
    }
  };
  
  const getInitials = (username: string) => {
    return username.split(' ').map(n => n[0]).join('').toUpperCase();
  };
  
  const getAvatarColor = (userId: number) => {
    const colors = [
      'bg-red-500', 'bg-blue-500', 'bg-green-500', 'bg-yellow-500',
      'bg-purple-500', 'bg-pink-500', 'bg-indigo-500', 'bg-teal-500'
    ];
    return colors[userId % colors.length];
  };
  
  return (
    <Card className="h-96 flex flex-col">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg">Live Chat</CardTitle>
          <div className="flex items-center space-x-2">
            <Badge variant="secondary" className="text-xs">
              <Users className="w-3 h-3 mr-1" />
              {viewerCount} participants
            </Badge>
            <div className={cn(
              "w-2 h-2 rounded-full",
              isConnected ? "bg-green-500" : "bg-red-500"
            )} />
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 flex flex-col p-0">
        {/* Messages */}
        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : messages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-center">
              <div>
                <Users className="w-12 h-12 mx-auto mb-2 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">No messages yet</p>
                <p className="text-xs text-muted-foreground">Be the first to say hello!</p>
              </div>
            </div>
          ) : (
            messages.map((msg: ChatMessage) => (
              <div key={msg.id} className="flex items-start space-x-3">
                <div className={cn(
                  "w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0",
                  getAvatarColor(msg.userId)
                )}>
                  <span className="text-white text-xs font-medium">
                    {msg.user?.username ? getInitials(msg.user.username) : 'U'}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2 mb-1">
                    <span className="text-sm font-medium">
                      {msg.user?.username || `User ${msg.userId}`}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {new Date(msg.timestamp).toLocaleTimeString()}
                    </span>
                    {msg.type === 'moderator' && (
                      <Badge variant="secondary" className="text-xs">MOD</Badge>
                    )}
                  </div>
                  <p className="text-sm text-foreground break-words">{msg.message}</p>
                </div>
              </div>
            ))
          )}
          <div ref={messagesEndRef} />
        </div>
        
        {/* Message Input */}
        <div className="p-4 border-t border-border">
          <form onSubmit={handleSendMessage} className="flex items-center space-x-2">
            <Input
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              placeholder="Type your message..."
              className="flex-1"
              disabled={!isConnected || sendMessageMutation.isPending}
            />
            <Button 
              type="submit" 
              size="sm"
              disabled={!message.trim() || !isConnected || sendMessageMutation.isPending}
            >
              <Send className="w-4 h-4" />
            </Button>
          </form>
        </div>
      </CardContent>
    </Card>
  );
}

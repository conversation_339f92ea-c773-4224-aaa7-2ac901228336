import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Alert,
  ActivityIndicator,
  Text,
  TouchableOpacity,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { Ionicons } from '@expo/vector-icons';
import { useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

import { RootStackParamList, GameSession } from '../types';
import { theme, colors } from '../theme';
import { supabase } from '../config/supabase';

type GameScreenRouteProp = RouteProp<RootStackParamList, 'Game'>;
type GameScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Game'>;

export default function GameScreen() {
  const route = useRoute<GameScreenRouteProp>();
  const navigation = useNavigation<GameScreenNavigationProp>();
  const webViewRef = useRef<WebView>(null);
  
  const { gameId, gameUrl, gameTitle } = route.params;
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(false);
  const [gameStartTime] = useState(Date.now());
  const [currentScore, setCurrentScore] = useState(0);

  useEffect(() => {
    // Set up navigation header with game controls
    navigation.setOptions({
      headerRight: () => (
        <View style={styles.headerControls}>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={refreshGame}
          >
            <Ionicons name="refresh" size={20} color={colors.text} />
          </TouchableOpacity>
          <TouchableOpacity
            style={styles.headerButton}
            onPress={showGameMenu}
          >
            <Ionicons name="menu" size={20} color={colors.text} />
          </TouchableOpacity>
        </View>
      ),
    });
  }, [navigation]);

  const refreshGame = () => {
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  };

  const showGameMenu = () => {
    Alert.alert(
      'Game Menu',
      'Choose an option',
      [
        { text: 'Refresh Game', onPress: refreshGame },
        { text: 'Exit Game', onPress: exitGame, style: 'destructive' },
        { text: 'Cancel', style: 'cancel' },
      ]
    );
  };

  const exitGame = () => {
    Alert.alert(
      'Exit Game',
      'Are you sure you want to exit? Your progress may be lost.',
      [
        { text: 'Stay', style: 'cancel' },
        { text: 'Exit', onPress: () => navigation.goBack(), style: 'destructive' },
      ]
    );
  };

  const handleWebViewMessage = async (event: any) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);
      
      switch (message.type) {
        case 'GAME_SCORE':
          setCurrentScore(message.score);
          break;
          
        case 'GAME_COMPLETED':
          await saveGameSession(message.score, message.duration || null);
          showGameCompletedAlert(message.score);
          break;
          
        case 'NAVIGATE_BACK':
          navigation.goBack();
          break;
          
        case 'GAME_ERROR':
          console.error('Game error:', message.error);
          setError(true);
          break;
          
        default:
          console.log('Unknown message from game:', message);
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  const saveGameSession = async (finalScore: number, duration?: number) => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      const sessionDuration = duration || Math.floor((Date.now() - gameStartTime) / 1000);
      
      const gameSession: Partial<GameSession> = {
        user_id: user.id,
        game_id: gameId,
        score: finalScore,
        duration: sessionDuration,
        completed_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('game_sessions')
        .insert([gameSession]);

      if (error) {
        console.error('Error saving game session:', error);
      } else {
        // Update user's total score
        await supabase.rpc('update_user_stats', {
          user_id: user.id,
          score_to_add: finalScore,
        });
      }
    } catch (error) {
      console.error('Error in saveGameSession:', error);
    }
  };

  const showGameCompletedAlert = (score: number) => {
    const sessionDuration = Math.floor((Date.now() - gameStartTime) / 1000);

    Alert.alert(
      'Game Completed!',
      `Congratulations! Your final score: ${score}`,
      [
        { text: 'Play Again', onPress: refreshGame },
        {
          text: 'Get AI Coaching',
          onPress: () => showAICoaching(score, sessionDuration)
        },
        { text: 'Back to Hub', onPress: () => navigation.goBack() },
      ]
    );
  };

  const showAICoaching = (score: number, duration: number) => {
    const sessionData = {
      score,
      duration,
      achievements: [], // Could be populated from game data
    };

    navigation.navigate('GameCoach' as never, {
      gameContext: {
        gameId,
        gameTitle,
        currentScore: score,
        difficulty: 'medium', // This could be dynamic
        timeSpent: duration,
        achievements: [],
      },
      sessionData,
    } as never);
  };

  const handleWebViewLoad = () => {
    setLoading(false);
    setError(false);
  };

  const handleWebViewError = () => {
    setLoading(false);
    setError(true);
  };

  const injectedJavaScript = `
    // Inject communication bridge for games
    window.ReactNativeWebView = {
      postMessage: function(data) {
        window.ReactNativeWebView.postMessage(data);
      }
    };
    
    // Override console.log to capture game logs
    const originalLog = console.log;
    console.log = function(...args) {
      originalLog.apply(console, args);
      try {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'GAME_LOG',
          message: args.join(' ')
        }));
      } catch (e) {}
    };
    
    // Inject score tracking helpers
    window.GameHub = {
      updateScore: function(score) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'GAME_SCORE',
          score: score
        }));
      },
      gameCompleted: function(score, duration) {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'GAME_COMPLETED',
          score: score,
          duration: duration
        }));
      },
      navigateBack: function() {
        window.ReactNativeWebView.postMessage(JSON.stringify({
          type: 'NAVIGATE_BACK'
        }));
      }
    };
    
    true; // Required for injected JavaScript
  `;

  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Ionicons name="warning" size={64} color={colors.error} />
        <Text style={styles.errorTitle}>Game Failed to Load</Text>
        <Text style={styles.errorMessage}>
          There was a problem loading the game. Please check your internet connection and try again.
        </Text>
        <TouchableOpacity style={styles.retryButton} onPress={refreshGame}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={colors.primary} />
          <Text style={styles.loadingText}>Loading {gameTitle}...</Text>
        </View>
      )}
      
      <WebView
        ref={webViewRef}
        source={{ uri: gameUrl }}
        style={styles.webView}
        onMessage={handleWebViewMessage}
        onLoad={handleWebViewLoad}
        onError={handleWebViewError}
        injectedJavaScript={injectedJavaScript}
        javaScriptEnabled={true}
        domStorageEnabled={true}
        startInLoadingState={false}
        allowsInlineMediaPlayback={true}
        mediaPlaybackRequiresUserAction={false}
        mixedContentMode="compatibility"
      />
      
      {currentScore > 0 && (
        <View style={styles.scoreOverlay}>
          <Text style={styles.scoreText}>Score: {currentScore}</Text>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  headerControls: {
    flexDirection: 'row',
    marginRight: theme.spacing.md,
  },
  headerButton: {
    marginLeft: theme.spacing.md,
    padding: theme.spacing.xs,
  },
  webView: {
    flex: 1,
  },
  loadingContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: colors.background,
    zIndex: 1000,
  },
  loadingText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    marginTop: theme.spacing.md,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing.xl,
    backgroundColor: colors.background,
  },
  errorTitle: {
    ...theme.typography.textStyles.h2,
    color: colors.text,
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.md,
    textAlign: 'center',
  },
  errorMessage: {
    ...theme.typography.textStyles.body,
    color: colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.xl,
    lineHeight: 24,
  },
  retryButton: {
    backgroundColor: colors.primary,
    paddingHorizontal: theme.spacing.xl,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.lg,
  },
  retryButtonText: {
    ...theme.typography.textStyles.h4,
    color: colors.text,
    fontWeight: '600',
  },
  scoreOverlay: {
    position: 'absolute',
    top: theme.spacing.lg,
    right: theme.spacing.lg,
    backgroundColor: colors.cardOverlay,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    borderRadius: theme.borderRadius.lg,
    ...theme.shadows.md,
  },
  scoreText: {
    ...theme.typography.textStyles.body,
    color: colors.text,
    fontWeight: '600',
  },
});

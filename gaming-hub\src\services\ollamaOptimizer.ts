import { Environment } from '../config/environment';
import axios from 'axios';

export interface OllamaModel {
  name: string;
  size: string;
  digest: string;
  details: {
    format: string;
    family: string;
    families: string[];
    parameter_size: string;
    quantization_level: string;
  };
  modified_at: string;
}

export interface ModelRecommendation {
  model: string;
  reason: string;
  useCase: string;
  performance: 'fast' | 'balanced' | 'quality';
  memoryUsage: 'low' | 'medium' | 'high';
}

export interface OllamaConfig {
  temperature: number;
  top_p: number;
  top_k: number;
  repeat_penalty: number;
  seed?: number;
  num_ctx: number;
  num_predict: number;
}

class OllamaOptimizer {
  private baseUrl: string;
  private availableModels: OllamaModel[] = [];
  private optimalConfigs: Map<string, OllamaConfig> = new Map();

  constructor() {
    this.baseUrl = Environment.OLLAMA.BASE_URL;
    this.initializeOptimalConfigs();
  }

  private initializeOptimalConfigs() {
    // Gaming-optimized configurations for different model types
    this.optimalConfigs.set('mistral', {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      repeat_penalty: 1.1,
      num_ctx: 4096,
      num_predict: 1000,
    });

    this.optimalConfigs.set('llama', {
      temperature: 0.6,
      top_p: 0.85,
      top_k: 35,
      repeat_penalty: 1.15,
      num_ctx: 4096,
      num_predict: 800,
    });

    this.optimalConfigs.set('codellama', {
      temperature: 0.3,
      top_p: 0.8,
      top_k: 30,
      repeat_penalty: 1.2,
      num_ctx: 8192,
      num_predict: 1200,
    });

    this.optimalConfigs.set('neural-chat', {
      temperature: 0.8,
      top_p: 0.95,
      top_k: 50,
      repeat_penalty: 1.05,
      num_ctx: 4096,
      num_predict: 1000,
    });
  }

  async checkOllamaStatus(): Promise<{ running: boolean; version?: string; models?: OllamaModel[] }> {
    try {
      // Check if Ollama is running
      const response = await axios.get(`${this.baseUrl}/api/tags`, { timeout: 5000 });
      
      if (response.status === 200) {
        this.availableModels = response.data.models || [];
        
        // Get version info
        try {
          const versionResponse = await axios.get(`${this.baseUrl}/api/version`, { timeout: 3000 });
          return {
            running: true,
            version: versionResponse.data.version,
            models: this.availableModels,
          };
        } catch {
          return {
            running: true,
            models: this.availableModels,
          };
        }
      }
      
      return { running: false };
    } catch (error) {
      console.warn('Ollama status check failed:', error);
      return { running: false };
    }
  }

  async getRecommendedModels(): Promise<ModelRecommendation[]> {
    const recommendations: ModelRecommendation[] = [];

    // Gaming-specific model recommendations
    const gamingModels = [
      {
        model: 'mistral:7b',
        reason: 'Excellent balance of performance and quality for gaming advice',
        useCase: 'General gaming assistance and strategy',
        performance: 'balanced' as const,
        memoryUsage: 'medium' as const,
      },
      {
        model: 'mistral:7b-instruct',
        reason: 'Optimized for instruction following and coaching',
        useCase: 'Personalized gaming coaching and tips',
        performance: 'balanced' as const,
        memoryUsage: 'medium' as const,
      },
      {
        model: 'neural-chat:7b',
        reason: 'Great conversational abilities for interactive assistance',
        useCase: 'Interactive gaming conversations and support',
        performance: 'fast' as const,
        memoryUsage: 'medium' as const,
      },
      {
        model: 'llama2:7b-chat',
        reason: 'Reliable and well-tested for general assistance',
        useCase: 'Stable gaming advice and general help',
        performance: 'balanced' as const,
        memoryUsage: 'medium' as const,
      },
      {
        model: 'codellama:7b-instruct',
        reason: 'Excellent for game development and technical advice',
        useCase: 'Game development assistance and debugging',
        performance: 'quality' as const,
        memoryUsage: 'high' as const,
      },
      {
        model: 'phi:2.7b',
        reason: 'Lightweight and fast for quick responses',
        useCase: 'Quick tips and fast interactions',
        performance: 'fast' as const,
        memoryUsage: 'low' as const,
      },
    ];

    // Filter recommendations based on available models
    for (const rec of gamingModels) {
      const isAvailable = this.availableModels.some(model => 
        model.name.includes(rec.model.split(':')[0])
      );
      
      if (isAvailable || this.availableModels.length === 0) {
        recommendations.push(rec);
      }
    }

    return recommendations;
  }

  getOptimalConfig(modelName: string, useCase: 'gaming' | 'coaching' | 'technical' | 'casual' = 'gaming'): OllamaConfig {
    // Determine model family
    let modelFamily = 'mistral'; // default
    
    if (modelName.includes('llama')) modelFamily = 'llama';
    else if (modelName.includes('codellama')) modelFamily = 'codellama';
    else if (modelName.includes('neural-chat')) modelFamily = 'neural-chat';
    else if (modelName.includes('mistral')) modelFamily = 'mistral';

    const baseConfig = this.optimalConfigs.get(modelFamily) || this.optimalConfigs.get('mistral')!;

    // Adjust config based on use case
    switch (useCase) {
      case 'coaching':
        return {
          ...baseConfig,
          temperature: Math.min(baseConfig.temperature + 0.1, 0.9),
          top_p: Math.min(baseConfig.top_p + 0.05, 0.95),
          num_predict: Math.min(baseConfig.num_predict + 200, 1500),
        };

      case 'technical':
        return {
          ...baseConfig,
          temperature: Math.max(baseConfig.temperature - 0.2, 0.1),
          top_p: Math.max(baseConfig.top_p - 0.1, 0.7),
          repeat_penalty: Math.min(baseConfig.repeat_penalty + 0.1, 1.3),
          num_predict: Math.min(baseConfig.num_predict + 400, 2000),
        };

      case 'casual':
        return {
          ...baseConfig,
          temperature: Math.min(baseConfig.temperature + 0.2, 1.0),
          top_p: Math.min(baseConfig.top_p + 0.1, 1.0),
          num_predict: Math.max(baseConfig.num_predict - 200, 500),
        };

      case 'gaming':
      default:
        return baseConfig;
    }
  }

  async pullRecommendedModel(modelName: string): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await axios.post(
        `${this.baseUrl}/api/pull`,
        { name: modelName },
        { 
          timeout: 300000, // 5 minutes timeout for model pulling
          headers: { 'Content-Type': 'application/json' }
        }
      );

      return { success: response.status === 200 };
    } catch (error: any) {
      return { 
        success: false, 
        error: error.response?.data?.error || error.message || 'Failed to pull model'
      };
    }
  }

  async optimizeForGaming(): Promise<{
    recommendedModel: string;
    config: OllamaConfig;
    instructions: string[];
  }> {
    const status = await this.checkOllamaStatus();
    
    if (!status.running) {
      return {
        recommendedModel: 'mistral:7b-instruct',
        config: this.getOptimalConfig('mistral:7b-instruct', 'gaming'),
        instructions: [
          'Install Ollama from https://ollama.ai',
          'Run: ollama pull mistral:7b-instruct',
          'Start Ollama service',
          'Restart the Gaming Hub app',
        ],
      };
    }

    // Find the best available model for gaming
    const recommendations = await this.getRecommendedModels();
    const availableRecs = recommendations.filter(rec => 
      status.models?.some(model => model.name.includes(rec.model.split(':')[0]))
    );

    const bestModel = availableRecs.length > 0 
      ? availableRecs[0].model 
      : recommendations[0].model;

    return {
      recommendedModel: bestModel,
      config: this.getOptimalConfig(bestModel, 'gaming'),
      instructions: availableRecs.length > 0 
        ? ['Your Ollama setup is optimized for gaming!']
        : [
            `Run: ollama pull ${bestModel}`,
            'Restart the Gaming Hub app for optimal performance',
          ],
    };
  }

  generateModelComparisonReport(): string {
    return `
# Ollama Model Recommendations for Gaming Hub

## Recommended Models for Gaming Use Cases:

### 🎮 General Gaming Assistant
**mistral:7b-instruct** - Best overall choice
- Excellent instruction following
- Good balance of speed and quality
- Memory usage: ~4GB RAM

### 💬 Interactive Chat
**neural-chat:7b** - Best for conversations
- Natural conversational flow
- Fast response times
- Memory usage: ~4GB RAM

### 🔧 Technical/Development
**codellama:7b-instruct** - Best for technical help
- Superior code understanding
- Detailed technical explanations
- Memory usage: ~5GB RAM

### ⚡ Quick Responses
**phi:2.7b** - Fastest option
- Ultra-fast responses
- Good for simple queries
- Memory usage: ~2GB RAM

## Performance Optimization Tips:

1. **For Low-End Hardware**: Use phi:2.7b or mistral:7b
2. **For Balanced Performance**: Use mistral:7b-instruct
3. **For Best Quality**: Use codellama:7b-instruct or llama2:13b
4. **For Conversations**: Use neural-chat:7b

## Installation Commands:

\`\`\`bash
# Essential models
ollama pull mistral:7b-instruct
ollama pull neural-chat:7b

# Optional for specific use cases
ollama pull codellama:7b-instruct
ollama pull phi:2.7b
\`\`\`

## Memory Requirements:

- **Minimum**: 8GB RAM (for 7b models)
- **Recommended**: 16GB RAM (for smooth operation)
- **Optimal**: 32GB RAM (for multiple models)
`;
  }
}

export const ollamaOptimizer = new OllamaOptimizer();
export default ollamaOptimizer;

import { useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { 
  Video, 
  Eye, 
  Clock, 
  Server, 
  Plus, 
  Search,
  Filter,
  Play,
  Edit,
  Trash2,
  Users,
  Settings,
  Code,
  Bell,
  Radio,
  BarChart3
} from "lucide-react";
import { StreamPlayer } from "@/components/stream-player";
import { Chat } from "@/components/chat";
import { StreamControls } from "@/components/stream-controls";
import { StreamTable } from "@/components/stream-table";
import { AnalyticsDashboard } from "@/components/analytics-dashboard";
import { useWebSocket } from "@/hooks/useWebSocket";
import { Link } from "wouter";

export default function Dashboard() {
  const [selectedStream, setSelectedStream] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  
  const { data: streams, isLoading: streamsLoading } = useQuery({
    queryKey: ['/api/streams'],
  });
  
  const { data: stats } = useQuery({
    queryKey: ['/api/stats'],
    refetchInterval: 30000, // Refetch every 30 seconds
  });
  
  const { isConnected } = useWebSocket('/ws');
  
  const activeStreams = streams?.filter((s: any) => s.status === 'live') || [];
  const featuredStream = activeStreams[0];
  
  useEffect(() => {
    if (featuredStream && !selectedStream) {
      setSelectedStream(featuredStream.id);
    }
  }, [featuredStream, selectedStream]);
  
  return (
    <div className="min-h-screen flex bg-background">
      {/* Sidebar */}
      <nav className="w-64 bg-card border-r border-border flex flex-col">
        <div className="p-6 border-b border-border">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-primary rounded-lg flex items-center justify-center">
              <Video className="w-5 h-5 text-primary-foreground" />
            </div>
            <div>
              <h1 className="text-xl font-bold">StreamEngine</h1>
              <p className="text-sm text-muted-foreground">Pro</p>
            </div>
          </div>
        </div>
        
        <div className="flex-1 py-4">
          <div className="px-4 mb-4">
            <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Main</p>
          </div>
          <ul className="space-y-1 px-2">
            <li>
              <Link href="/" className="flex items-center px-3 py-2 text-sm font-medium rounded-lg bg-primary text-primary-foreground">
                <BarChart3 className="w-5 h-5 mr-3" />
                Dashboard
              </Link>
            </li>
            <li>
              <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                <Radio className="w-5 h-5 mr-3" />
                Live Streams
              </a>
            </li>
            <li>
              <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                <Play className="w-5 h-5 mr-3" />
                Recordings
              </a>
            </li>
            <li>
              <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                <BarChart3 className="w-5 h-5 mr-3" />
                Analytics
              </a>
            </li>
          </ul>
          
          <div className="px-4 mt-8 mb-4">
            <p className="text-xs font-medium text-muted-foreground uppercase tracking-wider">Management</p>
          </div>
          <ul className="space-y-1 px-2">
            <li>
              <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                <Users className="w-5 h-5 mr-3" />
                Users & Access
              </a>
            </li>
            <li>
              <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                <Settings className="w-5 h-5 mr-3" />
                Settings
              </a>
            </li>
            <li>
              <a href="#" className="flex items-center px-3 py-2 text-sm font-medium rounded-lg text-muted-foreground hover:bg-accent hover:text-accent-foreground">
                <Code className="w-5 h-5 mr-3" />
                API Docs
              </a>
            </li>
          </ul>
        </div>
        
        <div className="p-4 border-t border-border">
          <div className="flex items-center space-x-3 p-3 rounded-lg bg-accent">
            <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
              <Users className="w-4 h-4 text-muted-foreground" />
            </div>
            <div className="flex-1">
              <p className="text-sm font-medium">Admin User</p>
              <p className="text-xs text-muted-foreground">System Administrator</p>
            </div>
          </div>
        </div>
      </nav>
      
      {/* Main Content */}
      <main className="flex-1 overflow-auto">
        {/* Header */}
        <header className="bg-card border-b border-border px-8 py-4 flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Live Streaming Dashboard</h2>
            <p className="text-muted-foreground">Monitor and manage your enterprise streaming infrastructure</p>
          </div>
          <div className="flex items-center space-x-4">
            <Button className="bg-primary hover:bg-primary/90">
              <Plus className="w-4 h-4 mr-2" />
              New Stream
            </Button>
            <Button variant="outline" size="icon">
              <Bell className="w-4 h-4" />
            </Button>
          </div>
        </header>
        
        <div className="p-8">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-10 h-10 bg-green-500/20 rounded-lg flex items-center justify-center">
                    <Radio className="w-5 h-5 text-green-500" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground uppercase">Active Streams</span>
                </div>
                <div className="text-2xl font-bold mb-1">{stats?.activeStreams || 0}</div>
                <div className="text-sm text-muted-foreground">
                  <span className="text-green-500">+2</span> from last hour
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-10 h-10 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Eye className="w-5 h-5 text-blue-500" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground uppercase">Total Viewers</span>
                </div>
                <div className="text-2xl font-bold mb-1">{stats?.totalViewers?.toLocaleString() || 0}</div>
                <div className="text-sm text-muted-foreground">
                  <span className="text-green-500">+12%</span> from yesterday
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-10 h-10 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                    <Clock className="w-5 h-5 text-yellow-500" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground uppercase">Avg Latency</span>
                </div>
                <div className="text-2xl font-bold mb-1">{stats?.avgLatency || '2.3s'}</div>
                <div className="text-sm text-muted-foreground">
                  <span className="text-green-500">-0.2s</span> improvement
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <div className="w-10 h-10 bg-red-500/20 rounded-lg flex items-center justify-center">
                    <Server className="w-5 h-5 text-red-500" />
                  </div>
                  <span className="text-xs font-medium text-muted-foreground uppercase">Server Load</span>
                </div>
                <div className="text-2xl font-bold mb-1">{stats?.serverLoad || '67%'}</div>
                <div className="text-sm text-muted-foreground">
                  <span className="text-muted-foreground">Normal range</span>
                </div>
              </CardContent>
            </Card>
          </div>
          
          {/* Main Content Area */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Stream Player */}
            <div className="lg:col-span-2">
              {featuredStream ? (
                <StreamPlayer 
                  stream={featuredStream}
                  className="mb-6"
                />
              ) : (
                <Card className="mb-6">
                  <CardContent className="p-8 text-center">
                    <Video className="w-16 h-16 mx-auto mb-4 text-muted-foreground" />
                    <h3 className="text-lg font-semibold mb-2">No Active Streams</h3>
                    <p className="text-muted-foreground mb-4">Start streaming to see your content here</p>
                    <Button>
                      <Plus className="w-4 h-4 mr-2" />
                      Create Stream
                    </Button>
                  </CardContent>
                </Card>
              )}
              
              {selectedStream && (
                <StreamControls streamId={selectedStream} />
              )}
            </div>
            
            {/* Chat and Quick Actions */}
            <div className="space-y-6">
              {selectedStream && (
                <Chat streamId={selectedStream} />
              )}
              
              <Card>
                <CardHeader>
                  <CardTitle>Quick Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-3">
                  <Button className="w-full" size="lg">
                    <Video className="w-4 h-4 mr-2" />
                    Start New Stream
                  </Button>
                  <Button variant="outline" className="w-full" size="lg">
                    <Play className="w-4 h-4 mr-2" />
                    Upload Recording
                  </Button>
                  <Button variant="outline" className="w-full" size="lg">
                    <BarChart3 className="w-4 h-4 mr-2" />
                    Export Analytics
                  </Button>
                </CardContent>
              </Card>
            </div>
          </div>
          
          {/* Streams Table */}
          <div className="mt-8">
            <StreamTable 
              streams={streams || []} 
              isLoading={streamsLoading}
              onStreamSelect={setSelectedStream}
            />
          </div>
          
          {/* Analytics */}
          <div className="mt-8">
            <AnalyticsDashboard />
          </div>
        </div>
      </main>
    </div>
  );
}

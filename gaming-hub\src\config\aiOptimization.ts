/**
 * AI Optimization Configuration
 * Fine-tuned settings for optimal gaming AI performance
 */

export interface AIOptimizationConfig {
  provider: string;
  model: string;
  parameters: {
    temperature: number;
    top_p: number;
    top_k: number;
    repeat_penalty: number;
    num_ctx: number;
    num_predict: number;
    stop?: string[];
  };
  systemPrompt: string;
  useCase: string[];
  priority: number;
}

export const GAMING_AI_CONFIGS: AIOptimizationConfig[] = [
  {
    provider: 'ollama',
    model: 'mistral:7b-instruct',
    parameters: {
      temperature: 0.7,
      top_p: 0.9,
      top_k: 40,
      repeat_penalty: 1.1,
      num_ctx: 4096,
      num_predict: 1000,
      stop: ['Human:', 'User:', '\n\n---'],
    },
    systemPrompt: `You are an expert gaming assistant and coach with deep knowledge of:
- Game mechanics and strategies across all genres
- Player skill development and improvement techniques
- Competitive gaming tactics and meta-game analysis
- Game design principles and best practices
- Performance optimization and troubleshooting

Provide direct, actionable advice without filler words. Be encouraging but honest about areas for improvement. Adapt your language to the user's skill level.`,
    useCase: ['general_gaming', 'strategy_advice', 'skill_coaching'],
    priority: 1,
  },
  {
    provider: 'ollama',
    model: 'deepseek-coder:6.7b-instruct',
    parameters: {
      temperature: 0.3,
      top_p: 0.8,
      top_k: 30,
      repeat_penalty: 1.2,
      num_ctx: 8192,
      num_predict: 1500,
      stop: ['```\n\n', 'Human:', 'User:'],
    },
    systemPrompt: `You are an expert game developer and software engineer specializing in:
- Game programming in JavaScript, TypeScript, HTML5, and modern frameworks
- Game engine development (Phaser.js, React, Unity concepts)
- Performance optimization and debugging
- Code architecture and best practices
- Game mechanics implementation

Provide clean, efficient code examples with clear explanations. Focus on game development patterns and optimization techniques.`,
    useCase: ['game_development', 'code_assistance', 'debugging', 'optimization'],
    priority: 2,
  },
  {
    provider: 'deepseek',
    model: 'deepseek-chat',
    parameters: {
      temperature: 0.4,
      top_p: 0.85,
      top_k: 35,
      repeat_penalty: 1.15,
      num_ctx: 4096,
      num_predict: 1200,
    },
    systemPrompt: `You are a professional game development consultant with expertise in:
- Advanced programming concepts and algorithms
- Game engine architecture and optimization
- Cross-platform development strategies
- Industry best practices and design patterns
- Technical problem-solving and debugging

Provide detailed technical guidance with practical examples. Focus on scalable solutions and professional development practices.`,
    useCase: ['advanced_development', 'technical_consulting', 'architecture'],
    priority: 3,
  },
  {
    provider: 'openrouter',
    model: 'mistralai/mistral-7b-instruct',
    parameters: {
      temperature: 0.6,
      top_p: 0.9,
      top_k: 40,
      repeat_penalty: 1.1,
      num_ctx: 4096,
      num_predict: 1000,
    },
    systemPrompt: `You are a versatile gaming expert capable of helping with:
- Gaming strategies and tactics
- Player improvement and skill development
- Game recommendations and reviews
- Community building and social gaming
- Esports and competitive gaming

Provide balanced, well-researched advice that considers different playstyles and preferences.`,
    useCase: ['general_assistance', 'recommendations', 'community'],
    priority: 4,
  },
  {
    provider: 'anthropic',
    model: 'claude-3-haiku-20240307',
    parameters: {
      temperature: 0.5,
      top_p: 0.9,
      top_k: 40,
      repeat_penalty: 1.0,
      num_ctx: 4096,
      num_predict: 1000,
    },
    systemPrompt: `You are a thoughtful gaming mentor focused on:
- Holistic player development and well-being
- Balanced gaming habits and healthy practices
- Creative problem-solving and innovation
- Educational aspects of gaming
- Inclusive and supportive gaming communities

Provide thoughtful, nuanced advice that considers the broader impact of gaming on players' lives.`,
    useCase: ['mentoring', 'wellness', 'education', 'creativity'],
    priority: 5,
  },
];

export interface VoiceOptimizationConfig {
  voiceId: string;
  name: string;
  description: string;
  useCase: string[];
  settings: {
    stability: number;
    similarity_boost: number;
    style?: number;
    use_speaker_boost?: boolean;
  };
}

export const VOICE_CONFIGS: VoiceOptimizationConfig[] = [
  {
    voiceId: 'EXAVITQu4vr4xnSDxMaL', // Bella - Young, friendly
    name: 'Gaming Assistant',
    description: 'Friendly, enthusiastic voice for general gaming assistance',
    useCase: ['general_gaming', 'tips', 'encouragement'],
    settings: {
      stability: 0.75,
      similarity_boost: 0.8,
      style: 0.2,
      use_speaker_boost: true,
    },
  },
  {
    voiceId: 'TxGEqnHWrfWFTfGW9XjX', // Josh - Professional, clear
    name: 'Game Coach',
    description: 'Professional, authoritative voice for coaching and analysis',
    useCase: ['coaching', 'analysis', 'strategy'],
    settings: {
      stability: 0.8,
      similarity_boost: 0.75,
      style: 0.1,
      use_speaker_boost: false,
    },
  },
  {
    voiceId: 'pNInz6obpgDQGcFmaJgB', // Adam - Calm, informative
    name: 'Technical Helper',
    description: 'Clear, technical voice for development and coding assistance',
    useCase: ['development', 'technical', 'tutorials'],
    settings: {
      stability: 0.85,
      similarity_boost: 0.7,
      style: 0.0,
      use_speaker_boost: false,
    },
  },
  {
    voiceId: 'onwK4e9ZLuTAKqWW03F9', // Daniel - Storytelling
    name: 'Game Narrator',
    description: 'Engaging voice for game descriptions and storytelling',
    useCase: ['narration', 'descriptions', 'storytelling'],
    settings: {
      stability: 0.7,
      similarity_boost: 0.85,
      style: 0.3,
      use_speaker_boost: true,
    },
  },
];

export class AIOptimizationService {
  static getOptimalConfig(useCase: string, provider?: string): AIOptimizationConfig {
    // Filter configs by use case
    const suitableConfigs = GAMING_AI_CONFIGS.filter(config => 
      config.useCase.includes(useCase)
    );

    // If provider specified, prefer that provider
    if (provider) {
      const providerConfig = suitableConfigs.find(config => config.provider === provider);
      if (providerConfig) return providerConfig;
    }

    // Return highest priority suitable config
    return suitableConfigs.sort((a, b) => a.priority - b.priority)[0] || GAMING_AI_CONFIGS[0];
  }

  static getVoiceConfig(useCase: string): VoiceOptimizationConfig {
    const suitableVoices = VOICE_CONFIGS.filter(voice => 
      voice.useCase.includes(useCase)
    );

    return suitableVoices[0] || VOICE_CONFIGS[0];
  }

  static optimizeForSkillLevel(config: AIOptimizationConfig, skillLevel: string): AIOptimizationConfig {
    const optimized = { ...config };

    switch (skillLevel) {
      case 'beginner':
        optimized.parameters.temperature = Math.min(config.parameters.temperature + 0.1, 0.9);
        optimized.parameters.num_predict = Math.max(config.parameters.num_predict - 200, 600);
        optimized.systemPrompt += '\n\nUse simple language and provide step-by-step explanations for beginners.';
        break;

      case 'intermediate':
        // Use default config
        break;

      case 'advanced':
        optimized.parameters.temperature = Math.max(config.parameters.temperature - 0.1, 0.3);
        optimized.parameters.num_predict = Math.min(config.parameters.num_predict + 200, 1500);
        optimized.systemPrompt += '\n\nProvide advanced strategies and assume familiarity with gaming concepts.';
        break;

      case 'expert':
        optimized.parameters.temperature = Math.max(config.parameters.temperature - 0.2, 0.2);
        optimized.parameters.num_predict = Math.min(config.parameters.num_predict + 400, 2000);
        optimized.systemPrompt += '\n\nFocus on cutting-edge techniques, meta-game analysis, and innovation.';
        break;
    }

    return optimized;
  }

  static getPerformanceMetrics() {
    return {
      targetResponseTime: {
        ollama: 3000, // 3 seconds
        cloud: 5000,  // 5 seconds
      },
      maxRetries: 3,
      timeoutMs: 30000,
      fallbackDelay: 1000,
    };
  }

  static generateContextualPrompt(
    basePrompt: string, 
    gameContext?: any, 
    userHistory?: string[]
  ): string {
    let enhancedPrompt = basePrompt;

    if (gameContext) {
      enhancedPrompt += `\n\nCurrent Game Context:
- Game: ${gameContext.gameTitle || 'Unknown'}
- Score: ${gameContext.currentScore || 0}
- Difficulty: ${gameContext.difficulty || 'Normal'}
- Time Played: ${gameContext.timeSpent || 0} minutes`;
    }

    if (userHistory && userHistory.length > 0) {
      enhancedPrompt += `\n\nRecent Interaction Context:
${userHistory.slice(-3).join('\n')}`;
    }

    enhancedPrompt += `\n\nProvide specific, actionable advice that directly addresses the user's current situation.`;

    return enhancedPrompt;
  }
}

export default AIOptimizationService;
